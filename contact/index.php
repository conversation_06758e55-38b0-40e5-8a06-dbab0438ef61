<?php
/**
 * Contact Us - 14K Jewelry & Coin Exchange
 * Contact information and inquiry form
 */

// Include configuration and core files
require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../includes/seo.php';

// Handle form submission
$form_submitted = false;
$form_success = false;
$form_errors = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['contact_form'])) {
    $form_submitted = true;
    
    // Validate form data
    $name = trim($_POST['name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $subject = trim($_POST['subject'] ?? '');
    $message = trim($_POST['message'] ?? '');
    
    // Basic validation
    if (empty($name)) {
        $form_errors[] = 'Name is required';
    }
    if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $form_errors[] = 'Valid email is required';
    }
    if (empty($message)) {
        $form_errors[] = 'Message is required';
    }
    
    // If no errors, process the form
    if (empty($form_errors)) {
        // In a real application, you would:
        // 1. Save to database
        // 2. Send email notification
        // 3. Send auto-reply to customer
        
        // For now, we'll just set success flag
        $form_success = true;
        
        // Clear form data on success
        $name = $email = $phone = $subject = $message = '';
    }
}

// Page-specific SEO data
$page_data = [
    'title' => 'Contact Us | 14K Jewelry & Coin Exchange | Southgate, Michigan',
    'description' => 'Contact 14K Jewelry & Coin Exchange in Southgate, MI. Get directions, hours, phone number, and send us a message. We\'re here to help with all your precious metals needs.',
    'keywords' => 'contact 14k exchange, Southgate Michigan, jewelry store location, precious metals dealer contact, directions',
    'canonical' => BASE_URL . '/contact/',
    'og_type' => 'website',
    'schema_type' => 'ContactPage'
];

// Generate SEO meta tags
$seo = new SEOManager($page_data);

// Breadcrumb data
$breadcrumbs = [
    ['name' => 'Home', 'url' => BASE_URL],
    ['name' => 'Contact Us', 'url' => BASE_URL . '/contact/']
];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <?php echo $seo->generateMetaTags(); ?>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="../assets/css/contact.css">

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Open+Sans:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- Header -->
    <?php include '../includes/header.php'; ?>

    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="breadcrumb-nav">
        <div class="container">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="/">Home</a></li>
                <li class="breadcrumb-item active" aria-current="page">Contact Us</li>
            </ol>
        </div>
    </nav>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h1 class="page-title">Contact Us</h1>
                    <p class="page-subtitle">Get in touch with our precious metals experts</p>
                    <p class="page-description">Visit our store, give us a call, or send us a message. We're here to help with all your jewelry, coin, and precious metals needs.</p>
                </div>
                <div class="col-lg-6">
                    <img src="../assets/images/contact-hero.jpg" alt="14K Exchange contact" class="img-fluid rounded shadow">
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Information -->
    <section class="contact-info py-5">
        <div class="container">
            <div class="row g-4">
                <div class="col-md-6 col-lg-3">
                    <div class="contact-card text-center">
                        <div class="contact-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <h4>Visit Our Store</h4>
                        <p><?php echo BUSINESS_ADDRESS; ?></p>
                        <a href="#map" class="btn btn-outline-primary">Get Directions</a>
                    </div>
                </div>
                <div class="col-md-6 col-lg-3">
                    <div class="contact-card text-center">
                        <div class="contact-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <h4>Call Us</h4>
                        <p><a href="tel:<?php echo BUSINESS_PHONE; ?>"><?php echo BUSINESS_PHONE; ?></a></p>
                        <p class="text-muted">Free appraisal consultation</p>
                    </div>
                </div>
                <div class="col-md-6 col-lg-3">
                    <div class="contact-card text-center">
                        <div class="contact-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <h4>Email Us</h4>
                        <p><a href="mailto:<?php echo BUSINESS_EMAIL; ?>"><?php echo BUSINESS_EMAIL; ?></a></p>
                        <p class="text-muted">We respond within 24 hours</p>
                    </div>
                </div>
                <div class="col-md-6 col-lg-3">
                    <div class="contact-card text-center">
                        <div class="contact-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <h4>Store Hours</h4>
                        <p><?php echo BUSINESS_HOURS; ?></p>
                        <p class="text-muted">Walk-ins welcome</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Form -->
    <section class="contact-form py-5 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <div class="form-container">
                        <h2 class="text-center mb-4">Send Us a Message</h2>

                        <?php if ($form_submitted && $form_success): ?>
                            <div class="alert alert-success" role="alert">
                                <i class="fas fa-check-circle me-2"></i>
                                Thank you for your message! We'll get back to you within 24 hours.
                            </div>
                        <?php endif; ?>

                        <?php if ($form_submitted && !empty($form_errors)): ?>
                            <div class="alert alert-danger" role="alert">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Please correct the following errors:
                                <ul class="mb-0 mt-2">
                                    <?php foreach ($form_errors as $error): ?>
                                        <li><?php echo htmlspecialchars($error); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>

                        <form method="POST" action="" class="contact-form-fields">
                            <input type="hidden" name="contact_form" value="1">

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label">Full Name *</label>
                                    <input type="text" class="form-control" id="name" name="name"
                                           value="<?php echo htmlspecialchars($name ?? ''); ?>" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">Email Address *</label>
                                    <input type="email" class="form-control" id="email" name="email"
                                           value="<?php echo htmlspecialchars($email ?? ''); ?>" required>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label">Phone Number</label>
                                    <input type="tel" class="form-control" id="phone" name="phone"
                                           value="<?php echo htmlspecialchars($phone ?? ''); ?>">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="subject" class="form-label">Subject</label>
                                    <select class="form-select" id="subject" name="subject">
                                        <option value="">Select a topic</option>
                                        <option value="appraisal" <?php echo ($subject ?? '') === 'appraisal' ? 'selected' : ''; ?>>Jewelry/Coin Appraisal</option>
                                        <option value="selling" <?php echo ($subject ?? '') === 'selling' ? 'selected' : ''; ?>>Selling Items</option>
                                        <option value="buying" <?php echo ($subject ?? '') === 'buying' ? 'selected' : ''; ?>>Buying Items</option>
                                        <option value="general" <?php echo ($subject ?? '') === 'general' ? 'selected' : ''; ?>>General Inquiry</option>
                                    </select>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="message" class="form-label">Message *</label>
                                <textarea class="form-control" id="message" name="message" rows="5"
                                          placeholder="Tell us about your items or how we can help you..." required><?php echo htmlspecialchars($message ?? ''); ?></textarea>
                            </div>

                            <div class="text-center">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-paper-plane me-2"></i>Send Message
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Map Section -->
    <section class="map-section" id="map">
        <div class="container-fluid p-0">
            <div class="row g-0">
                <div class="col-12">
                    <div class="map-placeholder">
                        <div class="map-overlay">
                            <div class="map-info">
                                <h3>Visit Our Store</h3>
                                <p><?php echo BUSINESS_ADDRESS; ?></p>
                                <p><?php echo BUSINESS_HOURS; ?></p>
                                <a href="https://maps.google.com/?q=<?php echo urlencode(BUSINESS_ADDRESS); ?>"
                                   target="_blank" class="btn btn-primary">
                                    <i class="fas fa-directions me-2"></i>Get Directions
                                </a>
                            </div>
                        </div>
                        <!-- In a real implementation, you would embed Google Maps here -->
                        <div class="map-iframe-placeholder">
                            <i class="fas fa-map-marked-alt"></i>
                            <p>Interactive Map Coming Soon</p>
                            <small>Click "Get Directions" above to view in Google Maps</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <?php include '../includes/footer.php'; ?>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="../assets/js/main.js"></script>

    <?php echo $seo->generateStructuredData(); ?>
    <?php echo $seo->generateBreadcrumbs($breadcrumbs); ?>
</body>
</html>
