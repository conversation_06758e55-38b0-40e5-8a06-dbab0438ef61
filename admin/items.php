<?php
/**
 * Admin Items Management - 14K Jewelry & Coin Exchange
 * Manage inventory items
 */

require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../includes/csrf.php';

// Start session and check authentication
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: /admin/');
    exit;
}

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $message = 'Security token validation failed.';
        $messageType = 'danger';
    } else {
        $action = $_POST['action'] ?? '';
        
        if ($action === 'add_item' && $pdo) {
            try {
                $stmt = $pdo->prepare("
                    INSERT INTO items (category_id, name, description, purchase_price, selling_price, weight, purity, condition_notes, status, created_at) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'available', NOW())
                ");
                $stmt->execute([
                    $_POST['category_id'] ?: 1,
                    sanitizeInput($_POST['name']),
                    sanitizeInput($_POST['description']),
                    floatval($_POST['purchase_price']),
                    floatval($_POST['selling_price']),
                    floatval($_POST['weight']),
                    floatval($_POST['purity']),
                    sanitizeInput($_POST['condition_notes']),
                ]);
                $message = 'Item added successfully!';
                $messageType = 'success';
            } catch (PDOException $e) {
                $message = 'Error adding item: ' . $e->getMessage();
                $messageType = 'danger';
            }
        } elseif ($action === 'delete_item' && $pdo) {
            try {
                $stmt = $pdo->prepare("DELETE FROM items WHERE id = ?");
                $stmt->execute([intval($_POST['item_id'])]);
                $message = 'Item deleted successfully!';
                $messageType = 'success';
            } catch (PDOException $e) {
                $message = 'Error deleting item: ' . $e->getMessage();
                $messageType = 'danger';
            }
        }
    }
}

// Fetch items and categories
$items = [];
$categories = [];

if ($pdo) {
    try {
        // Get categories
        $stmt = $pdo->query("SELECT id, name FROM categories ORDER BY name");
        $categories = $stmt->fetchAll();
        
        // Get items with category names
        $stmt = $pdo->query("
            SELECT i.*, c.name as category_name 
            FROM items i 
            LEFT JOIN categories c ON i.category_id = c.id 
            ORDER BY i.created_at DESC 
            LIMIT 50
        ");
        $items = $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Error fetching items: " . $e->getMessage());
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Items Management - Admin - 14K Exchange</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .sidebar { background: #2c3e50; min-height: 100vh; }
        .sidebar .nav-link { color: #bdc3c7; }
        .sidebar .nav-link:hover, .sidebar .nav-link.active { color: white; background: #34495e; }
        .main-content { background: #f8f9fa; min-height: 100vh; }
        .item-card { background: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 1rem; }
        .status-available { color: #28a745; }
        .status-sold { color: #dc3545; }
        .status-pending { color: #ffc107; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar p-0">
                <div class="p-3">
                    <h4 class="text-white">14K Exchange</h4>
                    <small class="text-muted">Admin Panel</small>
                </div>
                <nav class="nav flex-column">
                    <a class="nav-link" href="/admin/"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a>
                    <a class="nav-link active" href="/admin/items.php"><i class="fas fa-gem me-2"></i>Items</a>
                    <a class="nav-link" href="/admin/customers.php"><i class="fas fa-users me-2"></i>Customers</a>
                    <a class="nav-link" href="/admin/transactions.php"><i class="fas fa-receipt me-2"></i>Transactions</a>
                    <a class="nav-link" href="/admin/inquiries.php"><i class="fas fa-envelope me-2"></i>Inquiries</a>
                    <a class="nav-link" href="/admin/settings.php"><i class="fas fa-cog me-2"></i>Settings</a>
                    <hr class="text-muted">
                    <a class="nav-link" href="/" target="_blank"><i class="fas fa-external-link-alt me-2"></i>View Site</a>
                    <a class="nav-link" href="?logout=1"><i class="fas fa-sign-out-alt me-2"></i>Logout</a>
                </nav>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1>Items Management</h1>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addItemModal">
                        <i class="fas fa-plus me-2"></i>Add New Item
                    </button>
                </div>
                
                <?php if ($message): ?>
                    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                        <?php echo htmlspecialchars($message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <!-- Items List -->
                <div class="row">
                    <?php if (empty($items)): ?>
                        <div class="col-12 text-center py-5">
                            <i class="fas fa-gem fa-3x text-muted mb-3"></i>
                            <h3 class="text-muted">No Items Yet</h3>
                            <p class="text-muted">Add your first inventory item to get started.</p>
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addItemModal">
                                <i class="fas fa-plus me-2"></i>Add First Item
                            </button>
                        </div>
                    <?php else: ?>
                        <?php foreach ($items as $item): ?>
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="item-card p-3">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h5 class="mb-1"><?php echo htmlspecialchars($item['name']); ?></h5>
                                        <span class="badge bg-secondary"><?php echo htmlspecialchars($item['category_name'] ?? 'Uncategorized'); ?></span>
                                    </div>
                                    
                                    <p class="text-muted small mb-2"><?php echo htmlspecialchars($item['description']); ?></p>
                                    
                                    <div class="row text-center mb-2">
                                        <div class="col-6">
                                            <small class="text-muted">Purchase</small>
                                            <div class="fw-bold">$<?php echo number_format($item['purchase_price'], 2); ?></div>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">Selling</small>
                                            <div class="fw-bold text-success">$<?php echo number_format($item['selling_price'], 2); ?></div>
                                        </div>
                                    </div>
                                    
                                    <?php if ($item['weight']): ?>
                                        <div class="text-center mb-2">
                                            <small class="text-muted">Weight: <?php echo $item['weight']; ?>g</small>
                                            <?php if ($item['purity']): ?>
                                                <small class="text-muted">| Purity: <?php echo $item['purity']; ?>%</small>
                                            <?php endif; ?>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="text-center">
                                        <span class="badge bg-<?php echo $item['status'] === 'available' ? 'success' : ($item['status'] === 'sold' ? 'danger' : 'warning'); ?>">
                                            <?php echo ucfirst($item['status']); ?>
                                        </span>
                                    </div>
                                    
                                    <div class="mt-3 text-center">
                                        <button class="btn btn-sm btn-outline-primary me-1" onclick="editItem(<?php echo $item['id']; ?>)">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger" onclick="deleteItem(<?php echo $item['id']; ?>, '<?php echo htmlspecialchars($item['name']); ?>')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Item Modal -->
    <div class="modal fade" id="addItemModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add New Item</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add_item">
                        <?php echo csrfTokenField(); ?>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">Item Name *</label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="category_id" class="form-label">Category</label>
                                <select class="form-select" id="category_id" name="category_id">
                                    <option value="">Select Category</option>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?php echo $category['id']; ?>"><?php echo htmlspecialchars($category['name']); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="purchase_price" class="form-label">Purchase Price</label>
                                <input type="number" class="form-control" id="purchase_price" name="purchase_price" step="0.01" min="0">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="selling_price" class="form-label">Selling Price</label>
                                <input type="number" class="form-control" id="selling_price" name="selling_price" step="0.01" min="0">
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="weight" class="form-label">Weight (grams)</label>
                                <input type="number" class="form-control" id="weight" name="weight" step="0.1" min="0">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="purity" class="form-label">Purity (%)</label>
                                <input type="number" class="form-control" id="purity" name="purity" step="0.1" min="0" max="100">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="condition_notes" class="form-label">Condition Notes</label>
                            <textarea class="form-control" id="condition_notes" name="condition_notes" rows="2"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Add Item</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Form (hidden) -->
    <form id="deleteForm" method="POST" style="display: none;">
        <input type="hidden" name="action" value="delete_item">
        <input type="hidden" name="item_id" id="deleteItemId">
        <?php echo csrfTokenField(); ?>
    </form>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function editItem(id) {
            alert('Edit functionality coming soon! Item ID: ' + id);
        }
        
        function deleteItem(id, name) {
            if (confirm('Are you sure you want to delete "' + name + '"?')) {
                document.getElementById('deleteItemId').value = id;
                document.getElementById('deleteForm').submit();
            }
        }
    </script>
</body>
</html>
