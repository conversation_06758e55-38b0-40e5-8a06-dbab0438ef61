<?php
/**
 * Admin Settings - 14K Jewelry & Coin Exchange
 * System settings and configuration
 */

require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../includes/csrf.php';
require_once '../includes/cache.php';

// Start session and check authentication
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: /admin/');
    exit;
}

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $message = 'Security token validation failed.';
        $messageType = 'danger';
    } else {
        $action = $_POST['action'] ?? '';
        
        if ($action === 'clear_cache') {
            $cache = getCache();
            $cleared = $cache->cleanup();
            $message = "Cache cleared! Removed $cleared expired files.";
            $messageType = 'success';
        } elseif ($action === 'update_prices' && $pdo) {
            try {
                $stmt = $pdo->prepare("
                    INSERT INTO metal_prices (metal_type, price_per_ounce, updated_at) 
                    VALUES (?, ?, NOW()) 
                    ON DUPLICATE KEY UPDATE price_per_ounce = VALUES(price_per_ounce), updated_at = NOW()
                ");
                
                $metals = ['gold', 'silver', 'platinum'];
                foreach ($metals as $metal) {
                    if (isset($_POST[$metal . '_price']) && is_numeric($_POST[$metal . '_price'])) {
                        $stmt->execute([$metal, floatval($_POST[$metal . '_price'])]);
                    }
                }
                
                $message = 'Metal prices updated successfully!';
                $messageType = 'success';
            } catch (PDOException $e) {
                $message = 'Error updating prices: ' . $e->getMessage();
                $messageType = 'danger';
            }
        }
    }
}

// Get current metal prices
$metalPrices = [];
if ($pdo) {
    try {
        $stmt = $pdo->query("SELECT metal_type, price_per_ounce, updated_at FROM metal_prices ORDER BY updated_at DESC");
        $prices = $stmt->fetchAll();
        foreach ($prices as $price) {
            $metalPrices[$price['metal_type']] = $price;
        }
    } catch (PDOException $e) {
        error_log("Error fetching metal prices: " . $e->getMessage());
    }
}

// Get cache statistics
$cacheStats = getCache()->getStats();

// Get system information
$systemInfo = [
    'php_version' => PHP_VERSION,
    'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
    'database_connected' => $pdo ? 'Yes' : 'No',
    'environment' => ENVIRONMENT,
    'base_url' => BASE_URL,
    'business_name' => BUSINESS_NAME,
    'business_phone' => BUSINESS_PHONE,
    'business_email' => BUSINESS_EMAIL,
];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - Admin - 14K Exchange</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .sidebar { background: #2c3e50; min-height: 100vh; }
        .sidebar .nav-link { color: #bdc3c7; }
        .sidebar .nav-link:hover, .sidebar .nav-link.active { color: white; background: #34495e; }
        .main-content { background: #f8f9fa; min-height: 100vh; }
        .settings-card { background: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 2rem; }
        .settings-header { background: #f8f9fa; border-radius: 10px 10px 0 0; padding: 1rem; border-bottom: 1px solid #dee2e6; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar p-0">
                <div class="p-3">
                    <h4 class="text-white">14K Exchange</h4>
                    <small class="text-muted">Admin Panel</small>
                </div>
                <nav class="nav flex-column">
                    <a class="nav-link" href="/admin/"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a>
                    <a class="nav-link" href="/admin/items.php"><i class="fas fa-gem me-2"></i>Items</a>
                    <a class="nav-link" href="/admin/customers.php"><i class="fas fa-users me-2"></i>Customers</a>
                    <a class="nav-link" href="/admin/transactions.php"><i class="fas fa-receipt me-2"></i>Transactions</a>
                    <a class="nav-link" href="/admin/inquiries.php"><i class="fas fa-envelope me-2"></i>Inquiries</a>
                    <a class="nav-link active" href="/admin/settings.php"><i class="fas fa-cog me-2"></i>Settings</a>
                    <hr class="text-muted">
                    <a class="nav-link" href="/" target="_blank"><i class="fas fa-external-link-alt me-2"></i>View Site</a>
                    <a class="nav-link" href="?logout=1"><i class="fas fa-sign-out-alt me-2"></i>Logout</a>
                </nav>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content p-4">
                <h1 class="mb-4">Settings</h1>
                
                <?php if ($message): ?>
                    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                        <?php echo htmlspecialchars($message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <!-- Metal Prices -->
                <div class="settings-card">
                    <div class="settings-header">
                        <h5 class="mb-0"><i class="fas fa-coins me-2"></i>Metal Prices</h5>
                    </div>
                    <div class="p-4">
                        <form method="POST">
                            <input type="hidden" name="action" value="update_prices">
                            <?php echo csrfTokenField(); ?>
                            
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="gold_price" class="form-label">Gold (per ounce)</label>
                                    <div class="input-group">
                                        <span class="input-group-text">$</span>
                                        <input type="number" class="form-control" id="gold_price" name="gold_price" 
                                               value="<?php echo $metalPrices['gold']['price_per_ounce'] ?? '2000.00'; ?>" 
                                               step="0.01" min="0">
                                    </div>
                                    <?php if (isset($metalPrices['gold'])): ?>
                                        <small class="text-muted">Last updated: <?php echo date('M j, Y g:i A', strtotime($metalPrices['gold']['updated_at'])); ?></small>
                                    <?php endif; ?>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="silver_price" class="form-label">Silver (per ounce)</label>
                                    <div class="input-group">
                                        <span class="input-group-text">$</span>
                                        <input type="number" class="form-control" id="silver_price" name="silver_price" 
                                               value="<?php echo $metalPrices['silver']['price_per_ounce'] ?? '25.00'; ?>" 
                                               step="0.01" min="0">
                                    </div>
                                    <?php if (isset($metalPrices['silver'])): ?>
                                        <small class="text-muted">Last updated: <?php echo date('M j, Y g:i A', strtotime($metalPrices['silver']['updated_at'])); ?></small>
                                    <?php endif; ?>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="platinum_price" class="form-label">Platinum (per ounce)</label>
                                    <div class="input-group">
                                        <span class="input-group-text">$</span>
                                        <input type="number" class="form-control" id="platinum_price" name="platinum_price" 
                                               value="<?php echo $metalPrices['platinum']['price_per_ounce'] ?? '1000.00'; ?>" 
                                               step="0.01" min="0">
                                    </div>
                                    <?php if (isset($metalPrices['platinum'])): ?>
                                        <small class="text-muted">Last updated: <?php echo date('M j, Y g:i A', strtotime($metalPrices['platinum']['updated_at'])); ?></small>
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Update Prices
                            </button>
                        </form>
                    </div>
                </div>
                
                <!-- Cache Management -->
                <div class="settings-card">
                    <div class="settings-header">
                        <h5 class="mb-0"><i class="fas fa-database me-2"></i>Cache Management</h5>
                    </div>
                    <div class="p-4">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Cache Statistics</h6>
                                <ul class="list-unstyled">
                                    <li><strong>Total Files:</strong> <?php echo $cacheStats['total_files']; ?></li>
                                    <li><strong>Valid Files:</strong> <?php echo $cacheStats['valid_files']; ?></li>
                                    <li><strong>Expired Files:</strong> <?php echo $cacheStats['expired_files']; ?></li>
                                    <li><strong>Total Size:</strong> <?php echo formatBytes($cacheStats['total_size']); ?></li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>Actions</h6>
                                <form method="POST" class="d-inline">
                                    <input type="hidden" name="action" value="clear_cache">
                                    <?php echo csrfTokenField(); ?>
                                    <button type="submit" class="btn btn-warning" onclick="return confirm('Are you sure you want to clear the cache?')">
                                        <i class="fas fa-trash me-2"></i>Clear Cache
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- System Information -->
                <div class="settings-card">
                    <div class="settings-header">
                        <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>System Information</h5>
                    </div>
                    <div class="p-4">
                        <div class="row">
                            <?php foreach ($systemInfo as $key => $value): ?>
                                <div class="col-md-6 mb-2">
                                    <strong><?php echo ucwords(str_replace('_', ' ', $key)); ?>:</strong>
                                    <span class="text-muted"><?php echo htmlspecialchars($value); ?></span>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php
function formatBytes($size, $precision = 2) {
    $base = log($size, 1024);
    $suffixes = array('B', 'KB', 'MB', 'GB', 'TB');
    return round(pow(1024, $base - floor($base)), $precision) . ' ' . $suffixes[floor($base)];
}
?>
