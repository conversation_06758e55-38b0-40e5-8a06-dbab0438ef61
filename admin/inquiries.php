<?php
/**
 * Admin Inquiries - 14K Jewelry & Coin Exchange
 * View and manage contact inquiries
 */

// Include configuration and core files
require_once '../config/config.php';
require_once '../includes/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check authentication
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: /admin/');
    exit;
}

// Fetch inquiries from database
$inquiries = [];
if ($pdo) {
    try {
        $stmt = $pdo->query("
            SELECT id, name, email, phone, subject, message, created_at 
            FROM contact_inquiries 
            ORDER BY created_at DESC 
            LIMIT 50
        ");
        $inquiries = $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Error fetching inquiries: " . $e->getMessage());
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Inquiries - Admin - 14K Exchange</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../assets/css/main.css">
    <style>
        .sidebar { background: #2c3e50; min-height: 100vh; }
        .sidebar .nav-link { color: #bdc3c7; }
        .sidebar .nav-link:hover, .sidebar .nav-link.active { color: white; background: #34495e; }
        .main-content { background: #f8f9fa; min-height: 100vh; }
        .inquiry-card { background: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 1rem; }
        .inquiry-header { background: #f8f9fa; border-radius: 10px 10px 0 0; padding: 1rem; border-bottom: 1px solid #dee2e6; }
        .inquiry-body { padding: 1rem; }
        .badge-new { background: #28a745; }
        .badge-old { background: #6c757d; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar p-0">
                <div class="p-3">
                    <h4 class="text-white">14K Exchange</h4>
                    <small class="text-muted">Admin Panel</small>
                </div>
                <nav class="nav flex-column">
                    <a class="nav-link" href="/admin/"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a>
                    <a class="nav-link" href="#items"><i class="fas fa-gem me-2"></i>Items</a>
                    <a class="nav-link" href="#customers"><i class="fas fa-users me-2"></i>Customers</a>
                    <a class="nav-link" href="#transactions"><i class="fas fa-receipt me-2"></i>Transactions</a>
                    <a class="nav-link active" href="/admin/inquiries.php"><i class="fas fa-envelope me-2"></i>Inquiries</a>
                    <a class="nav-link" href="#settings"><i class="fas fa-cog me-2"></i>Settings</a>
                    <hr class="text-muted">
                    <a class="nav-link" href="/" target="_blank"><i class="fas fa-external-link-alt me-2"></i>View Site</a>
                    <a class="nav-link" href="?logout=1"><i class="fas fa-sign-out-alt me-2"></i>Logout</a>
                </nav>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1>Contact Inquiries</h1>
                    <span class="badge bg-primary"><?php echo count($inquiries); ?> Total</span>
                </div>
                
                <?php if (empty($inquiries)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-envelope fa-3x text-muted mb-3"></i>
                        <h3 class="text-muted">No Inquiries Yet</h3>
                        <p class="text-muted">Contact form submissions will appear here.</p>
                    </div>
                <?php else: ?>
                    <?php foreach ($inquiries as $inquiry): ?>
                        <div class="inquiry-card">
                            <div class="inquiry-header">
                                <div class="row align-items-center">
                                    <div class="col-md-6">
                                        <h5 class="mb-1"><?php echo htmlspecialchars($inquiry['name']); ?></h5>
                                        <small class="text-muted">
                                            <i class="fas fa-envelope me-1"></i>
                                            <?php echo htmlspecialchars($inquiry['email']); ?>
                                            <?php if ($inquiry['phone']): ?>
                                                <i class="fas fa-phone ms-3 me-1"></i>
                                                <?php echo htmlspecialchars($inquiry['phone']); ?>
                                            <?php endif; ?>
                                        </small>
                                    </div>
                                    <div class="col-md-6 text-end">
                                        <?php if ($inquiry['subject']): ?>
                                            <span class="badge bg-info me-2"><?php echo htmlspecialchars($inquiry['subject']); ?></span>
                                        <?php endif; ?>
                                        <?php
                                        $created = new DateTime($inquiry['created_at']);
                                        $now = new DateTime();
                                        $diff = $now->diff($created);
                                        $isNew = $diff->days < 1;
                                        ?>
                                        <span class="badge <?php echo $isNew ? 'badge-new' : 'badge-old'; ?>">
                                            <?php echo $created->format('M j, Y g:i A'); ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="inquiry-body">
                                <p class="mb-0"><?php echo nl2br(htmlspecialchars($inquiry['message'])); ?></p>
                                <div class="mt-3">
                                    <button class="btn btn-sm btn-primary me-2" onclick="replyToInquiry('<?php echo htmlspecialchars($inquiry['email']); ?>')">
                                        <i class="fas fa-reply me-1"></i>Reply
                                    </button>
                                    <button class="btn btn-sm btn-outline-secondary" onclick="markAsRead(<?php echo $inquiry['id']; ?>)">
                                        <i class="fas fa-check me-1"></i>Mark as Read
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function replyToInquiry(email) {
            window.location.href = 'mailto:' + email;
        }
        
        function markAsRead(id) {
            // In a real implementation, this would make an AJAX call to mark the inquiry as read
            alert('Feature coming soon! Inquiry ID: ' + id);
        }
    </script>
</body>
</html>
