<?php
/**
 * Admin Transactions Management - 14K Jewelry & Coin Exchange
 * Manage sales transactions
 */

require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../includes/csrf.php';

// Start session and check authentication
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: /admin/');
    exit;
}

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $message = 'Security token validation failed.';
        $messageType = 'danger';
    } else {
        $action = $_POST['action'] ?? '';
        
        if ($action === 'add_transaction' && $pdo) {
            try {
                $pdo->beginTransaction();
                
                // Insert transaction
                $stmt = $pdo->prepare("
                    INSERT INTO transactions (customer_id, transaction_type, total_amount, payment_method, notes, created_at) 
                    VALUES (?, ?, ?, ?, ?, NOW())
                ");
                $stmt->execute([
                    intval($_POST['customer_id']) ?: null,
                    sanitizeInput($_POST['transaction_type']),
                    floatval($_POST['total_amount']),
                    sanitizeInput($_POST['payment_method']),
                    sanitizeInput($_POST['notes']),
                ]);
                
                $transactionId = $pdo->lastInsertId();
                
                // If items are selected, add them to transaction_items
                if (!empty($_POST['item_ids'])) {
                    $stmt = $pdo->prepare("
                        INSERT INTO transaction_items (transaction_id, item_id, quantity, price) 
                        VALUES (?, ?, 1, ?)
                    ");
                    
                    foreach ($_POST['item_ids'] as $itemId) {
                        // Get item price
                        $itemStmt = $pdo->prepare("SELECT selling_price FROM items WHERE id = ?");
                        $itemStmt->execute([intval($itemId)]);
                        $item = $itemStmt->fetch();
                        
                        if ($item) {
                            $stmt->execute([$transactionId, intval($itemId), $item['selling_price']]);
                            
                            // Mark item as sold
                            $updateStmt = $pdo->prepare("UPDATE items SET status = 'sold' WHERE id = ?");
                            $updateStmt->execute([intval($itemId)]);
                        }
                    }
                }
                
                $pdo->commit();
                $message = 'Transaction added successfully!';
                $messageType = 'success';
            } catch (PDOException $e) {
                $pdo->rollBack();
                $message = 'Error adding transaction: ' . $e->getMessage();
                $messageType = 'danger';
            }
        }
    }
}

// Fetch transactions, customers, and available items
$transactions = [];
$customers = [];
$availableItems = [];

if ($pdo) {
    try {
        // Get transactions with customer names
        $stmt = $pdo->query("
            SELECT t.*, 
                   CONCAT(c.first_name, ' ', c.last_name) as customer_name,
                   c.email as customer_email
            FROM transactions t 
            LEFT JOIN customers c ON t.customer_id = c.id 
            ORDER BY t.created_at DESC 
            LIMIT 50
        ");
        $transactions = $stmt->fetchAll();
        
        // Get customers for dropdown
        $stmt = $pdo->query("
            SELECT id, CONCAT(first_name, ' ', last_name) as name 
            FROM customers 
            ORDER BY first_name, last_name
        ");
        $customers = $stmt->fetchAll();
        
        // Get available items
        $stmt = $pdo->query("
            SELECT id, name, selling_price 
            FROM items 
            WHERE status = 'available' 
            ORDER BY name
        ");
        $availableItems = $stmt->fetchAll();
        
    } catch (PDOException $e) {
        error_log("Error fetching transactions: " . $e->getMessage());
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transactions Management - Admin - 14K Exchange</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .sidebar { background: #2c3e50; min-height: 100vh; }
        .sidebar .nav-link { color: #bdc3c7; }
        .sidebar .nav-link:hover, .sidebar .nav-link.active { color: white; background: #34495e; }
        .main-content { background: #f8f9fa; min-height: 100vh; }
        .transaction-card { background: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .type-purchase { border-left: 4px solid #28a745; }
        .type-sale { border-left: 4px solid #007bff; }
        .type-appraisal { border-left: 4px solid #ffc107; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar p-0">
                <div class="p-3">
                    <h4 class="text-white">14K Exchange</h4>
                    <small class="text-muted">Admin Panel</small>
                </div>
                <nav class="nav flex-column">
                    <a class="nav-link" href="/admin/"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a>
                    <a class="nav-link" href="/admin/items.php"><i class="fas fa-gem me-2"></i>Items</a>
                    <a class="nav-link" href="/admin/customers.php"><i class="fas fa-users me-2"></i>Customers</a>
                    <a class="nav-link active" href="/admin/transactions.php"><i class="fas fa-receipt me-2"></i>Transactions</a>
                    <a class="nav-link" href="/admin/inquiries.php"><i class="fas fa-envelope me-2"></i>Inquiries</a>
                    <a class="nav-link" href="/admin/settings.php"><i class="fas fa-cog me-2"></i>Settings</a>
                    <hr class="text-muted">
                    <a class="nav-link" href="/" target="_blank"><i class="fas fa-external-link-alt me-2"></i>View Site</a>
                    <a class="nav-link" href="?logout=1"><i class="fas fa-sign-out-alt me-2"></i>Logout</a>
                </nav>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1>Transactions Management</h1>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addTransactionModal">
                        <i class="fas fa-plus me-2"></i>New Transaction
                    </button>
                </div>
                
                <?php if ($message): ?>
                    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                        <?php echo htmlspecialchars($message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <!-- Transactions List -->
                <?php if (empty($transactions)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                        <h3 class="text-muted">No Transactions Yet</h3>
                        <p class="text-muted">Record your first transaction to get started.</p>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addTransactionModal">
                            <i class="fas fa-plus me-2"></i>Add First Transaction
                        </button>
                    </div>
                <?php else: ?>
                    <div class="row">
                        <?php foreach ($transactions as $transaction): ?>
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="transaction-card p-3 type-<?php echo $transaction['transaction_type']; ?>">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h6 class="mb-0">
                                            <span class="badge bg-<?php echo $transaction['transaction_type'] === 'sale' ? 'primary' : ($transaction['transaction_type'] === 'purchase' ? 'success' : 'warning'); ?>">
                                                <?php echo ucfirst($transaction['transaction_type']); ?>
                                            </span>
                                        </h6>
                                        <small class="text-muted">#<?php echo $transaction['id']; ?></small>
                                    </div>
                                    
                                    <div class="mb-2">
                                        <strong class="text-success">$<?php echo number_format($transaction['total_amount'], 2); ?></strong>
                                    </div>
                                    
                                    <?php if ($transaction['customer_name']): ?>
                                        <div class="mb-2">
                                            <small class="text-muted">Customer:</small><br>
                                            <strong><?php echo htmlspecialchars($transaction['customer_name']); ?></strong>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="mb-2">
                                        <small class="text-muted">Payment:</small><br>
                                        <?php echo htmlspecialchars($transaction['payment_method']); ?>
                                    </div>
                                    
                                    <div class="mb-2">
                                        <small class="text-muted">Date:</small><br>
                                        <?php echo date('M j, Y g:i A', strtotime($transaction['created_at'])); ?>
                                    </div>
                                    
                                    <?php if ($transaction['notes']): ?>
                                        <div class="mb-2">
                                            <small class="text-muted">Notes:</small><br>
                                            <small><?php echo htmlspecialchars($transaction['notes']); ?></small>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="text-center mt-3">
                                        <button class="btn btn-sm btn-outline-primary" onclick="viewTransaction(<?php echo $transaction['id']; ?>)">
                                            <i class="fas fa-eye"></i> View Details
                                        </button>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Add Transaction Modal -->
    <div class="modal fade" id="addTransactionModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">New Transaction</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add_transaction">
                        <?php echo csrfTokenField(); ?>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="transaction_type" class="form-label">Transaction Type *</label>
                                <select class="form-select" id="transaction_type" name="transaction_type" required>
                                    <option value="">Select Type</option>
                                    <option value="sale">Sale</option>
                                    <option value="purchase">Purchase</option>
                                    <option value="appraisal">Appraisal</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="customer_id" class="form-label">Customer</label>
                                <select class="form-select" id="customer_id" name="customer_id">
                                    <option value="">Select Customer (Optional)</option>
                                    <?php foreach ($customers as $customer): ?>
                                        <option value="<?php echo $customer['id']; ?>"><?php echo htmlspecialchars($customer['name']); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="total_amount" class="form-label">Total Amount *</label>
                                <input type="number" class="form-control" id="total_amount" name="total_amount" step="0.01" min="0" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="payment_method" class="form-label">Payment Method *</label>
                                <select class="form-select" id="payment_method" name="payment_method" required>
                                    <option value="">Select Method</option>
                                    <option value="cash">Cash</option>
                                    <option value="check">Check</option>
                                    <option value="credit_card">Credit Card</option>
                                    <option value="bank_transfer">Bank Transfer</option>
                                </select>
                            </div>
                        </div>
                        
                        <?php if (!empty($availableItems)): ?>
                            <div class="mb-3">
                                <label class="form-label">Items (Optional)</label>
                                <div class="border rounded p-3" style="max-height: 200px; overflow-y: auto;">
                                    <?php foreach ($availableItems as $item): ?>
                                        <div class="form-check">
                                            <input class="form-check-input item-checkbox" type="checkbox" name="item_ids[]" value="<?php echo $item['id']; ?>" id="item_<?php echo $item['id']; ?>" data-price="<?php echo $item['selling_price']; ?>">
                                            <label class="form-check-label" for="item_<?php echo $item['id']; ?>">
                                                <?php echo htmlspecialchars($item['name']); ?> - $<?php echo number_format($item['selling_price'], 2); ?>
                                            </label>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                                <small class="text-muted">Selected items will automatically calculate the total amount.</small>
                            </div>
                        <?php endif; ?>
                        
                        <div class="mb-3">
                            <label for="notes" class="form-label">Notes</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Add Transaction</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function viewTransaction(id) {
            alert('Transaction details coming soon! Transaction ID: ' + id);
        }

        // Auto-calculate total when items are selected
        document.addEventListener('DOMContentLoaded', function() {
            const itemCheckboxes = document.querySelectorAll('.item-checkbox');
            const totalAmountInput = document.getElementById('total_amount');

            itemCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    let total = 0;
                    itemCheckboxes.forEach(cb => {
                        if (cb.checked) {
                            total += parseFloat(cb.dataset.price) || 0;
                        }
                    });
                    totalAmountInput.value = total.toFixed(2);
                });
            });
        });
    </script>
</body>
</html>
