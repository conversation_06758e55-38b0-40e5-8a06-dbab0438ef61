<?php
/**
 * Admin Panel - 14K Jewelry & Coin Exchange
 * Administrative dashboard for managing the website
 */

// Include configuration and core files
require_once '../config/config.php';
require_once '../includes/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Simple authentication check
$is_logged_in = isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;

// Handle login
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['login'])) {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    
    // Simple authentication (in production, use proper password hashing)
    if ($username === 'admin' && $password === 'admin123') {
        $_SESSION['admin_logged_in'] = true;
        $_SESSION['admin_username'] = $username;
        $is_logged_in = true;
    } else {
        $login_error = 'Invalid username or password';
    }
}

// Handle logout
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: /admin/');
    exit;
}

// If not logged in, show login form
if (!$is_logged_in) {
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - 14K Exchange</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../assets/css/main.css">
    <style>
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .login-container { min-height: 100vh; display: flex; align-items: center; }
        .login-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
    </style>
</head>
<body>
    <div class="container login-container">
        <div class="row justify-content-center w-100">
            <div class="col-md-6 col-lg-4">
                <div class="login-card p-4">
                    <div class="text-center mb-4">
                        <h2>14K Exchange</h2>
                        <p class="text-muted">Admin Panel Login</p>
                    </div>
                    
                    <?php if (isset($login_error)): ?>
                        <div class="alert alert-danger"><?php echo htmlspecialchars($login_error); ?></div>
                    <?php endif; ?>
                    
                    <form method="POST">
                        <div class="mb-3">
                            <label for="username" class="form-label">Username</label>
                            <input type="text" class="form-control" id="username" name="username" required>
                        </div>
                        <div class="mb-3">
                            <label for="password" class="form-label">Password</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>
                        <button type="submit" name="login" class="btn btn-primary w-100">Login</button>
                    </form>
                    
                    <div class="text-center mt-3">
                        <small class="text-muted">Default: admin / admin123</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
<?php
    exit;
}

// Get dashboard statistics
$stats = [
    'items' => 0,
    'customers' => 0,
    'transactions' => 0,
    'inquiries' => 0
];

if ($pdo) {
    try {
        // Get inquiries count
        $stmt = $pdo->query("SELECT COUNT(*) FROM contact_inquiries");
        $stats['inquiries'] = $stmt->fetchColumn();

        // Get other counts (tables may not exist yet)
        $tables = ['items', 'customers', 'transactions'];
        foreach ($tables as $table) {
            try {
                $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
                $stats[$table] = $stmt->fetchColumn();
            } catch (PDOException $e) {
                // Table doesn't exist yet, keep default 0
            }
        }
    } catch (PDOException $e) {
        error_log("Dashboard stats error: " . $e->getMessage());
    }
}

// Admin dashboard content
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - 14K Exchange</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../assets/css/main.css">
    <style>
        .sidebar { background: #2c3e50; min-height: 100vh; }
        .sidebar .nav-link { color: #bdc3c7; }
        .sidebar .nav-link:hover, .sidebar .nav-link.active { color: white; background: #34495e; }
        .main-content { background: #f8f9fa; min-height: 100vh; }
        .stat-card { background: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar p-0">
                <div class="p-3">
                    <h4 class="text-white">14K Exchange</h4>
                    <small class="text-muted">Admin Panel</small>
                </div>
                <nav class="nav flex-column">
                    <a class="nav-link active" href="#dashboard"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a>
                    <a class="nav-link" href="#items"><i class="fas fa-gem me-2"></i>Items</a>
                    <a class="nav-link" href="#customers"><i class="fas fa-users me-2"></i>Customers</a>
                    <a class="nav-link" href="#transactions"><i class="fas fa-receipt me-2"></i>Transactions</a>
                    <a class="nav-link" href="/admin/inquiries.php"><i class="fas fa-envelope me-2"></i>Inquiries</a>
                    <a class="nav-link" href="#settings"><i class="fas fa-cog me-2"></i>Settings</a>
                    <hr class="text-muted">
                    <a class="nav-link" href="/" target="_blank"><i class="fas fa-external-link-alt me-2"></i>View Site</a>
                    <a class="nav-link" href="?logout=1"><i class="fas fa-sign-out-alt me-2"></i>Logout</a>
                </nav>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1>Dashboard</h1>
                    <span class="text-muted">Welcome, <?php echo htmlspecialchars($_SESSION['admin_username']); ?></span>
                </div>
                
                <!-- Stats Cards -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="stat-card p-3">
                            <div class="d-flex align-items-center">
                                <div class="flex-grow-1">
                                    <h6 class="text-muted mb-1">Total Items</h6>
                                    <h3 class="mb-0"><?php echo number_format($stats['items']); ?></h3>
                                </div>
                                <div class="text-primary">
                                    <i class="fas fa-gem fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="stat-card p-3">
                            <div class="d-flex align-items-center">
                                <div class="flex-grow-1">
                                    <h6 class="text-muted mb-1">Customers</h6>
                                    <h3 class="mb-0"><?php echo number_format($stats['customers']); ?></h3>
                                </div>
                                <div class="text-success">
                                    <i class="fas fa-users fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="stat-card p-3">
                            <div class="d-flex align-items-center">
                                <div class="flex-grow-1">
                                    <h6 class="text-muted mb-1">Transactions</h6>
                                    <h3 class="mb-0"><?php echo number_format($stats['transactions']); ?></h3>
                                </div>
                                <div class="text-warning">
                                    <i class="fas fa-receipt fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="stat-card p-3">
                            <div class="d-flex align-items-center">
                                <div class="flex-grow-1">
                                    <h6 class="text-muted mb-1">Inquiries</h6>
                                    <h3 class="mb-0"><?php echo number_format($stats['inquiries']); ?></h3>
                                </div>
                                <div class="text-info">
                                    <i class="fas fa-envelope fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Actions -->
                <div class="row">
                    <div class="col-md-6 mb-4">
                        <div class="stat-card p-4">
                            <h5 class="mb-3">Quick Actions</h5>
                            <div class="d-grid gap-2">
                                <button class="btn btn-primary" onclick="alert('Feature coming soon!')">
                                    <i class="fas fa-plus me-2"></i>Add New Item
                                </button>
                                <button class="btn btn-success" onclick="alert('Feature coming soon!')">
                                    <i class="fas fa-user-plus me-2"></i>Add Customer
                                </button>
                                <button class="btn btn-warning" onclick="alert('Feature coming soon!')">
                                    <i class="fas fa-receipt me-2"></i>New Transaction
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-4">
                        <div class="stat-card p-4">
                            <h5 class="mb-3">Recent Activity</h5>
                            <div class="text-muted text-center py-4">
                                <i class="fas fa-clock fa-3x mb-3"></i>
                                <p>No recent activity</p>
                                <small>Activity will appear here as you use the system</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- System Info -->
                <div class="row">
                    <div class="col-12">
                        <div class="stat-card p-4">
                            <h5 class="mb-3">System Information</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Database:</strong> <?php echo $pdo ? 'Connected' : 'Not Connected'; ?></p>
                                    <p><strong>Environment:</strong> <?php echo ENVIRONMENT; ?></p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>PHP Version:</strong> <?php echo PHP_VERSION; ?></p>
                                    <p><strong>Server Time:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
