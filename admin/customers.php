<?php
/**
 * Admin Customers Management - 14K Jewelry & Coin Exchange
 * Manage customer database
 */

require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../includes/csrf.php';

// Start session and check authentication
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: /admin/');
    exit;
}

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $message = 'Security token validation failed.';
        $messageType = 'danger';
    } else {
        $action = $_POST['action'] ?? '';
        
        if ($action === 'add_customer' && $pdo) {
            try {
                $stmt = $pdo->prepare("
                    INSERT INTO customers (first_name, last_name, email, phone, address, city, state, zip_code, notes, created_at) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
                ");
                $stmt->execute([
                    sanitizeInput($_POST['first_name']),
                    sanitizeInput($_POST['last_name']),
                    sanitizeInput($_POST['email']),
                    sanitizeInput($_POST['phone']),
                    sanitizeInput($_POST['address']),
                    sanitizeInput($_POST['city']),
                    sanitizeInput($_POST['state']),
                    sanitizeInput($_POST['zip_code']),
                    sanitizeInput($_POST['notes']),
                ]);
                $message = 'Customer added successfully!';
                $messageType = 'success';
            } catch (PDOException $e) {
                $message = 'Error adding customer: ' . $e->getMessage();
                $messageType = 'danger';
            }
        } elseif ($action === 'delete_customer' && $pdo) {
            try {
                $stmt = $pdo->prepare("DELETE FROM customers WHERE id = ?");
                $stmt->execute([intval($_POST['customer_id'])]);
                $message = 'Customer deleted successfully!';
                $messageType = 'success';
            } catch (PDOException $e) {
                $message = 'Error deleting customer: ' . $e->getMessage();
                $messageType = 'danger';
            }
        }
    }
}

// Fetch customers
$customers = [];
if ($pdo) {
    try {
        $stmt = $pdo->query("
            SELECT c.*, 
                   COUNT(t.id) as transaction_count,
                   COALESCE(SUM(t.total_amount), 0) as total_spent
            FROM customers c 
            LEFT JOIN transactions t ON c.id = t.customer_id 
            GROUP BY c.id 
            ORDER BY c.created_at DESC 
            LIMIT 50
        ");
        $customers = $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Error fetching customers: " . $e->getMessage());
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Customers Management - Admin - 14K Exchange</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .sidebar { background: #2c3e50; min-height: 100vh; }
        .sidebar .nav-link { color: #bdc3c7; }
        .sidebar .nav-link:hover, .sidebar .nav-link.active { color: white; background: #34495e; }
        .main-content { background: #f8f9fa; min-height: 100vh; }
        .customer-card { background: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar p-0">
                <div class="p-3">
                    <h4 class="text-white">14K Exchange</h4>
                    <small class="text-muted">Admin Panel</small>
                </div>
                <nav class="nav flex-column">
                    <a class="nav-link" href="/admin/"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a>
                    <a class="nav-link" href="/admin/items.php"><i class="fas fa-gem me-2"></i>Items</a>
                    <a class="nav-link active" href="/admin/customers.php"><i class="fas fa-users me-2"></i>Customers</a>
                    <a class="nav-link" href="/admin/transactions.php"><i class="fas fa-receipt me-2"></i>Transactions</a>
                    <a class="nav-link" href="/admin/inquiries.php"><i class="fas fa-envelope me-2"></i>Inquiries</a>
                    <a class="nav-link" href="/admin/settings.php"><i class="fas fa-cog me-2"></i>Settings</a>
                    <hr class="text-muted">
                    <a class="nav-link" href="/" target="_blank"><i class="fas fa-external-link-alt me-2"></i>View Site</a>
                    <a class="nav-link" href="?logout=1"><i class="fas fa-sign-out-alt me-2"></i>Logout</a>
                </nav>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1>Customers Management</h1>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCustomerModal">
                        <i class="fas fa-user-plus me-2"></i>Add New Customer
                    </button>
                </div>
                
                <?php if ($message): ?>
                    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                        <?php echo htmlspecialchars($message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <!-- Customers List -->
                <?php if (empty($customers)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h3 class="text-muted">No Customers Yet</h3>
                        <p class="text-muted">Add your first customer to get started.</p>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCustomerModal">
                            <i class="fas fa-user-plus me-2"></i>Add First Customer
                        </button>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Name</th>
                                    <th>Contact</th>
                                    <th>Location</th>
                                    <th>Transactions</th>
                                    <th>Total Spent</th>
                                    <th>Joined</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($customers as $customer): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($customer['first_name'] . ' ' . $customer['last_name']); ?></strong>
                                        </td>
                                        <td>
                                            <?php if ($customer['email']): ?>
                                                <div><i class="fas fa-envelope text-muted me-1"></i><?php echo htmlspecialchars($customer['email']); ?></div>
                                            <?php endif; ?>
                                            <?php if ($customer['phone']): ?>
                                                <div><i class="fas fa-phone text-muted me-1"></i><?php echo htmlspecialchars($customer['phone']); ?></div>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($customer['city'] || $customer['state']): ?>
                                                <?php echo htmlspecialchars(trim($customer['city'] . ', ' . $customer['state'], ', ')); ?>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?php echo $customer['transaction_count']; ?></span>
                                        </td>
                                        <td>
                                            <strong class="text-success">$<?php echo number_format($customer['total_spent'], 2); ?></strong>
                                        </td>
                                        <td>
                                            <?php echo date('M j, Y', strtotime($customer['created_at'])); ?>
                                        </td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary me-1" onclick="viewCustomer(<?php echo $customer['id']; ?>)">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary me-1" onclick="editCustomer(<?php echo $customer['id']; ?>)">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" onclick="deleteCustomer(<?php echo $customer['id']; ?>, '<?php echo htmlspecialchars($customer['first_name'] . ' ' . $customer['last_name']); ?>')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Add Customer Modal -->
    <div class="modal fade" id="addCustomerModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add New Customer</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add_customer">
                        <?php echo csrfTokenField(); ?>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="first_name" class="form-label">First Name *</label>
                                <input type="text" class="form-control" id="first_name" name="first_name" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="last_name" class="form-label">Last Name *</label>
                                <input type="text" class="form-control" id="last_name" name="last_name" required>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="email" name="email">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">Phone</label>
                                <input type="tel" class="form-control" id="phone" name="phone">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="address" class="form-label">Address</label>
                            <input type="text" class="form-control" id="address" name="address">
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="city" class="form-label">City</label>
                                <input type="text" class="form-control" id="city" name="city">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="state" class="form-label">State</label>
                                <input type="text" class="form-control" id="state" name="state">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="zip_code" class="form-label">ZIP Code</label>
                                <input type="text" class="form-control" id="zip_code" name="zip_code">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="notes" class="form-label">Notes</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Add Customer</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Form (hidden) -->
    <form id="deleteForm" method="POST" style="display: none;">
        <input type="hidden" name="action" value="delete_customer">
        <input type="hidden" name="customer_id" id="deleteCustomerId">
        <?php echo csrfTokenField(); ?>
    </form>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function viewCustomer(id) {
            alert('View customer details coming soon! Customer ID: ' + id);
        }
        
        function editCustomer(id) {
            alert('Edit functionality coming soon! Customer ID: ' + id);
        }
        
        function deleteCustomer(id, name) {
            if (confirm('Are you sure you want to delete customer "' + name + '"?')) {
                document.getElementById('deleteCustomerId').value = id;
                document.getElementById('deleteForm').submit();
            }
        }
    </script>
</body>
</html>
