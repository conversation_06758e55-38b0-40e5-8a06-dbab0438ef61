#!/bin/bash

# GitHub Setup Script for 14K Exchange Website
# This script helps you push the project to GitHub

echo "🚀 14K Exchange - GitHub Setup"
echo "================================"
echo ""

# Check if git is initialized
if [ ! -d ".git" ]; then
    echo "❌ Git repository not found. Initializing..."
    git init
    git branch -M main
else
    echo "✅ Git repository found"
fi

# Check current remote
CURRENT_REMOTE=$(git remote get-url origin 2>/dev/null)
if [ $? -eq 0 ]; then
    echo "📡 Current remote: $CURRENT_REMOTE"
else
    echo "⚠️  No remote repository configured"
fi

echo ""
echo "Please provide your GitHub repository information:"
echo ""

# Get GitHub username
read -p "GitHub username (e.g., joanncodex): " GITHUB_USER
if [ -z "$GITHUB_USER" ]; then
    echo "❌ Username is required"
    exit 1
fi

# Get repository name
read -p "Repository name (e.g., southgate14k): " REPO_NAME
if [ -z "$REPO_NAME" ]; then
    echo "❌ Repository name is required"
    exit 1
fi

# Construct repository URL
REPO_URL="https://github.com/$GITHUB_USER/$REPO_NAME.git"

echo ""
echo "📋 Repository URL: $REPO_URL"
echo ""

# Ask for confirmation
read -p "Is this correct? (y/N): " CONFIRM
if [[ ! $CONFIRM =~ ^[Yy]$ ]]; then
    echo "❌ Setup cancelled"
    exit 1
fi

echo ""
echo "🔧 Setting up repository..."

# Set or update remote
if [ -z "$CURRENT_REMOTE" ]; then
    git remote add origin "$REPO_URL"
    echo "✅ Remote added"
else
    git remote set-url origin "$REPO_URL"
    echo "✅ Remote updated"
fi

# Check if files are committed
if git diff-index --quiet HEAD --; then
    echo "✅ All files are committed"
else
    echo "📝 Committing changes..."
    git add .
    git commit -m "Initial commit: Complete 14K Exchange website with admin panel

Features:
- Professional responsive website
- Fully functional admin panel
- Database-driven inventory management
- Interactive price calculator
- Security features and SEO optimization
- Contact forms and inquiry management"
    echo "✅ Files committed"
fi

echo ""
echo "🚀 Pushing to GitHub..."
echo ""

# Push to GitHub
if git push -u origin main; then
    echo ""
    echo "🎉 SUCCESS! Your code has been pushed to GitHub!"
    echo ""
    echo "📱 Repository URL: https://github.com/$GITHUB_USER/$REPO_NAME"
    echo ""
    echo "Next steps:"
    echo "1. Visit your repository on GitHub to verify the upload"
    echo "2. Review the DEPLOYMENT.md file for hosting instructions"
    echo "3. Set up your production environment"
    echo ""
else
    echo ""
    echo "❌ Push failed. This might be due to:"
    echo ""
    echo "1. Repository doesn't exist on GitHub"
    echo "   - Go to https://github.com/new"
    echo "   - Create a repository named '$REPO_NAME'"
    echo "   - Don't initialize with README, .gitignore, or license"
    echo ""
    echo "2. Authentication issues"
    echo "   - You may need to use a Personal Access Token"
    echo "   - Or set up SSH keys"
    echo "   - Or use GitHub CLI: gh auth login"
    echo ""
    echo "3. Permission issues"
    echo "   - Make sure you have write access to the repository"
    echo ""
    echo "After fixing the issue, you can run:"
    echo "git push -u origin main"
    echo ""
fi

echo "📚 For more help, see DEPLOYMENT.md"
