<?php
/**
 * Metal Prices API - 14K Jewelry & Coin Exchange
 * Returns current metal prices in JSON format
 */

// Include configuration
require_once '../config/config.php';
require_once '../includes/cache.php';

// Set JSON content type
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Only allow GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

try {
    // Try to get prices from cache first
    $cacheKey = 'metal_prices_api';
    $prices = cache_remember($cacheKey, function() use ($pdo) {
        $defaultPrices = [
            'gold' => ['price' => 2000.00, 'updated' => date('Y-m-d H:i:s')],
            'silver' => ['price' => 25.00, 'updated' => date('Y-m-d H:i:s')],
            'platinum' => ['price' => 1000.00, 'updated' => date('Y-m-d H:i:s')]
        ];
        
        if (!$pdo) {
            return $defaultPrices;
        }
        
        try {
            $stmt = $pdo->query("
                SELECT metal_type, price_per_ounce, updated_at 
                FROM metal_prices 
                ORDER BY updated_at DESC
            ");
            $dbPrices = $stmt->fetchAll();
            
            $result = $defaultPrices;
            foreach ($dbPrices as $price) {
                $result[$price['metal_type']] = [
                    'price' => floatval($price['price_per_ounce']),
                    'updated' => $price['updated_at']
                ];
            }
            
            return $result;
        } catch (PDOException $e) {
            error_log("API prices error: " . $e->getMessage());
            return $defaultPrices;
        }
    }, 300); // Cache for 5 minutes
    
    // Format response
    $response = [
        'success' => true,
        'data' => $prices,
        'timestamp' => date('Y-m-d H:i:s'),
        'source' => 'internal'
    ];
    
    echo json_encode($response, JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Internal server error',
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    error_log("API error: " . $e->getMessage());
}
?>
