# Deployment Guide - 14K Jewelry & Coin Exchange

This guide will help you deploy the 14K Exchange website to GitHub and set up hosting.

## 🚀 GitHub Setup

### Step 1: Create GitHub Repository

1. **Go to GitHub** and sign in to your account
2. **Create a new repository**:
   - Repository name: `southgate14k` (or your preferred name)
   - Description: "Professional website for 14K Jewelry & Coin Exchange"
   - Set to Public or Private (your choice)
   - **DO NOT** initialize with README, .gitignore, or license (we already have these)

### Step 2: Push Local Code to GitHub

The local repository is already initialized and committed. You just need to:

1. **Update the remote URL** (if the repository name is different):
   ```bash
   git remote set-url origin https://github.com/joanncodex/YOUR-REPO-NAME.git
   ```

2. **Push to GitHub**:
   ```bash
   git push -u origin main
   ```

   If you get authentication errors, you may need to:
   - Use a Personal Access Token instead of password
   - Set up SSH keys
   - Use GitHub CLI: `gh auth login`

### Step 3: Verify Upload

Once pushed successfully, your repository should contain:
- All website files
- Admin panel
- Database schema
- Documentation
- Configuration files

## 🌐 Hosting Options

### Option 1: Shared Hosting (Recommended for small business)

**Popular providers**: Bluehost, SiteGround, HostGator, A2 Hosting

**Requirements**:
- PHP 8.0+
- MySQL/MariaDB 5.7+
- SSL certificate
- At least 1GB storage

**Setup steps**:
1. Upload files via FTP/cPanel File Manager
2. Create MySQL database and user
3. Import `database/schema.sql`
4. Update `config/config.php` with production settings
5. Set up SSL certificate
6. Configure domain DNS

### Option 2: VPS/Cloud Hosting

**Providers**: DigitalOcean, Linode, Vultr, AWS EC2

**Setup steps**:
1. Create server instance (Ubuntu 20.04+ recommended)
2. Install LAMP stack (Linux, Apache, MySQL, PHP)
3. Configure virtual host
4. Upload files and set permissions
5. Set up database
6. Configure SSL with Let's Encrypt

### Option 3: Platform-as-a-Service

**Providers**: Heroku, Railway, PlanetScale + Vercel

**Note**: May require modifications for database connections and file storage.

## 🔧 Production Configuration

### Database Setup

1. **Create production database**:
   ```sql
   CREATE DATABASE 14k_exchange_prod CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   CREATE USER 'prod_user'@'localhost' IDENTIFIED BY 'secure_password_here';
   GRANT ALL PRIVILEGES ON 14k_exchange_prod.* TO 'prod_user'@'localhost';
   FLUSH PRIVILEGES;
   ```

2. **Import schema**:
   ```bash
   mysql -u prod_user -p 14k_exchange_prod < database/schema.sql
   ```

### Configuration Updates

Edit `config/config.php` for production:

```php
// Environment
define('ENVIRONMENT', 'production');

// Database
define('DB_HOST', 'localhost');
define('DB_NAME', '14k_exchange_prod');
define('DB_USER', 'prod_user');
define('DB_PASS', 'your_secure_password');

// URLs
define('BASE_URL', 'https://yourdomain.com');

// Security
define('SECURE_COOKIES', true);
```

### Security Checklist

- [ ] Change default admin password
- [ ] Use strong database passwords
- [ ] Enable HTTPS/SSL
- [ ] Set proper file permissions (644 for files, 755 for directories)
- [ ] Remove or secure database setup files
- [ ] Configure firewall
- [ ] Set up regular backups
- [ ] Update PHP and server software regularly

### File Permissions

```bash
# Set proper permissions
find . -type f -exec chmod 644 {} \;
find . -type d -exec chmod 755 {} \;

# Make cache directory writable
chmod 755 cache/
chmod 644 cache/*

# Secure sensitive files
chmod 600 config/config.php
```

## 📧 Email Configuration

For contact forms to work, configure email:

### Option 1: SMTP (Recommended)

Add to `config/config.php`:
```php
define('SMTP_HOST', 'smtp.yourdomain.com');
define('SMTP_PORT', 587);
define('SMTP_USER', '<EMAIL>');
define('SMTP_PASS', 'email_password');
```

### Option 2: PHP mail() function

Ensure your server has mail() function enabled and configured.

## 🔄 Backup Strategy

### Automated Backups

Create a backup script (`backup.sh`):
```bash
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/path/to/backups"

# Database backup
mysqldump -u prod_user -p 14k_exchange_prod > $BACKUP_DIR/db_$DATE.sql

# Files backup
tar -czf $BACKUP_DIR/files_$DATE.tar.gz /path/to/website

# Keep only last 30 days
find $BACKUP_DIR -name "*.sql" -mtime +30 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete
```

Set up cron job:
```bash
# Daily backup at 2 AM
0 2 * * * /path/to/backup.sh
```

## 🚀 Going Live Checklist

### Pre-Launch
- [ ] Test all pages and functionality
- [ ] Verify contact forms work
- [ ] Test admin panel thoroughly
- [ ] Check mobile responsiveness
- [ ] Validate HTML/CSS
- [ ] Test page load speeds
- [ ] Verify SSL certificate

### Launch
- [ ] Update DNS records
- [ ] Set up Google Analytics
- [ ] Submit sitemap to Google Search Console
- [ ] Set up monitoring/uptime checks
- [ ] Create social media accounts
- [ ] Set up business listings (Google My Business)

### Post-Launch
- [ ] Monitor error logs
- [ ] Check contact form submissions
- [ ] Monitor website performance
- [ ] Set up regular content updates
- [ ] Plan SEO improvements

## 📞 Support

If you need help with deployment:

1. **Check the logs** - Most issues are logged in error logs
2. **Verify configuration** - Double-check database credentials and URLs
3. **Test locally first** - Ensure everything works in development
4. **Contact hosting support** - They can help with server-specific issues

## 🔗 Useful Resources

- [PHP Manual](https://www.php.net/manual/)
- [MySQL Documentation](https://dev.mysql.com/doc/)
- [Apache Documentation](https://httpd.apache.org/docs/)
- [Let's Encrypt SSL](https://letsencrypt.org/)
- [Google Search Console](https://search.google.com/search-console)

---

**Need help?** Contact the development team or refer to the main README.md for additional information.
