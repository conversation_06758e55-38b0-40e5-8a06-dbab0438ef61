<?php
/**
 * FAQs - 14K Jewelry & Coin Exchange
 * Frequently Asked Questions page
 */

// Include configuration and core files
require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../includes/seo.php';

// Page-specific SEO data
$page_data = [
    'title' => 'FAQs | Frequently Asked Questions | 14K Exchange Southgate',
    'description' => 'Get answers to common questions about selling gold, silver, jewelry, and coins at 14K Jewelry & Coin Exchange in Southgate, Michigan.',
    'keywords' => 'jewelry questions, coin questions, gold selling FAQ, silver selling FAQ, precious metals FAQ',
    'canonical' => BASE_URL . '/faqs/',
    'og_type' => 'website',
    'schema_type' => 'FAQPage'
];

// Generate SEO meta tags
$seo = new SEOManager($page_data);

// Breadcrumb data
$breadcrumbs = [
    ['name' => 'Home', 'url' => BASE_URL],
    ['name' => 'FAQs', 'url' => BASE_URL . '/faqs/']
];

// FAQ Data
$faqs = [
    [
        'category' => 'General',
        'questions' => [
            [
                'question' => 'Do I need an appointment to sell my items?',
                'answer' => 'No appointment is necessary! We welcome walk-ins during our regular business hours. However, if you have a large collection or estate items, calling ahead can help us prepare for your visit.'
            ],
            [
                'question' => 'What do I need to bring when selling items?',
                'answer' => 'Please bring a valid government-issued photo ID (driver\'s license, passport, or state ID). This is required by law for all precious metals transactions.'
            ],
            [
                'question' => 'How long does the evaluation process take?',
                'answer' => 'Most evaluations take 15-30 minutes, depending on the number and complexity of items. We take our time to ensure accurate testing and fair pricing.'
            ]
        ]
    ],
    [
        'category' => 'Jewelry',
        'questions' => [
            [
                'question' => 'Do you buy broken or damaged jewelry?',
                'answer' => 'Yes! We buy jewelry in any condition. Broken chains, single earrings, bent rings - we evaluate based on metal content and gemstones, not appearance.'
            ],
            [
                'question' => 'How do you test gold purity?',
                'answer' => 'We use electronic gold testers and acid testing to determine exact gold content. Our equipment provides accurate readings for 10K, 14K, 18K, and higher purities.'
            ],
            [
                'question' => 'Do you buy costume jewelry?',
                'answer' => 'We focus on precious metals (gold, silver, platinum) and genuine gemstones. Costume jewelry typically has no precious metal content, but we\'re happy to evaluate any piece you bring in.'
            ]
        ]
    ],
    [
        'category' => 'Coins',
        'questions' => [
            [
                'question' => 'What makes a coin valuable?',
                'answer' => 'Coin value depends on several factors: precious metal content, rarity, condition (grade), and collector demand. We evaluate both numismatic (collector) value and melt value.'
            ],
            [
                'question' => 'Do you buy coin collections?',
                'answer' => 'Absolutely! We purchase individual coins, partial collections, and complete estates. Large collections may require additional time for proper evaluation.'
            ],
            [
                'question' => 'What about foreign coins?',
                'answer' => 'We buy foreign coins that contain precious metals. Many older foreign coins contain silver or gold and have value beyond their face value.'
            ]
        ]
    ]
];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <?php echo $seo->generateMetaTags(); ?>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="../assets/css/faqs.css">

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Open+Sans:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- Header -->
    <?php include '../includes/header.php'; ?>

    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="breadcrumb-nav">
        <div class="container">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="/">Home</a></li>
                <li class="breadcrumb-item active" aria-current="page">FAQs</li>
            </ol>
        </div>
    </nav>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h1 class="page-title">Frequently Asked Questions</h1>
                    <p class="page-subtitle">Get answers to common questions about our services</p>
                    <p class="page-description">Find quick answers to the most common questions about selling jewelry, coins, and precious metals at 14K Exchange.</p>
                </div>
                <div class="col-lg-6">
                    <img src="../assets/images/faqs-hero.jpg" alt="FAQ support" class="img-fluid rounded shadow">
                </div>
            </div>
        </div>
    </section>

    <!-- FAQs Section -->
    <section class="faqs-section py-5">
        <div class="container">
            <?php foreach ($faqs as $categoryIndex => $category): ?>
                <div class="faq-category mb-5">
                    <h2 class="category-title"><?php echo htmlspecialchars($category['category']); ?> Questions</h2>
                    <div class="accordion" id="faqAccordion<?php echo $categoryIndex; ?>">
                        <?php foreach ($category['questions'] as $questionIndex => $faq): ?>
                            <div class="accordion-item">
                                <h3 class="accordion-header" id="heading<?php echo $categoryIndex . $questionIndex; ?>">
                                    <button class="accordion-button collapsed" type="button" 
                                            data-bs-toggle="collapse" 
                                            data-bs-target="#collapse<?php echo $categoryIndex . $questionIndex; ?>" 
                                            aria-expanded="false" 
                                            aria-controls="collapse<?php echo $categoryIndex . $questionIndex; ?>">
                                        <?php echo htmlspecialchars($faq['question']); ?>
                                    </button>
                                </h3>
                                <div id="collapse<?php echo $categoryIndex . $questionIndex; ?>" 
                                     class="accordion-collapse collapse" 
                                     aria-labelledby="heading<?php echo $categoryIndex . $questionIndex; ?>" 
                                     data-bs-parent="#faqAccordion<?php echo $categoryIndex; ?>">
                                    <div class="accordion-body">
                                        <?php echo htmlspecialchars($faq['answer']); ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </section>

    <!-- Contact CTA -->
    <section class="faq-cta py-5 bg-light">
        <div class="container text-center">
            <h2>Still Have Questions?</h2>
            <p class="lead mb-4">Our knowledgeable staff is here to help with any questions about your precious metals.</p>
            <div class="cta-buttons">
                <a href="/contact/" class="btn btn-primary btn-lg me-3">Contact Us</a>
                <a href="tel:<?php echo BUSINESS_PHONE; ?>" class="btn btn-outline-primary btn-lg">Call Now</a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <?php include '../includes/footer.php'; ?>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="../assets/js/main.js"></script>

    <?php echo $seo->generateStructuredData(); ?>
    <?php echo $seo->generateBreadcrumbs($breadcrumbs); ?>
</body>
</html>
