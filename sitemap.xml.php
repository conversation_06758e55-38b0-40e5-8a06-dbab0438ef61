<?php
/**
 * Dynamic Sitemap Generator - 14K Jewelry & Coin Exchange
 * Generates XML sitemap for search engines
 */

// Include configuration
require_once 'config/config.php';

// Set content type to XML
header('Content-Type: application/xml; charset=utf-8');

// Define site pages with their priorities and change frequencies
$pages = [
    [
        'url' => BASE_URL,
        'priority' => '1.0',
        'changefreq' => 'weekly',
        'lastmod' => date('Y-m-d')
    ],
    [
        'url' => BASE_URL . '/jewelry/',
        'priority' => '0.9',
        'changefreq' => 'monthly',
        'lastmod' => date('Y-m-d')
    ],
    [
        'url' => BASE_URL . '/coins-bullion/',
        'priority' => '0.9',
        'changefreq' => 'monthly',
        'lastmod' => date('Y-m-d')
    ],
    [
        'url' => BASE_URL . '/firearms/',
        'priority' => '0.8',
        'changefreq' => 'monthly',
        'lastmod' => date('Y-m-d')
    ],
    [
        'url' => BASE_URL . '/estates-collections/',
        'priority' => '0.8',
        'changefreq' => 'monthly',
        'lastmod' => date('Y-m-d')
    ],
    [
        'url' => BASE_URL . '/what-we-buy/',
        'priority' => '0.9',
        'changefreq' => 'monthly',
        'lastmod' => date('Y-m-d')
    ],
    [
        'url' => BASE_URL . '/sell-silver/',
        'priority' => '0.8',
        'changefreq' => 'monthly',
        'lastmod' => date('Y-m-d')
    ],
    [
        'url' => BASE_URL . '/about/',
        'priority' => '0.7',
        'changefreq' => 'monthly',
        'lastmod' => date('Y-m-d')
    ],
    [
        'url' => BASE_URL . '/contact/',
        'priority' => '0.8',
        'changefreq' => 'monthly',
        'lastmod' => date('Y-m-d')
    ],
    [
        'url' => BASE_URL . '/faqs/',
        'priority' => '0.6',
        'changefreq' => 'monthly',
        'lastmod' => date('Y-m-d')
    ]
];

// Start XML output
echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
<?php foreach ($pages as $page): ?>
    <url>
        <loc><?php echo htmlspecialchars($page['url']); ?></loc>
        <lastmod><?php echo $page['lastmod']; ?></lastmod>
        <changefreq><?php echo $page['changefreq']; ?></changefreq>
        <priority><?php echo $page['priority']; ?></priority>
    </url>
<?php endforeach; ?>
</urlset>
