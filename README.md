# 14K Jewelry & Coin Exchange Website

A professional website and management system for a precious metals dealer specializing in jewelry, coins, bullion, and estate collections.

## 🌟 Features

### Public Website
- **Responsive Design** - Mobile-first design that works on all devices
- **Service Pages** - Dedicated pages for jewelry, coins, firearms, estates
- **Interactive Price Calculator** - Real-time precious metals pricing
- **Contact System** - Working contact forms with database storage
- **SEO Optimized** - Meta tags, structured data, sitemap
- **Professional Design** - Clean, modern interface with custom branding

### Admin Panel
- **Complete Dashboard** - Real-time statistics and quick actions
- **Inventory Management** - Add, edit, delete items with full details
- **Customer Database** - Comprehensive customer relationship management
- **Transaction Tracking** - Record sales, purchases, and appraisals
- **Inquiry Management** - Handle website contact form submissions
- **Settings Panel** - Update metal prices and system configuration

### Technical Features
- **Security Hardened** - CSRF protection, input validation, rate limiting
- **Database Driven** - MariaDB/MySQL with complete schema
- **Caching System** - File-based caching for performance
- **RESTful API** - JSON endpoints for price data
- **Modern PHP** - Clean, object-oriented code structure

## 🚀 Quick Start

### Prerequisites
- PHP 8.0 or higher
- MariaDB/MySQL 5.7 or higher
- Web server (Apache/Nginx) or PHP built-in server

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/southgate-14kgold.git
   cd southgate-14kgold
   ```

2. **Set up the database**
   ```bash
   # Start MariaDB service
   sudo systemctl start mariadb

   # Run the database setup script
   chmod +x setup_database.sh
   ./setup_database.sh
   ```

3. **Configure the application**
   ```bash
   # Copy and edit configuration
   cp config/config.php.example config/config.php
   # Edit config/config.php with your settings
   ```

4. **Start the development server**
   ```bash
   php -S localhost:8000
   ```

5. **Access the application**
   - Website: http://localhost:8000
   - Admin Panel: http://localhost:8000/admin (admin/admin123)

## 📁 Project Structure

```
├── admin/                  # Admin panel pages
│   ├── index.php          # Dashboard
│   ├── items.php          # Inventory management
│   ├── customers.php      # Customer management
│   ├── transactions.php   # Transaction management
│   ├── inquiries.php      # Contact inquiries
│   └── settings.php       # System settings
├── api/                   # RESTful API endpoints
│   └── prices.php         # Metal prices API
├── assets/                # Static assets
│   ├── css/              # Stylesheets
│   ├── js/               # JavaScript files
│   └── images/           # Image assets
├── config/               # Configuration files
│   └── config.php        # Main configuration
├── database/             # Database files
│   └── schema.sql        # Database schema
├── includes/             # PHP includes
│   ├── header.php        # Site header
│   ├── footer.php        # Site footer
│   ├── functions.php     # Utility functions
│   ├── seo.php          # SEO management
│   ├── csrf.php         # Security functions
│   └── cache.php        # Caching system
├── jewelry/              # Jewelry service page
├── coins-bullion/        # Coins & bullion page
├── firearms/             # Firearms page
├── estates-collections/  # Estates page
├── what-we-buy/         # What we buy page
├── sell-silver/         # Sell silver page
├── about/               # About page
├── contact/             # Contact page
├── faqs/                # FAQ page
├── index.php            # Homepage
├── .htaccess           # URL rewriting rules
├── robots.txt          # Search engine directives
└── sitemap.xml.php     # Dynamic sitemap
├── README.md                 # This file
├── config/
│   └── config.php           # Site configuration & database connection
├── includes/
│   ├── header.php           # Site header component
│   ├── footer.php           # Site footer component
│   ├── functions.php        # Utility functions
│   └── seo.php             # SEO management class
├── assets/
│   ├── css/
│   │   ├── main.css        # Main stylesheet
│   │   └── homepage.css    # Homepage-specific styles
│   ├── js/
│   │   └── main.js         # Main JavaScript file
│   └── images/             # Site images and media
├── jewelry/
│   └── index.php           # Fine jewelry page
├── coins-bullion/
│   └── index.php           # Coins & bullion page
├── firearms/
│   └── index.php           # Firearms page
├── estates-collections/
│   └── index.php           # Estates & collections page
├── what-we-buy/
│   └── index.php           # What we buy page
├── sell-silver/
│   └── index.php           # Sell silver page
├── about/
│   └── index.php           # About us page
├── contact/
│   └── index.php           # Contact page
├── faqs/
│   └── index.php           # FAQs page
├── admin/
│   └── index.php           # Admin dashboard
├── database/
│   └── schema.sql          # Database structure
└── oldsite/                # Previous website files (archived)
```

## 🛠 Technology Stack

- **Backend**: PHP 7.4+
- **Database**: MySQL 5.7+
- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Framework**: Bootstrap 5.3
- **Image Gallery**: Fancybox 5.0
- **Icons**: Font Awesome 6.4
- **Fonts**: Google Fonts (Playfair Display, Open Sans)

## 📋 Requirements

- PHP 7.4 or higher
- MySQL 5.7 or higher
- Apache web server with mod_rewrite enabled
- SSL certificate (recommended for production)

## 🚀 Installation

1. **Clone or download** the project files to your web server
2. **Configure database** settings in `config/config.php`
3. **Import database** schema from `database/schema.sql`
4. **Set up virtual host** or configure your web server
5. **Update configuration** values in `config/config.php`
6. **Test the installation** by visiting your domain

### Database Setup

```sql
-- Create database
CREATE DATABASE 14k_exchange CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Import schema
mysql -u username -p 14k_exchange < database/schema.sql
```

### Configuration

Update the following in `config/config.php`:

```php
// Database settings
define('DB_HOST', 'localhost');
define('DB_NAME', '14k_exchange');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');

// Site settings
define('BASE_URL', 'https://yourdomain.com');
define('BUSINESS_PHONE', '(*************');
define('BUSINESS_EMAIL', '<EMAIL>');
```

## 🎨 Customization

### Adding New Pages

1. Create a new directory: `/new-page/`
2. Add `index.php` with proper SEO structure
3. Update navigation in `includes/header.php`
4. Add URL rewrite rule in `.htaccess`

### Styling

- Main styles: `assets/css/main.css`
- Page-specific styles: `assets/css/[page-name].css`
- CSS variables for easy theme customization

### JavaScript

- Main functionality: `assets/js/main.js`
- Modular structure with ES6+ features
- Includes form validation, animations, and API integration

## 🔒 Security Features

- CSRF token protection
- Input sanitization and validation
- SQL injection prevention with PDO
- XSS protection headers
- File upload restrictions
- Activity logging
- Secure session management

## 📈 SEO Features

- **Structured Data**: JSON-LD schema markup
- **Meta Tags**: Comprehensive meta tag management
- **Open Graph**: Social media optimization
- **Breadcrumbs**: Navigation and SEO breadcrumbs
- **Clean URLs**: SEO-friendly URL structure
- **Sitemaps**: Automatic sitemap generation
- **Page Speed**: Optimized loading times

## 🎯 Performance Optimization

- **Compression**: Gzip compression enabled
- **Caching**: Browser caching headers
- **Image Optimization**: WebP support, lazy loading
- **Minification**: CSS and JS optimization
- **CDN Ready**: External resource optimization

## 📱 Responsive Design

- Mobile-first approach
- Bootstrap 5 grid system
- Touch-friendly interfaces
- Optimized for all screen sizes
- Progressive enhancement

## 🔧 Maintenance

### Regular Tasks

1. **Update metal prices** in the database
2. **Backup database** regularly
3. **Monitor logs** for errors
4. **Update dependencies** as needed
5. **Review security** settings

### Monitoring

- Check `logs/` directory for error logs
- Monitor database performance
- Review Google Analytics data
- Test contact forms regularly

## 📞 Support

For technical support or customization requests, please contact the development team.

## 📄 License

This project is proprietary software developed for 14K Jewelry & Coin Exchange.

---

**Built with ❤️ for 14K Jewelry & Coin Exchange**