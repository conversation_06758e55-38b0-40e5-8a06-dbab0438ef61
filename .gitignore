# 14K Jewelry & Coin Exchange - Git Ignore File

# Environment and Configuration
.env
.env.local
.env.production
config/local.php
config/production.php

# Database
*.sql.backup
*.db
*.sqlite
*.sqlite3
database/backups/
database/dumps/

# Cache and Temporary Files
cache/
tmp/
temp/
/tmp/
*.tmp
*.cache
*_cache/
14k_cache/

# Logs
logs/
*.log
error.log
access.log
debug.log

# Session Files
sessions/
*.sess

# Uploads and User Content
uploads/
user_uploads/
images/uploads/
assets/images/uploads/

# IDE and Editor Files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Node.js (if using build tools)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# Composer (PHP dependency manager)
vendor/
composer.lock

# Build and Distribution
dist/
build/
*.min.js
*.min.css

# Backup Files
*.backup
*.bak
*.old
*_backup
backup_*

# Security and Sensitive Files
*.key
*.pem
*.p12
*.pfx
private/
secrets/

# System Files
.htaccess.backup
.htpasswd
wp-config.php

# Development Tools
.phpunit.result.cache
phpunit.xml
.phpcs.xml
.phpmd.xml

# Documentation Build
docs/_build/
docs/build/

# Local Development
local/
dev/
development/

# Archive Files
*.zip
*.tar
*.tar.gz
*.rar
*.7z

# Image Thumbnails
*_thumb.*
*_small.*
*_medium.*
*_large.*

# Temporary PHP Files
*.php~
*.php.bak

# Error Pages (if auto-generated)
50x.html
404.html

# Analytics and Tracking
analytics/
tracking/

# Third-party Libraries (if not using package manager)
lib/
libraries/
3rdparty/

# Local Configuration Examples
config.example.php
.env.example

# Test Files
tests/coverage/
coverage/
*.coverage

# Deployment Scripts
deploy.sh
deploy/
deployment/

# Local Database Setup
setup_database.sh.local
database_setup.local.sql

# Old Site Files (archived)
oldsite/
