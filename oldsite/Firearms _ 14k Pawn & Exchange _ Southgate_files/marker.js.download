google.maps.__gjsload__('marker', function(_){var BWa=function(a,b){const c=_.ra(b);a.Eg.set(c,b);_.ym(a.Fg)},CWa=function(a,b){if(a.Fg.has(b)){_.Zj(b,"UPDATE_BASEMAP_COLLISION");_.Zj(b,"UPDATE_MARKER_COLLISION");_.Zj(b,"REMOVE_COLLISION");a.Fg.delete(b);var c=a.Gg;const d=_.ra(b);c.Eg.has(d)&&(c.Eg.delete(d),b.Ln=null,_.ym(c.Fg));_.zea(a.Eg,b)}},DWa=function(a,b){a.Fg.has(b)||(a.Fg.add(b),_.Vj(b,"UPDATE_BASEMAP_COLLISION",()=>{a.Ig.add(b);a.Jg.ir()}),_.Vj(b,"UPDATE_MARKER_COLLISION",()=>{a.Jg.ir()}),_.Vj(b,"REMOVE_COLLISION",()=>{CWa(a,b)}),
BWa(a.Gg,b),_.yea(a.Eg,b))},EWa=function(a){return`${_.Rca(a)}-internal-${a}`},FWa=function(a,b){b=(a=a.__e3_)&&a[b];return!!b&&Object.values(b).some(c=>c.SB)},GWa=function(a,b,c){return new _.Uj(a,`${b}${"_removed"}`,c,0,!1)},HWa=function(a,b,c){return new _.Uj(a,`${b}${"_added"}`,c,0,!1)},aO=function(a){return a?_.fo.has(a)?_.fo.get(a):a instanceof ShadowRoot?aO(a.host):aO(a.parentNode):null},IWa=function(a){var b=1;return()=>{--b||a()}},JWa=function(a,b){_.wF().Eg.load(new _.WH(a),c=>{b(c&&c.size)})},
KWa=function(a,b){a=a.getBoundingClientRect();b=b instanceof Element?b.getBoundingClientRect():a;return{offset:new _.Zk(b.x-a.x,b.y-a.y),size:new _.al(b.width,b.height)}},LWa=function(a){a=new DOMMatrixReadOnly(a.transform);return{offsetX:a.m41,offsetY:a.m42}},bO=function(a){const b=window.devicePixelRatio||1;return Math.round(a*b)/b},MWa=function(a,{clientX:b,clientY:c}){const {height:d,left:e,top:f,width:g}=a.getBoundingClientRect();return{hh:bO(b-(e+g/2)),jh:bO(c-(f+d/2))}},NWa=function(a,b){if(!a||
!b)return null;a=a.getProjection();return _.bu(b,a)},OWa=function(a,b){const c=_.zI(a);if(!b||!c)return!1;a=Math.abs(c.clientX-b.clientX);b=Math.abs(c.clientY-b.clientY);return a*a+b*b>=4},cO=function(a="DEFAULT"){const b=document.createElementNS("http://www.w3.org/2000/svg","svg");b.setAttribute("xmlns","http://www.w3.org/2000/svg");var c=document.createElementNS("http://www.w3.org/2000/svg","defs"),d=document.createElementNS("http://www.w3.org/2000/svg","filter");d.setAttribute("id",_.Bk());var e=
document.createElementNS("http://www.w3.org/2000/svg","feFlood");e.setAttribute("result","floodFill");var f=document.createElementNS("http://www.w3.org/2000/svg","feComposite");f.setAttribute("in","floodFill");f.setAttribute("in2","SourceAlpha");f.setAttribute("operator","in");f.setAttribute("result","sourceAlphaFill");var g=document.createElementNS("http://www.w3.org/2000/svg","feComposite");g.setAttribute("in","sourceAlphaFill");g.setAttribute("in2","SourceGraphic");g.setAttribute("operator","in");
d.appendChild(e);d.appendChild(f);d.appendChild(g);c.appendChild(d);b.appendChild(c);c=document.createElementNS("http://www.w3.org/2000/svg","g");c.setAttribute("fill","none");c.setAttribute("fill-rule","evenodd");b.appendChild(c);g=document.createElementNS("http://www.w3.org/2000/svg","path");g.classList.add(_.VJ);d=document.createElementNS("http://www.w3.org/2000/svg","path");d.classList.add(_.UJ);d.setAttribute("fill","#EA4335");e=document.createElementNS("http://www.w3.org/2000/svg","image");
e.setAttribute("x","50%");e.setAttribute("y","50%");e.setAttribute("preserveAspectRatio","xMidYMid meet");f=document.createElementNS("http://www.w3.org/2000/svg","text");f.setAttribute("x","50%");f.setAttribute("y","50%");f.setAttribute("text-anchor","middle");f.style.font="inherit";f.style.fontSize="16px";switch(a){case "PIN":b.setAttribute("width","27");b.setAttribute("height","43");b.setAttribute("viewBox","0 0 27 43");c.setAttribute("transform","translate(1 1)");d.setAttribute("d","M12.5 0C5.596 0 0 5.596 0 12.5c0 1.886.543 3.746 1.441 5.462 3.425 6.615 10.216 13.566 10.216 22.195a.843.843 0 101.686 0c0-8.63 6.79-15.58 10.216-22.195.899-1.716 1.442-3.576 1.442-5.462C25 5.596 19.405 0 12.5 0z");
g.setAttribute("d","M12.5-.5c7.18 0 13 5.82 13 13 0 1.9-.524 3.833-1.497 5.692-.916 1.768-1.018 1.93-4.17 6.779-4.257 6.55-5.99 10.447-5.99 15.187a1.343 1.343 0 11-2.686 0c0-4.74-1.733-8.636-5.99-15.188-3.152-4.848-3.254-5.01-4.169-6.776C.024 16.333-.5 14.4-.5 12.5c0-7.18 5.82-13 13-13z");g.setAttribute("stroke","#fff");c.append(d,g);f.style.transform="translate(-1px, -3px)";break;case "PINLET":b.setAttribute("width","19");b.setAttribute("height","26");b.setAttribute("viewBox","0 0 19 26");d.setAttribute("d",
"M18.998 9.5c0 1.415-.24 2.819-.988 4.3-2.619 5.186-7.482 6.3-7.87 11.567-.025.348-.286.633-.642.633-.354 0-.616-.285-.641-.633C8.469 20.1 3.607 18.986.987 13.8.24 12.319 0 10.915 0 9.5 0 4.24 4.25 0 9.5 0a9.49 9.49 0 019.498 9.5z");a=document.createElementNS("http://www.w3.org/2000/svg","path");a.setAttribute("d","M-1-1h21v30H-1z");c.append(d,a);f.style.fontSize="14px";f.style.transform="translateY(1px)";break;default:b.setAttribute("width","26"),b.setAttribute("height","37"),b.setAttribute("viewBox",
"0 0 26 37"),g.setAttribute("d","M13 0C5.8175 0 0 5.77328 0 12.9181C0 20.5733 5.59 23.444 9.55499 30.0784C12.09 34.3207 11.3425 37 13 37C14.7225 37 13.975 34.2569 16.445 30.1422C20.085 23.8586 26 20.6052 26 12.9181C26 5.77328 20.1825 0 13 0Z"),g.setAttribute("fill","#C5221F"),d.setAttribute("d","M13.0167 35C12.7836 35 12.7171 34.9346 12.3176 33.725C11.9848 32.6789 11.4854 31.0769 10.1873 29.1154C8.92233 27.1866 7.59085 25.6173 6.32594 24.1135C3.36339 20.5174 1 17.7057 1 12.6385C1.03329 6.19808 6.39251 1 13.0167 1C19.6408 1 25 6.23078 25 12.6385C25 17.7057 22.6699 20.55 19.6741 24.1462C18.4425 25.65 17.1443 27.2193 15.8793 29.1154C14.6144 31.0442 14.0818 32.6135 13.749 33.6596C13.3495 34.9346 13.2497 35 13.0167 35Z"),
a=document.createElementNS("http://www.w3.org/2000/svg","path"),a.classList.add(_.WJ),a.setAttribute("d","M13 18C15.7614 18 18 15.7614 18 13C18 10.2386 15.7614 8 13 8C10.2386 8 8 10.2386 8 13C8 15.7614 10.2386 18 13 18Z"),a.setAttribute("fill","#B31412"),c.append(g,d,a)}c.append(e,f);return b},PWa=function(a,b){const c=[];c.push("@-webkit-keyframes ",b," {\n");_.xb(a.frames,d=>{c.push(d.time*100+"% { ");c.push("-webkit-transform: translate3d("+d.translate[0]+"px,",d.translate[1]+"px,0); ");c.push("-webkit-animation-timing-function: ",
d.pm,"; ");c.push("}\n")});c.push("}\n");return c.join("")},QWa=function(a,b){for(let c=0;c<a.frames.length-1;c++){const d=a.frames[c+1];if(b>=a.frames[c].time&&b<d.time)return c}return a.frames.length-1},RWa=function(a){if(a.Eg)return a.Eg;a.Eg="_gm"+Math.round(Math.random()*1E4);var b=PWa(a,a.Eg);dO||(dO=_.yi("style"),dO.type="text/css",document.querySelectorAll("HEAD")[0].appendChild(dO));b=dO.textContent+b;b=_.Ei(b);dO.textContent=_.Of(new _.Nf(b));return a.Eg},eO=function(a){switch(a){case 1:_.Sk(window,
"Pegh");_.Q(window,160667);break;case 2:_.Sk(window,"Psgh");_.Q(window,160666);break;case 3:_.Sk(window,"Pugh");_.Q(window,160668);break;default:_.Sk(window,"Pdgh"),_.Q(window,160665)}},fO=function(a){_.hk(a,"changed")},SWa=function(a){a.Wg&&a.Wg.setAttribute("fill",a.Pg||a.Yg);a.Gg.style.color=a.glyphColor||"";a.Ah.removeAttribute("flood-color");a.Jg.removeAttribute("filter");a.glyph instanceof URL&&(a.glyphColor&&(a.Ah.setAttribute("flood-color",a.glyphColor),a.Jg.setAttribute("filter",`url(#${a.Rh})`)),
a.Jg.href.baseVal=a.Fg.toString());a.Zg.setAttribute("fill",a.glyphColor||a.Yg)},hO=function(a){return a instanceof gO},TWa=function(a){a=a.get("collisionBehavior");return a==="REQUIRED_AND_HIDES_OPTIONAL"||a==="OPTIONAL_AND_HIDES_LOWER_PRIORITY"},UWa=function(a,b,c=!1){if(!b.get("internalMarker")){_.Sk(a,"Om");_.Q(a,149055);c?(_.Sk(a,"Wgmk"),_.Q(a,149060)):a instanceof _.Ck?(_.Sk(a,"Ramk"),_.Q(a,149057)):a instanceof _.jl&&(_.Sk(a,"Svmk"),_.Q(a,149059),a.get("standAlone")&&(_.Sk(a,"Ssvmk"),_.Q(a,
149058)));c=a.get("styles")||[];Array.isArray(c)&&c.some(e=>"stylers"in e)&&(_.Sk(a,"Csmm"),_.Q(a,174113));TWa(b)&&(_.Sk(a,"Mocb"),_.Q(a,149062));b.get("anchorPoint")&&(_.Sk(a,"Moap"),_.Q(a,149064));c=b.get("animation");c===1&&(_.Sk(a,"Moab"),_.Q(a,149065));c===2&&(_.Sk(a,"Moad"),_.Q(a,149066));b.get("clickable")===!1&&(_.Sk(a,"Ucmk"),_.Q(a,149091),b.get("title")&&(_.Sk(a,"Uctmk"),_.Q(a,149063)));b.get("draggable")&&(_.Sk(a,"Drmk"),_.Q(a,149069),b.get("clickable")===!1&&(_.Sk(a,"Dumk"),_.Q(a,149070)));
b.get("visible")===!1&&(_.Sk(a,"Ivmk"),_.Q(a,149081));b.get("crossOnDrag")&&(_.Sk(a,"Mocd"),_.Q(a,149067));b.get("cursor")&&(_.Sk(a,"Mocr"),_.Q(a,149068));b.get("label")&&(_.Sk(a,"Molb"),_.Q(a,149080));b.get("title")&&(_.Sk(a,"Moti"),_.Q(a,149090));b.get("opacity")!=null&&(_.Sk(a,"Moop"),_.Q(a,149082));b.get("optimized")===!0?(_.Sk(a,"Most"),_.Q(a,149085)):b.get("optimized")===!1&&(_.Sk(a,"Mody"),_.Q(a,149071));b.get("zIndex")!=null&&(_.Sk(a,"Mozi"),_.Q(a,149092));c=b.get("icon");var d=new iO;(d=
!c||c===d.icon.url||c.url===d.icon.url)?(_.Sk(a,"Dmii"),_.Q(a,173084)):(_.Sk(a,"Cmii"),_.Q(a,173083));typeof c==="string"?(_.Sk(a,"Mosi"),_.Q(a,149079)):c&&c.url!=null?(c.anchor&&(_.Sk(a,"Moia"),_.Q(a,149074)),c.labelOrigin&&(_.Sk(a,"Moil"),_.Q(a,149075)),c.origin&&(_.Sk(a,"Moio"),_.Q(a,149076)),c.scaledSize&&(_.Sk(a,"Mois"),_.Q(a,149077)),c.size&&(_.Sk(a,"Moiz"),_.Q(a,149078))):c&&c.path!=null?(c=c.path,c===0?(_.Sk(a,"Mosc"),_.Q(a,149088)):c===1?(_.Sk(a,"Mosfc"),_.Q(a,149072)):c===2?(_.Sk(a,"Mosfo"),
_.Q(a,149073)):c===3?(_.Sk(a,"Mosbc"),_.Q(a,149086)):c===4?(_.Sk(a,"Mosbo"),_.Q(a,149087)):(_.Sk(a,"Mosbu"),_.Q(a,149089))):hO(c)&&(_.Sk(a,"Mpin"),_.Q(a,149083));b.get("shape")&&(_.Sk(a,"Mosp"),_.Q(a,149084),d&&(_.Sk(a,"Dismk"),_.Q(a,162762)));if(c=b.get("place"))c.placeId?(_.Sk(a,"Smpi"),_.Q(a,149093)):(_.Sk(a,"Smpq"),_.Q(a,149094)),b.get("attribution")&&(_.Sk(a,"Sma"),_.Q(a,149061))}},jO=function(a){return hO(a)?a.getSize():a.size},VWa=function(a,b){if(!(a&&b&&a.isConnected&&b.isConnected))return!1;
a=a.getBoundingClientRect();b=b.getBoundingClientRect();return b.x+b.width<a.x-0||b.x>a.x+a.width+0||b.y+b.height<a.y-0||b.y>a.y+a.height+0?!1:!0},XWa=function(a,b,c){WWa(a,c,d=>{a.set(b,d);const e=d?jO(d):null;b==="viewIcon"&&d&&e&&a.Eg&&a.Eg(e,d.anchor,d.labelOrigin);d=a.get("modelLabel");a.set("viewLabel",d?{text:d.text||d,color:_.bj(d.color,"#000000"),fontWeight:_.bj(d.fontWeight,""),fontSize:_.bj(d.fontSize,"14px"),fontFamily:_.bj(d.fontFamily,"Roboto,Arial,sans-serif"),className:d.className||
""}:null)})},WWa=function(a,b,c){b?hO(b)?c(b):b.path!=null?c(a.Fg(b)):(_.dj(b)||(b.size=b.size||b.scaledSize),b.size?c(b):(b.url||(b={url:b}),JWa(b.url,d=>{b.size=d||new _.al(24,24);c(b)}))):c(null)},YWa=function(a){const b=a.get("mapPixelBoundsQ");var c=a.get("icon");const d=a.get("position");if(!b||!c||!d)return a.get("visible")!==!1;const e=c.anchor||_.ml,f=c.size.width+Math.abs(e.x);c=c.size.height+Math.abs(e.y);return d.x>b.minX-f&&d.y>b.minY-c&&d.x<b.maxX+f&&d.y<b.maxY+c?a.get("visible")!==
!1:!1},ZWa=function(a,b){a.origin=b;_.ym(a.Fg)},kO=function(a){a.Eg&&(_.uu(a.Eg),a.Eg=null)},$Wa=function(a,b,c){_.yF(()=>{a.style.webkitAnimationDuration=c.duration?c.duration+"ms":"";a.style.webkitAnimationIterationCount=`${c.im}`;a.style.webkitAnimationName=b||""})},aXa=function(){const a=[];for(let b=0;b<lO.length;b++){const c=lO[b];c.tick();c.Eg||a.push(c)}lO=a;lO.length===0&&(window.clearInterval(mO),mO=null)},nO=function(a){return a?a.__gm_at||_.ml:null},cXa=function(a,b){var c=1,d=a.animation;
var e=d.frames[QWa(d,b)];var f;d=a.animation;(f=d.frames[QWa(d,b)+1])&&(c=(b-e.time)/(f.time-e.time));b=nO(a.element);d=a.element;f?(c=(0,bXa[e.pm||"linear"])(c),e=e.translate,f=f.translate,c=new _.Zk(Math.round(c*f[0]-c*e[0]+e[0]),Math.round(c*f[1]-c*e[1]+e[1]))):c=new _.Zk(e.translate[0],e.translate[1]);c=d.__gm_at=c;d=c.x-b.x;b=c.y-b.y;if(d!==0||b!==0)c=a.element,e=new _.Zk(_.vF(c.style.left)||0,_.vF(c.style.top)||0),e.x+=d,e.y+=b,_.mu(c,e);_.hk(a,"tick")},fXa=function(a,b,c){let d;var e;if(e=
c.dG!==!1)e=_.Om(),e=e.Eg.Mg||e.Eg.Kg&&_.Js(e.Eg.version,7);e?d=new dXa(a,b,c):d=new eXa(a,b,c);d.start();return d},qO=function(a){a.Jg&&(oO(a.Ih),a.Jg.release(),a.Jg=null);a.Eg&&_.uu(a.Eg);a.Eg=null;a.Ig&&_.uu(a.Ig);a.Ig=null;pO(a,!0);a.Mg=[]},pO=function(a,b=!1){a.Qg?a.Wg=!0:(_.hk(a,b?"ELEMENTS_REMOVED":"CLEAR_TARGET"),a.targetElement&&_.uu(a.targetElement),a.targetElement=null,a.Kg&&(a.Kg.unbindAll(),a.Kg.release(),a.Kg=null,oO(a.Sg),a.Sg=null),a.Ng&&a.Ng.remove(),a.Og&&a.Og.remove())},hXa=function(a,
b){const c=a.Zg();if(c){var d=c.url!=null;a.Eg&&a.rh==d&&(_.uu(a.Eg),a.Eg=null);a.rh=!d;var e=null;d&&(e={js:()=>{}});a.Eg=rO(a,b,a.Eg,c,e);gXa(a,c,sO(a))}},lXa=function(a){var b=a.gh();if(b){if(!a.Jg){const e=a.Jg=new iXa(a.getPanes(),b,a.get("opacity"),a.get("visible"),a.vi);a.Ih=[_.Vj(a,"label_changed",function(){e.setLabel(this.get("label"))}),_.Vj(a,"opacity_changed",function(){e.setOpacity(this.get("opacity"))}),_.Vj(a,"panes_changed",function(){var f=this.get("panes");e.vl=f;kO(e);_.ym(e.Fg)}),
_.Vj(a,"visible_changed",function(){e.setVisible(this.get("visible"))})]}if(b=a.Zg()){var c=a.Eg,d=sO(a);c=jXa(a,b,d,nO(c)||_.ml);d=jO(b);d=b.labelOrigin||new _.Zk(d.width/2,d.height/2);hO(b)&&(b=b.getSize().width,d=new _.Zk(b/2,b/2));ZWa(a.Jg,new _.Zk(c.x+d.x,c.y+d.y));a.Jg.setZIndex(kXa(a));_.zm(a.Jg.Fg)}}},nXa=function(a){if(!a.Vg){a.Gg&&(a.Pg&&_.Xj(a.Pg),a.Gg.cancel(),a.Gg=null);var b=a.get("animation");if(b=mXa[b]){var c=b.options;a.Eg&&(a.Vg=!0,a.set("animating",!0),b=fXa(a.Eg,b.icon,c),a.Gg=
b,a.Pg=_.ek(b,"done",function(){a.set("animating",!1);a.Gg=null;a.set("animation",null)}))}}},oO=function(a){if(a)for(let b=0,c=a.length;b<c;b++)_.Xj(a[b])},sO=function(a){return _.Om().transform?Math.min(1,a.get("scale")||1):1},jXa=function(a,b,c,d){const e=a.getPosition(),f=jO(b);var g=(b=tO(b))?b.x:f.width/2;a.kh.x=e.x+d.x-Math.round(g-(g-f.width/2)*(1-c));b=b?b.y:f.height;a.kh.y=e.y+d.y-Math.round(b-(b-f.height/2)*(1-c));return a.kh},kXa=function(a){let b=a.get("zIndex");a.Ek&&(b=1E6);_.$i(b)||
(b=Math.min(a.getPosition().y,999999));return b},tO=function(a){return hO(a)?a.getAnchor():a.anchor},gXa=function(a,b,c){const d=jO(b);a.Xg.width=c*d.width;a.Xg.height=c*d.height;a.set("size",a.Xg);const e=a.get("anchorPoint");if(!e||e.Eg)b=tO(b),a.Rg.x=c*(b?d.width/2-b.x:0),a.Rg.y=-c*(b?b.y:d.height),a.Rg.Eg=!0,a.set("anchorPoint",a.Rg)},rO=function(a,b,c,d,e){if(hO(d))a=oXa(a,b,c,d);else if(d.url!=null){const f=d.origin||_.ml;a=a.get("opacity");const g=_.bj(a,1);c?(c.firstChild.__src__!=d.url&&
_.YH(c.firstChild,d.url),_.$H(c,d.size,f,d.scaledSize),c.firstChild.style.opacity=`${g}`):(e=e||{},e.Sz=!_.Mm.Lg,e.alpha=!0,e.opacity=a,c=_.ZH(d.url,null,f,d.size,null,d.scaledSize,e),_.DF(c),b.appendChild(c));a=c}else b=c||_.nu("div",b),b.textContent="",c=_.vo(),e=_.iu(b).createElement("canvas"),e.width=d.size.width*c,e.height=d.size.height*c,e.style.width=_.gj(d.size.width),e.style.height=_.gj(d.size.height),_.Pm(b,d.size),b.appendChild(e),_.mu(e,_.ml),_.Sm(e),e=e.getContext("2d"),e.lineCap=e.lineJoin=
"round",e.scale(c,c),c=new _.hJa(e),e.beginPath(),c.Nh(d.oF,d.anchor.x,d.anchor.y,d.rotation||0,d.scale),d.fillOpacity&&(e.fillStyle=d.fillColor,e.globalAlpha=d.fillOpacity,e.fill()),d.strokeWeight&&(e.lineWidth=d.strokeWeight,e.strokeStyle=d.strokeColor,e.globalAlpha=d.strokeOpacity,e.stroke()),_.FF(b,_.bj(a.get("opacity"),1)),a=b;c=a;c.Mg=d;return c},pXa=function(a,b){a.Ng&&a.Og&&a.ph==b||(a.ph=b,a.Ng&&a.Ng.remove(),a.Og&&a.Og.remove(),a.Ng=_.Qv(b,{tk:function(c){a.Qg++;_.Av(c);_.hk(a,"mousedown",
c.Eg)},Gk:function(c){a.Qg--;!a.Qg&&a.Wg&&_.xF(this,function(){a.Wg=!1;pO(a);_.zm(a.Lg)},0);_.Cv(c);_.hk(a,"mouseup",c.Eg)},Ql:({event:c,Aq:d})=>{if(a.get("clickable")!==!1||a.getDraggable())_.Vt(c.Eg),c.button==3?d||c.button==3&&_.hk(a,"rightclick",c.Eg):d?_.hk(a,"dblclick",c.Eg):(_.hk(a,"click",c.Eg),_.Sk(window,"Mmi"),_.Q(window,171150))},Kt:c=>{_.Dv(c);_.hk(a,"contextmenu",c.Eg)}}),a.Og=new _.Mz(b,b,{ks:function(c){_.hk(a,"mouseout",c)},ls:function(c){_.hk(a,"mouseover",c)}}))},oXa=function(a,
b,c,d){c=c||_.nu("div",b);_.ao(c);b===a.getPanes().overlayMouseTarget?(b=d.element.cloneNode(!0),_.FF(b,0),c.appendChild(b)):c.appendChild(d.element);b=d.getSize();c.style.width=b.width+(b.Fg||"px");c.style.height=b.height+(b.Eg||"px");c.style.pointerEvents="none";c.style.userSelect="none";_.ek(d,"changed",()=>{a.Fg()});return c},uO=function(a){const b=a.marker.get("place");a=a.marker.get("position");return b&&b.location||a},vO=function(a,b){a.Ig&&a.Ig.has(b)&&({marker:a}=a.Ig.get(b),b.xm=qXa(a),
b.xm&&(b=a.getMap()))&&(_.Sk(b,"Mwfl"),_.Q(b,184438))},sXa=function(a,b){if(a.Ig){var {YD:c,marker:d}=a.Ig.get(b);for(const e of rXa)c.push(HWa(d,e,()=>{vO(a,b)})),c.push(GWa(d,e,()=>{!qXa(d)&&b.xm&&vO(a,b)}))}},tXa=function(a){const b=a.Fg.__gm;a.Eg.bindTo("mapPixelBounds",b,"pixelBounds");a.Eg.bindTo("panningEnabled",a.Fg,"draggable");a.Eg.bindTo("panes",b)},uXa=function(a){const b=a.Fg.__gm;_.Vj(a.Ng,"dragging_changed",()=>{b.set("markerDragging",a.marker.get("dragging"))});b.set("markerDragging",
b.get("markerDragging")||a.marker.get("dragging"))},wXa=function(a){a.Kg.push(_.gk(a.Eg,"panbynow",a.Fg.__gm));vXa.forEach(b=>{a.Kg.push(_.Vj(a.Eg,b,c=>{const d=a.Og?uO(a):a.marker.get("internalPosition");c=new _.Nz(d,c,a.Eg.get("position"));_.hk(a.marker,b,c)}))})},xXa=function(a){const b=()=>{a.marker.get("place")?a.Eg.set("draggable",!1):a.Eg.set("draggable",!!a.marker.get("draggable"))};a.Kg.push(_.Vj(a.Ng,"draggable_changed",b));a.Kg.push(_.Vj(a.Ng,"place_changed",b));b()},yXa=function(a){a.Kg.push(_.Vj(a.Fg,
"projection_changed",()=>{wO(a)}));a.Kg.push(_.Vj(a.Ng,"position_changed",()=>{wO(a)}));a.Kg.push(_.Vj(a.Ng,"place_changed",()=>{wO(a)}))},AXa=function(a){a.Kg.push(_.Vj(a.Eg,"dragging_changed",()=>{if(a.Eg.get("dragging"))a.Rg=a.Jg.en(),a.Rg&&_.cJ(a.Jg,a.Rg);else{a.Rg=null;a.Qg=null;var b=a.Jg.getPosition();if(b&&(b=_.Nl(b,a.Fg.get("projection")),b=zXa(a,b))){const c=_.bu(b,a.Fg.get("projection"));a.marker.get("place")||(a.Pg=!1,a.marker.set("position",b),a.Pg=!0);a.Jg.setPosition(c)}}}));a.Kg.push(_.Vj(a.Eg,
"deltaclientposition_changed",()=>{var b=a.Eg.get("deltaClientPosition");if(b&&(a.Rg||a.Qg)){var c=a.Qg||a.Rg;a.Qg={clientX:c.clientX+b.clientX,clientY:c.clientY+b.clientY};b=a.ah.Ll(a.Qg);b=_.Nl(b,a.Fg.get("projection"));c=a.Qg;var d=zXa(a,b);d&&(a.marker.get("place")||(a.Pg=!1,a.marker.set("position",d),a.Pg=!0),d.equals(b)||(b=_.bu(d,a.Fg.get("projection")),c=a.Jg.en(b)));c&&_.cJ(a.Jg,c)}}))},BXa=function(a){if(a.Gg){a.Eg.bindTo("scale",a.Gg);a.Eg.bindTo("position",a.Gg,"pixelPosition");const b=
a.Fg.__gm;a.Gg.bindTo("latLngPosition",a.marker,"internalPosition");a.Gg.bindTo("focus",a.Fg,"position");a.Gg.bindTo("zoom",b);a.Gg.bindTo("offset",b);a.Gg.bindTo("center",b,"projectionCenterQ");a.Gg.bindTo("projection",a.Fg)}},DXa=function(a){if(a.Gg){const b=new CXa(a.Fg instanceof _.jl);b.bindTo("internalPosition",a.Gg,"latLngPosition");b.bindTo("place",a.marker);b.bindTo("position",a.marker);b.bindTo("draggable",a.marker);a.Eg.bindTo("draggable",b,"actuallyDraggable")}},wO=function(a){if(a.Pg){var b=
uO(a);b&&a.Jg.setPosition(_.bu(b,a.Fg.get("projection")))}},zXa=function(a,b){const c=a.Fg.__gm.get("snappingCallback");return c&&(a=c({latLng:b,overlay:a.marker}))?a:b},qXa=function(a){return rXa.some(b=>FWa(a,b))},FXa=function(a,b,c){if(b instanceof _.Ck){const d=b.__gm;Promise.all([d.Fg,d.Gg]).then(([{ah:e},f])=>{EXa(a,b,c,e,f)})}else EXa(a,b,c,null)},EXa=function(a,b,c,d,e=!1){const f=new Map,g=h=>{var l=b instanceof _.Ck;const n=l?h.__gm.Lq.map:h.__gm.Lq.streetView,p=n&&n.Fg===b,r=p!==a.contains(h);
n&&r&&(l?(h.__gm.Lq.map.dispose(),h.__gm.Lq.map=null):(h.__gm.Lq.streetView.dispose(),h.__gm.Lq.streetView=null));!a.contains(h)||!l&&h.get("mapOnly")||p||(b instanceof _.Ck?(l=b.__gm,h.__gm.Lq.map=new GXa(h,b,c,_.UI(l,h),d,l.Sg,f)):h.__gm.Lq.streetView=new GXa(h,b,c,_.Xg,null,null,null),UWa(b,h,e))};_.Vj(a,"insert",g);_.Vj(a,"remove",g);a.forEach(g)},xO=function(a){return _.vo()/(a.webkitBackingStorePixelRatio||a.mozBackingStorePixelRatio||a.msBackingStorePixelRatio||a.oBackingStorePixelRatio||a.backingStorePixelRatio||
1)},HXa=function(a,b,c){a=a.Eg;a.width=b;a.height=c;return a},IXa=function(a){const b=[];a.Ji.forEach(c=>{b.push(c)});b.sort((c,d)=>c.zIndex-d.zIndex);return b},JXa=function(a){const b=IXa(a),c=a.getContext(),d=xO(c);a=a.Bh.size;c.clearRect(0,0,Math.ceil(a.hh*d),Math.ceil(a.jh*d));b.forEach(e=>{c.globalAlpha=_.bj(e.opacity,1);c.drawImage(e.image,e.Ay,e.By,e.zy,e.ty,Math.round(e.dx*d),Math.round(e.dy*d),e.nq*d,e.kq*d)})},KXa=function(a,b,c){if(c.dx>a||c.dy>b||c.dx+c.nq<a||c.dy+c.kq<b)a=!1;else a:{var d=
c.Dv.shape;a-=c.dx;b-=c.dy;if(!d)throw Error("Shape cannot be null.");c=d.coords||[];switch(d.type.toLowerCase()){case "rect":a=c[0]<=a&&a<=c[2]&&c[1]<=b&&b<=c[3];break a;case "circle":d=c[2];a-=c[0];b-=c[1];a=a*a+b*b<=d*d;break a;default:d=c,c=d.length,d[0]==d[c-2]&&d[1]==d[c-1]||d.push(d[0],d[1]),a=_.kHa(a,b,d)!=0}}return a},yO=function(a,b,c,d){var e=b.oi,f=a.Fg.get();if(!f)return null;f=f.Bh.size;c=_.dJ(a.Gg,e,new _.Zk(c,d));if(!c)return null;a=new _.Zk(c.pt.sh*f.hh,c.pt.th*f.jh);const g=[];c.dk.Ji.forEach(h=>
g.push(h));g.sort((h,l)=>l.zIndex-h.zIndex);c=null;for(e=0;d=g[e];++e)if(f=d.Dv,f.clickable!==!1&&(f=f.Jz,KXa(a.x,a.y,d))){c=f;break}c&&(b.mj=d);return c},MXa=function(a,b){if(!b.FA){b.FA=!0;var c=_.Ml(a.get("projection")),d=b.Bt;d.dx<-64||d.dy<-64||d.dx+d.nq>64||d.dy+d.kq>64?(_.Cm(a.Ig,b),d=a.Gg.search(_.Aq)):(d=b.latLng,d=new _.Zk(d.lat(),d.lng()),b.oi=d,_.XI(a.Jg,{oi:d,marker:b}),d=_.gHa(a.Gg,d));for(let f=0,g=d.length;f<g;++f){var e=d[f];const h=e.dk||null;if(e=LXa(a,h,e.XF||null,b,c))b.Ji[_.jk(e)]=
e,_.Cm(h.Ji,e)}}},NXa=function(a,b){b.FA&&(b.FA=!1,a.Ig.contains(b)?a.Ig.remove(b):a.Jg.remove({oi:b.oi,marker:b}),_.Si(b.Ji,(c,d)=>{delete b.Ji[c];d.dk.Ji.remove(d)}))},OXa=function(a,b){a.Kg[_.jk(b)]=b;var c={sh:b.mi.x,th:b.mi.y,xh:b.zoom};const d=_.Ml(a.get("projection"));var e=_.qx(a.Fg,c);e=new _.Zk(e.Eg,e.Fg);const {min:f,max:g}=_.GE(a.Fg,c,64/a.Fg.size.hh);c=_.Zl(f.Eg,f.Fg,g.Eg,g.Fg);_.jHa(c,d,e,(h,l)=>{h.XF=l;h.dk=b;b.Wo[_.jk(h)]=h;_.VI(a.Gg,h);l=(a.Jg.search(h)||[]).map(n=>n.marker);a.Ig.forEach((0,_.ta)(l.push,
l));for(let n=0,p=l.length;n<p;++n){const r=l[n],u=LXa(a,b,h.XF,r,d);u&&(r.Ji[_.jk(u)]=u,_.Cm(b.Ji,u))}});b.div&&b.Ji&&a.Mg(b.div,b.Ji)},PXa=function(a,b){b&&(delete a.Kg[_.jk(b)],b.Ji.forEach(function(c){b.Ji.remove(c);delete c.Dv.Ji[_.jk(c)]}),_.Si(b.Wo,(c,d)=>{a.Gg.remove(d)}))},LXa=function(a,b,c,d,e){if(!e||!c||!d.latLng)return null;var f=e.fromLatLngToPoint(c);c=e.fromLatLngToPoint(d.latLng);e=a.Fg.size;a=_.FCa(a.Fg,new _.gm(c.x,c.y),new _.gm(f.x,f.y),b.zoom);c.x=a.sh*e.hh;c.y=a.th*e.jh;a=d.zIndex;
_.$i(a)||(a=c.y);a=Math.round(a*1E3)+_.jk(d)%1E3;f=d.Bt;b={image:f.image,Ay:f.Ay,By:f.By,zy:f.zy,ty:f.ty,dx:f.dx+c.x,dy:f.dy+c.y,nq:f.nq,kq:f.kq,zIndex:a,opacity:d.opacity,dk:b,Dv:d};return b.dx>e.hh||b.dy>e.jh||b.dx+b.nq<0||b.dy+b.kq<0?null:b},QXa=function(a,b,c){a.Ig++<4?c?a.Fg.VC(b):a.Fg.DL(b):a.Eg=!0;a.bo||(a.bo=_.yF((0,_.ta)(a.Gg,a)))},RXa=function(a){return typeof a==="string"?(zO.has(a)||zO.set(a,{url:a}),zO.get(a)):a},XXa=function(a,b,c){const d=new _.Bm,e=new _.Bm,f=new SXa;new TXa(a,d,new iO,
f,c);const g=_.iu(b.getDiv()).createElement("canvas"),h={};a=_.Zl(-100,-300,100,300);const l=new _.$J(a);a=_.Zl(-90,-180,90,180);const n=_.iHa(a,(x,y)=>x.marker===y.marker);let p=null,r=null;const u=new _.hl(null),w=b.__gm;w.Fg.then(x=>{w.Kg.register(new UXa(h,w,u,x.ah.tj));_.Bs(x.sr,y=>{if(y&&p!==y.Bh){r&&r.unbindAll();var B=p=y.Bh;r=new VXa(h,d,e,function(D,G){return new WXa(G,new AO(D,G,g,B),D)},l,n,p);r.bindTo("projection",b);u.set(r.Eg())}})});_.eJ(b,u,"markerLayer",-1)},ZXa=function(a){a.bo||
(a.bo=_.yF(()=>{a.bo=0;const b=a.Ou;a.Ou={};const c=a.Tv;for(const d of Object.values(b))YXa(a,d);c&&!a.Tv&&a.nt.forEach(d=>{YXa(a,d)})}))},YXa=function(a,b){var c=b.get("place");c=c?c.location:b.get("position");b.set("internalPosition",c);b.changed=a.YK;if(!b.get("animating"))if(a.XB.remove(b),!c||b.get("visible")==0||b.__gm&&b.__gm.Ln)a.nt.remove(b);else{a.Tv&&!a.ZD&&a.nt.getSize()>=256&&(a.Tv=!1);c=b.get("optimized");const e=b.get("draggable"),f=!!b.get("animation");var d=b.get("icon");const g=
!!d&&d.path!=null;d=hO(d);const h=b.get("label")!=null;a.ZD||c==0||e||f||g||d||h||!c&&a.Tv?_.Cm(a.nt,b):(a.nt.remove(b),_.Cm(a.XB,b))}},$Xa=function(a,b){const c=new _.nm;c.onAdd=()=>{};c.onContextLost=()=>{};c.onRemove=()=>{};c.onContextRestored=()=>{};c.onDraw=({transformer:d})=>{a.onDraw(d)};_.Dq.add(c);c.setMap(b);return c},aYa=function(a){a.Lg||(a.Lg=setTimeout(()=>{const b=[...a.Ig].filter(c=>!c.xp).length;b>0&&a.Wi.Xg(a.map,b);a.Lg=0},0))},dYa=function(a,b){a.Jg.has(b)||(a.Jg.add(b),_.Mx(_.Lx(),
()=>{if(a.map){var c=[];for(const d of a.Jg){if(!d.map)continue;const e=d.targetElement;e.parentNode||c.push(d);const f=d.Ln!==!1&&BO(d)||d.tv,g=_.Dk(a.map);g||(a.Mg||(a.Mg=a.Fg.attachShadow({mode:_.Km[166]?"open":"closed"}),a.Mg.append(a.Kg,a.Eg)),a.Fg.append(e));g&&e.parentElement===g||!g&&e.parentElement===a.Fg?e.setAttribute("slot",f?bYa:cYa):e.style.visibility=f?"hidden":"";d.Wl(!f);d.Fv=!1}a.Jg.clear();for(const d of c)d.Gy(!0)}}))},eYa=function(a){CO||(CO=new ResizeObserver(b=>{for(const c of b)c.target.dispatchEvent(new CustomEvent("resize",
{detail:c.contentRect}))}));CO.observe(a)},gYa=function(a,b){const c=_.ra(b);let d=DO.get(c);d||(d=new fYa(b),DO.set(c,d));b=d;a.Kw(b.tm);b.Ig.add(a);aYa(b);eYa(a.targetElement)},hYa=function(a){a=_.ra(a);(a=DO.get(a))&&a.requestRedraw()},iYa=function(a,b){b=_.ra(b);(b=DO.get(b))&&dYa(b,a)},jYa=function(a){let b=0,c=0;for(const d of a)switch(d){case "ArrowLeft":--b;break;case "ArrowRight":b+=1;break;case "ArrowDown":c+=1;break;case "ArrowUp":--c}return{deltaX:b,deltaY:c}},FO=function(a,b,c=!0){a.Eg.position=
a.Qg;EO(a,b,c)},EO=function(a,b,c=!0){b.preventDefault();b.stopImmediatePropagation();GO(a);kYa(a);a.Gg&&(a.Gg.release(),a.Gg=null);c&&HO(a.Eg,"dragend",b)},mYa=function(a){a.Fg.style.display="none";a.Fg.style.opacity="0.5";a.Fg.style.position="absolute";a.Fg.style.left="50%";a.Fg.style.transform="translate(-50%, -50%)";a.Fg.style.zIndex="-1";lYa(a);const b=a.Eg.Co;b.addEventListener("pointerenter",a.Ug);b.addEventListener("pointerleave",a.Vg);b.addEventListener("focus",a.Ug);b.addEventListener("blur",
a.Vg)},nYa=function(a,b=!1){return a.Ig?_.Vx:b?"pointer":_.Foa},IO=function(a){const b=a.Eg.ki;b&&b.appendChild(a.Fg)},lYa=function(a){a.Fg.children[0]?.remove();var b=a.Eg,c;if(!(c=b.dragIndicator)){if(!b.Lu){const {url:d,scaledSize:e}=(new iO).Eg;b.Lu=new Image(e.width,e.height);b.Lu.src=d;b.Lu.alt=""}c=b.Lu}a.Fg.appendChild(c);IO(a)},pYa=function(a){if(!a.Eg.Cz){a.Gg=new _.aK((c,d)=>{var e=a.Eg;e.Fh&&_.hk(e.Fh,"panbynow",c,d)});_.bJ(a.Gg,!0);var b=oYa(a.Eg);_.aJ(a.Gg,b);a.Gg.Ig=a.Jg}},qYa=function(a,
b){GO(a);a.Jg=!1;a.Gg&&(a.Gg.Ig=!1);a.Kg=a.Eg.en();a.Og=_.zI(b)},rYa=function(a,b){var c=_.zI(b);if(c){b=c.clientX;c=c.clientY;var d=b-a.Og.clientX,e=c-a.Og.clientY;a.Og={clientX:b,clientY:c};b={clientX:a.Kg.clientX+d,clientY:a.Kg.clientY+e};a.Kg=b;a.Eg.LB(b)}},sYa=function(a,b){a.Kg=a.Eg.en();a.Qg=a.Eg.position;a.Og=_.zI(b);a.Ig=!0;pYa(a);a.Eg.Co.setAttribute("aria-grabbed","true");JO(a.Eg);a.Eg.Co.style.zIndex="2147483647";a.Fg.style.opacity="1";a.Fg.style.display="";HO(a.Eg,"dragstart",b)},tYa=
function(a){a.Jg&&(a.Kg=a.Eg.en())},KO=function(a){_.Pv!==2?(document.removeEventListener("pointermove",a.Sg),document.removeEventListener("pointerup",a.Lg),document.removeEventListener("pointercancel",a.Lg)):(document.removeEventListener("touchmove",a.Sg,{passive:!1}),document.removeEventListener("touchend",a.Lg),document.removeEventListener("touchcancel",a.Lg),document.removeEventListener("touchstart",a.Lg));GO(a);kYa(a);a.Gg&&(a.Gg.release(),a.Gg=null)},GO=function(a){const b=a.Eg.Co;b.removeEventListener("keydown",
a.lh);b.removeEventListener("keyup",a.ph);b.removeEventListener("blur",a.kh)},uYa=function(a){if(a.Rg.size===0)a.Pg=0;else{var {deltaX:b,deltaY:c}=jYa(a.Rg),d=1;_.HI(a.Wg)&&(d=a.Wg.next());var e=Math.round(3*d*b);d=Math.round(3*d*c);e===0&&(e=b);d===0&&(d=c);e={clientX:a.Kg.clientX+e,clientY:a.Kg.clientY+d};a.Kg=e;a.Eg.LB(e);a.Pg=window.setTimeout(()=>{uYa(a)},10)}},kYa=function(a){a.Ig=!1;a.Jg=!1;a.Og=null;a.Kg=null;clearTimeout(a.Pg);a.Pg=0;a.Qg=null;a.Xg=null;a.Ng=null;const b=a.Eg.Co,c=a.Eg.zIndex;
a.Fg.style.opacity="0.5";b.setAttribute("aria-grabbed","false");b.style.zIndex=c==null?"":`${c}`;vYa(a.Eg)},BO=function(a){return a.collisionBehavior!=="REQUIRED"&&!a.Ek&&!!a.map&&!!a.position},oYa=function(a){return a.Fh?a.Fh.get("pixelBounds"):null},HO=function(a,b,c){_.hk(a,b,new _.Nz(a.Oo,c,a.yv?new _.Zk(a.yv.hh,a.yv.jh):null))},JO=function(a){_.hk(a,"REMOVE_COLLISION")},vYa=function(a){a.element.style.cursor=a.Mi?nYa(a.Mi,a.qv):a.qv?"pointer":""},LO=function(a,b=!1){BO(a)&&(a.Fh&&DWa(a.Fh.Vg,
a),_.hk(a,"UPDATE_MARKER_COLLISION"),b&&a.yw&&_.hk(a,"UPDATE_BASEMAP_COLLISION"))},wYa=function(a){a.Yq.then(()=>{_.el(a.element,"marker-view");a.element.style.position="absolute";a.element.style.left="0px"})},MO=function(a){a.style.pointerEvents=a.Mx?"none":a.FE?"auto":""},NO=function(a){a.xm=a.qv||!!a.hu},xYa=function(a,b){var c;if(c=a.Mi)c=a.Mi,c=c.Ng&&b.timeStamp-c.Ng>=500?!0:c.Mg;if(!c&&a.Oo){a.gmpDraggable||a.element.focus();HO(a,"click",b);if(a.gmpClickable||_.Wj(a,"gmp-click"))c=new OO,_.hk(a,
"gmp-click",c),a.element.dispatchEvent(c);a.Wi.Mg(b)}},yYa=function(a){!a.Xj&&a.map&&a.Fh&&(a.RB=!0,a.Xj=_.Qv(a.element,{Ql:({event:b,Aq:c})=>{a.FE?(_.Vt(b.Eg),b.button===3||c||xYa(a,b.Eg)):a.element===b.Eg.target||a.Mx||(console.debug('To make AdvancedMarkerElement clickable and provide better accessible experiences, use addListener() to register a "click" event on the AdvancedMarkerElement instance.'),a.Wi.Og(a.map))}}),a.Tz=_.qma({draggable:a.rE,FD:new _.eA(a.map,"gestureHandling"),rk:a.Fh.pl}),
_.Bs(a.Tz,a.eE),a.RB=!1)},PO=function(a){const b=c=>c.nodeType===Node.TEXT_NODE&&c.nodeValue!=null&&!/\S/.test(c.nodeValue);return a.childNodes.length>0?([...a.childNodes].every(b)&&_.Pj(_.Jl(a,"AdvancedMarkerElement is displaying empty text content. If you want a pin to appear, make sure to remove any whitespace between the <gmp-advanced-marker> tags.")),[...a.childNodes]):a.Ok&&a.Ok.contains(a.kl)?[a.kl]:[]},zYa=function(a,b,c){if(b&&c&&({altitude:b}=new _.Qp(b),b>0||b<0))throw a.Wi.Pg(window),
_.pj("Draggable AdvancedMarkerElement with non-zero altitude is not supported");},QO=function(a){if(a.Jj){const b=_.ra(a.Jj),c=DO.get(b);c&&(c.Ig.delete(a),c.isEmpty()&&(c.dispose(),DO.delete(b)));CO&&CO.unobserve(a.targetElement);_.hk(a,"REMOVE_FOCUS");_.hk(a,"REMOVE_COLLISION");a.ah&&(a.Hj&&(a.ah.xl(a.Hj),a.Hj=null),a.ah=null);a.Mi&&KO(a.Mi);a.lD?.remove();a.wG?.remove();a.sF?.remove();a.WE?.remove();a.Tz?.removeListener(a.eE);a.Xj&&(a.Xj.remove(),a.Xj=null);a.yq.set("map",null);a.yw=null;a.Fh=
null;a.Jj=null;a.Fv=!0}},RO=function(a){if(a.Fh&&!a.Ek){var b=a.Fh.Sg;b&&(a.xm&&a.Rp&&!a.Ln?b.Sg(a):_.hk(a,"REMOVE_FOCUS"))}},BYa=function(a){var b=a.Fh.get("baseMapType");b=b&&(!b.mapTypeId||!Object.values(_.xp).includes(b.mapTypeId));a.yw=a.HE&&!b;if(!a.xp||a.position)a.yw?hYa(a.map):AYa(a)},CYa=function(a){if(!a.xp){var b=a.Fh.Eg;b.OA.then(()=>{const c=_.jm(b,"ADVANCED_MARKERS");if(!c.isAvailable){a.Fh&&a.Fh.ph();for(const d of c.Eg)b.log(d);a.Wi.Ng(a.map);a.dispose()}})}},DYa=function(a){a.Wi.Ug(a.map);
a.Wi.Ig(a.map,a.Mx);if(a.qv){const b=_.Wj(a,"gmp-click");a.Wi.Fg(a.map,b)}a.gmpDraggable&&a.Wi.Jg(a.map);a.title&&a.Wi.Kg(a.map);a.zIndex!==null&&a.Wi.Lg(a.map);a.Vk()>0&&a.Wi.Eg(a.map);a.Wi.Gg(a.map,a.collisionBehavior)},SO=function(a,b){a.Rp=b;a.Mi&&tYa(a.Mi);a.yq.set("pixelPosition",b);if(b){a.element.style.transform=`translate(-50%, -100%) translate(${b.x}px, ${b.y}px)`;const c=a.element.style.willChange?a.element.style.willChange.replace(/\s+/g,"").split(","):[];c.includes("transform")||_.Mx(_.Lx(),
()=>{c.push("transform");a.element.style.willChange=c.join(",")},a,a)}RO(a)},AYa=function(a){var b=NWa(a.Jj,a.Oo);a.Hj?a.Hj.setPosition(b,a.Vk()):a.ah&&(b=new _.cK(a.ah.tj,a,b,a.ah,null,a.Vk(),a.jJ),a.ah.Li(b),a.Hj=b)};_.Zk.prototype.Ux=_.da(17,function(){return Math.sqrt(this.x*this.x+this.y*this.y)});
var rXa=["click","dblclick","rightclick","contextmenu"],EYa={DEFAULT:"DEFAULT",nO:"PIN",oO:"PINLET"},FYa=class extends _.kk{constructor(){super();this.constraint=0;this.Eg=!1}position_changed(){this.Eg||(this.Eg=!0,this.set("rawPosition",this.get("position")),this.Eg=!1)}rawPosition_changed(){if(!this.Eg){this.Eg=!0;var a=this.set,b;var c=this.get("rawPosition");if(c){(b=this.get("snappingCallback"))&&(c=b(c));b=c.x;c=c.y;var d=this.get("referencePosition");d&&(this.constraint===2?b=d.x:this.constraint===
1&&(c=d.y));b=new _.Zk(b,c)}else b=null;a.call(this,"position",b);this.Eg=!1}}},GYa=class{constructor(a,b,c,d,e=0,f=0){this.width=c;this.height=d;this.offsetX=e;this.offsetY=f;this.Fg=new Float64Array(2);this.Fg[0]=a;this.Fg[1]=b;this.Eg=new Float32Array(2)}transform(a){a.Zt(1,this.Fg,this.Eg,0,0,0);this.Eg[0]+=this.offsetX;this.Eg[1]+=this.offsetY}isVisible(a){return this.Eg[0]>=-this.width&&this.Eg[0]<=a.width+this.width&&this.Eg[1]>=-this.height&&this.Eg[1]<=a.height+this.height}equals(a){return this.Fg[0]===
a.Fg[0]&&this.Fg[1]===a.Fg[1]&&this.width===a.width&&this.height===a.height&&this.offsetX===a.offsetX&&this.offsetY===a.offsetY}Gg(a){return this.Eg[0]>a.right||this.Eg[0]+this.width<a.left||this.Eg[1]>a.bottom||this.Eg[1]+this.height<a.top?!1:!0}},bXa={linear:a=>a,["ease-out"]:a=>1-Math.pow(a-1,2),["ease-in"]:a=>Math.pow(a,2)},TO=class{constructor(a){this.frames=a;this.Eg=""}},dO;var mXa={[1]:{options:{duration:700,im:"infinite"},icon:new TO([{time:0,translate:[0,0],pm:"ease-out"},{time:.5,translate:[0,-20],pm:"ease-in"},{time:1,translate:[0,0],pm:"ease-out"}])},[2]:{options:{duration:500,im:1},icon:new TO([{time:0,translate:[0,-500],pm:"ease-in"},{time:.5,translate:[0,0],pm:"ease-out"},{time:.75,translate:[0,-20],pm:"ease-in"},{time:1,translate:[0,0],pm:"ease-out"}])},[3]:{options:{duration:200,Ux:20,im:1,dG:!1},icon:new TO([{time:0,translate:[0,0],pm:"ease-in"},{time:1,
translate:[0,-20],pm:"ease-out"}])},[4]:{options:{duration:500,Ux:20,im:1,dG:!1},icon:new TO([{time:0,translate:[0,-20],pm:"ease-in"},{time:.5,translate:[0,0],pm:"ease-out"},{time:.75,translate:[0,-10],pm:"ease-in"},{time:1,translate:[0,0],pm:"ease-out"}])}};var iO=class{constructor(){this.icon={url:_.wo("api-3/images/spotlight-poi3",!0),scaledSize:new _.al(26,37),origin:new _.Zk(0,0),anchor:new _.Zk(13,37),labelOrigin:new _.Zk(13,14)};this.Fg={url:_.wo("api-3/images/spotlight-poi-dotless3",!0),scaledSize:new _.al(26,37),origin:new _.Zk(0,0),anchor:new _.Zk(13,37),labelOrigin:new _.Zk(13,14)};this.Eg={url:_.wo("api-3/images/drag-cross",!0),scaledSize:new _.al(13,11),origin:new _.Zk(0,0),anchor:new _.Zk(7,6)};this.shape={coords:[13,0,4,3.5,0,12,2.75,21,
13,37,23.5,21,26,12,22,3.5],type:"poly"}}};var gO=class extends _.yq{constructor(a={}){super();this.Pg=this.Fg=this.Lg=this.Vg=void 0;this.Ig=null;this.jD=document.createElement("div");_.el(this.element,"maps-pin-view");this.shape=this.mh("shape",_.Bj(_.uj(EYa)),a.shape)||"DEFAULT";_.Il(this,"shape");let b,c;switch(this.shape){case "PIN":UO||(UO=cO("PIN"));var d=UO;b=13;c=7;break;case "PINLET":VO||(VO=cO("PINLET"));d=VO;b=9;c=5;break;default:WO||(WO=cO("DEFAULT")),d=WO,b=15,c=5.5}this.element.style.display="grid";this.element.style.setProperty("grid-template-columns",
"auto");this.element.style.setProperty("grid-template-rows",`${c}px auto`);this.element.style.setProperty("gap","0px");this.element.style.setProperty("justify-items","center");this.element.style.pointerEvents="none";this.element.style.userSelect="none";this.Eg=d.cloneNode(!0);this.Eg.style.display="block";this.Eg.style.overflow="visible";this.Eg.style.gridArea="1";this.Ih=Number(this.Eg.getAttribute("width"));this.Dh=Number(this.Eg.getAttribute("height"));this.Eg.querySelector("g").style.pointerEvents=
"auto";this.ph=this.Eg.querySelector(`.${_.UJ}`).getAttribute("fill")||"";d=void 0;const e=this.Eg.querySelector(`.${_.VJ}`);e&&(this.shape==="DEFAULT"?d=e.getAttribute("fill"):this.shape==="PIN"&&(d=e.getAttribute("stroke")));this.rh=d||"";d=this.Eg.querySelector("filter");this.Rh=d.id;this.Ah=d.querySelector("feFlood");this.Jg=this.Eg.querySelector("g > image");this.Zg=this.Eg.querySelector("g > text");d=void 0;(this.Wg=this.Eg.querySelector(`.${_.WJ}`))&&(d=this.Wg.getAttribute("fill"));this.Yg=
d||"";this.element.appendChild(this.Eg);this.Gg=document.createElement("div");this.Tg=b;this.Oh=c;this.Gg.style.setProperty("grid-area","2");this.Gg.style.display="flex";this.Gg.style.alignItems="center";this.Gg.style.justifyContent="center";this.element.appendChild(this.Gg);this.background=a.background;this.borderColor=a.borderColor;this.glyph=a.glyph;this.glyphColor=a.glyphColor;this.scale=a.scale;_.Sk(window,"Pin");_.Q(window,149597);this.Wh(a,gO,"PinElement")}get element(){return this.jD}get background(){return this.Vg}set background(a){a=
this.mh("background",_.Gp,a)||this.ph;this.Vg!==a&&(this.Vg=a,this.Eg.querySelector(`.${_.UJ}`).setAttribute("fill",this.Vg),fO(this),this.Vg===this.ph?(_.Sk(window,"Pdbk"),_.Q(window,160660)):(_.Sk(window,"Pvcb"),_.Q(window,160662)))}get borderColor(){return this.Lg}set borderColor(a){a=this.mh("borderColor",_.Gp,a)||this.rh;this.Lg!==a&&(this.Lg=a,(a=this.Eg.querySelector(`.${_.VJ}`))&&(this.shape==="DEFAULT"?a.setAttribute("fill",this.Lg):a.setAttribute("stroke",this.Lg)),fO(this),this.Lg===this.rh?
(_.Sk(window,"Pdbc"),_.Q(window,160663)):(_.Sk(window,"Pcbc"),_.Q(window,160664)))}get glyph(){return this.Fg}set glyph(a){a=this.mh("glyph",_.Bj(_.zj([_.Jo,_.tj(Element,"Element"),_.tj(URL,"URL")])),a)??null;if(this.Fg!==a){this.Fg=a;if(a=this.Eg.querySelector(`.${_.WJ}`))a.style.display=this.Fg==null?"":"none";this.Fg==null&&eO(0);this.Gg.textContent="";this.Zg.textContent="";this.Jg.href.baseVal="";this.Fg instanceof Element?(this.Gg.appendChild(this.Fg),eO(1)):typeof this.Fg==="string"?(this.Zg.textContent=
this.Fg,eO(2)):this.Fg instanceof URL&&eO(3);SWa(this);fO(this)}}get glyphColor(){return this.Pg}set glyphColor(a){a=this.mh("glyphColor",_.Gp,a)||null;this.Pg!==a&&(this.Pg=a,SWa(this),fO(this),this.Pg==null||this.Pg===this.Yg?(_.Sk(window,"Pdgc"),_.Q(window,160669)):(_.Sk(window,"Pcgc"),_.Q(window,160670)))}get scale(){return this.Ig}set scale(a){a=this.mh("scale",_.Bj(_.Aj(_.Dp,_.Cp)),a);a==null&&(a=1);this.Ig!==a&&(this.Ig=a,a=this.getSize(),this.Eg.setAttribute("width",`${a.width}px`),this.Eg.setAttribute("height",
`${a.height}px`),this.element.style.width=`${a.width}px`,this.element.style.height=`${a.height}px`,a=Math.round(this.Tg*this.Ig),this.Gg.style.width=`${a}px`,this.Gg.style.height=`${a}px`,this.Jg.setAttribute("width",`${this.Tg}px`),this.Jg.setAttribute("height",`${this.Tg}px`),a=_.eJa[this.shape],this.Jg.style.transform=`translate(${-(this.Tg/2+a.x)}px, ${-(this.Tg/2+a.y)}px)`,this.element.style.setProperty("grid-template-rows",`${this.Oh*this.Ig}px auto`),fO(this),this.Ig===1?(_.Sk(window,"Pds"),
_.Q(window,160671)):(_.Sk(window,"Pcs"),_.Q(window,160672)))}getAnchor(){return new _.Zk(this.getSize().width/2,this.getSize().height-1*this.Ig)}getSize(){return new _.al(Math.round(this.Ih*this.Ig/2)*2,Math.round(this.Dh*this.Ig/2)*2)}addListener(a,b){return _.Vj(this,a,b)}addEventListener(){throw Error(_.Jl(this,"addEventListener is unavailable in this version."));}update(a){super.update(a);this.dispatchEvent(new Event("gmp-internal-pinchange",{bubbles:!0,composed:!0}))}};
gO.prototype.addEventListener=gO.prototype.addEventListener;gO.prototype.constructor=gO.prototype.constructor;gO.pi={si:182481,ri:182482};var WO=null,VO=null,UO=null;_.Ea([_.jo({fh:"background",type:String,nh:!0}),_.M("design:type",Object),_.M("design:paramtypes",[Object])],gO.prototype,"background",null);_.Ea([_.jo({fh:"border-color",type:String,nh:!0}),_.M("design:type",Object),_.M("design:paramtypes",[Object])],gO.prototype,"borderColor",null);
_.Ea([_.jo(),_.M("design:type",Object),_.M("design:paramtypes",[Object])],gO.prototype,"glyph",null);_.Ea([_.jo({fh:"glyph-color",type:String,nh:!0}),_.M("design:type",Object),_.M("design:paramtypes",[Object])],gO.prototype,"glyphColor",null);_.Ea([_.jo({fh:"scale",type:Number,nh:!0}),_.M("design:type",Object),_.M("design:paramtypes",[Object])],gO.prototype,"scale",null);_.ul("gmp-internal-pin",gO);var HYa=class extends _.kk{constructor(a,b){super();this.Fg=a;this.Eg=b;XO||(XO=new iO)}changed(a){a!=="modelIcon"&&a!=="modelShape"&&a!=="modelCross"&&a!=="modelLabel"||_.Mx(_.Lx(),this.Gg,this,this)}Gg(){const a=this.get("modelIcon");var b=this.get("modelLabel");XWa(this,"viewIcon",a||b&&XO.Fg||XO.icon);XWa(this,"viewCross",XO.Eg);b=this.get("useDefaults");let c=this.get("modelShape");c||a&&!b||(c=XO.shape);this.get("viewShape")!==c&&this.set("viewShape",c)}},XO;var IYa=class extends _.kk{constructor(){super();this.Fg=!1;this.Eg=YWa(this);this.set("shouldRender",this.Eg)}changed(){if(!this.Fg){var a=YWa(this);this.Eg!==a&&(this.Eg=a,this.Fg=!0,this.set("shouldRender",this.Eg),this.Fg=!1)}}};var CXa=class extends _.kk{constructor(a){super();this.Fg=a;this.Eg=!1}internalPosition_changed(){if(!this.Eg){this.Eg=!0;var a=this.get("position"),b=this.get("internalPosition");a&&b&&!a.equals(b)&&this.set("position",this.get("internalPosition"));this.Eg=!1}}draggable_changed(){if(!this.Eg){this.Eg=!0;if(this.Fg){const a=this.get("place");a?this.set("internalPosition",a.location):this.set("internalPosition",this.get("position"))}this.get("place")?this.set("actuallyDraggable",!1):this.set("actuallyDraggable",
this.get("draggable"));this.Eg=!1}}position_changed(){this.draggable_changed()}place_changed(){this.draggable_changed()}};var iXa=class{constructor(a,b,c,d,e){this.vl=a;this.label=b;this.opacity=c;this.visible=d;this.origin=void 0;this.zIndex=0;this.Gg=this.Jg=this.Eg=null;this.Fg=new _.xm(this.Kg,0,this);this.Ig=e;this.vl=a;this.label=b;this.opacity=c;this.visible=d}setOpacity(a){this.opacity=a;_.ym(this.Fg)}setLabel(a){this.label=a;_.ym(this.Fg)}setVisible(a){this.visible=a;_.ym(this.Fg)}setZIndex(a){this.zIndex=a;_.ym(this.Fg)}release(){this.vl=null;kO(this)}Kg(){if(this.vl&&this.label&&this.visible!==!1){var a=this.vl.markerLayer,
b=this.label;this.Eg?a.appendChild(this.Eg):(this.Eg=_.nu("div",a),this.Eg.style.transform="translateZ(0)");a=this.Eg;this.origin&&_.mu(a,this.origin);var c=a.firstElementChild;c||(c=_.nu("div",a),c.style.height="100px",c.style.transform="translate(-50%, -50px)",c.style.display="table",c.style.borderSpacing="0");let d=c.firstElementChild;d||(d=_.nu("div",c),d.style.display="table-cell",d.style.verticalAlign="middle",d.style.whiteSpace="nowrap",d.style.textAlign="center");c=d.firstElementChild||_.nu("div",
d);c.textContent=b.text;c.style.color=b.color;c.style.fontSize=b.fontSize;c.style.fontWeight=b.fontWeight;c.style.fontFamily=b.fontFamily;c.className=b.className;c.setAttribute("aria-hidden","true");if(this.Ig&&b!==this.Gg){this.Gg=b;const {width:e,height:f}=c.getBoundingClientRect();b=new _.al(e,f);b.equals(this.Jg)||(this.Jg=b,this.Ig(b))}_.FF(c,_.bj(this.opacity,1));_.ou(a,this.zIndex)}else kO(this)}};var dXa=class{constructor(a,b,c){this.element=a;this.animation=b;this.options=c;this.Fg=!1;this.Eg=null}start(){this.options.im=this.options.im||1;this.options.duration=this.options.duration||1;_.ck(this.element,"webkitAnimationEnd",()=>{this.Fg=!0;_.hk(this,"done")});$Wa(this.element,RWa(this.animation),this.options)}cancel(){this.Eg&&(this.Eg.remove(),this.Eg=null);$Wa(this.element,null,{});_.hk(this,"done")}stop(){this.Fg||(this.Eg=_.ck(this.element,"webkitAnimationIteration",()=>{this.cancel()}))}};var lO=[],mO=null,eXa=class{constructor(a,b,c){this.element=a;this.animation=b;this.im=-1;this.Eg=!1;this.startTime=0;c.im!=="infinity"&&(this.im=c.im||1);this.duration=c.duration||1E3}start(){lO.push(this);mO||(mO=window.setInterval(aXa,10));this.startTime=Date.now();this.tick()}cancel(){this.Eg||(this.Eg=!0,cXa(this,1),_.hk(this,"done"))}stop(){this.Eg||(this.im=1)}tick(){if(!this.Eg){var a=Date.now();cXa(this,(a-this.startTime)/this.duration);a>=this.startTime+this.duration&&(this.startTime=Date.now(),
this.im!=="infinite"&&(this.im--,this.im||this.cancel()))}}};var JYa=_.ka.DEF_DEBUG_MARKERS,YO=class extends _.kk{constructor(a,b,c){super();this.Lg=new _.xm(()=>{var d=this.get("panes"),e=this.get("scale");if(!d||!this.getPosition()||this.Ei()==0||_.$i(e)&&e<.1&&!this.Ek)qO(this);else{hXa(this,d.markerLayer);if(!this.Qg){var f=this.Zg();if(f){var g=f.url;e=this.get("clickable")!=0;var h=this.getDraggable(),l=this.get("title")||"",n=l;n||(n=(n=this.gh())?n.text:"");if(e||h||n){var p=!e&&!h&&!l,r=hO(f),u=tO(f),w=this.get("shape"),x=jO(f),y={};if(_.ru())f=x.width,
x=x.height,r=new _.al(f+16,x+16),f={url:_.Kz,size:r,anchor:u?new _.Zk(u.x+8,u.y+8):new _.Zk(Math.round(f/2)+8,x+8),scaledSize:r};else{const D=f.scaledSize||x;(_.Mm.Fg||_.Mm.Eg)&&w&&(y.shape=w,x=D);if(!r||w)f={url:_.Kz,size:x,anchor:u,scaledSize:D}}u=f.url!=null;this.Dh===u&&pO(this);this.Dh=!u;y=this.targetElement=rO(this,this.getPanes().overlayMouseTarget,this.targetElement,f,y);this.targetElement.style.pointerEvents=p?"none":"";if(p=y.querySelector("img"))p.style.removeProperty("position"),p.style.removeProperty("opacity"),
p.style.removeProperty("left"),p.style.removeProperty("top");p=y;if((u=p.getAttribute("usemap")||p.firstChild&&p.firstChild.getAttribute("usemap"))&&u.length&&(p=_.iu(p).getElementById(u.substr(1))))var B=p.firstChild;B&&(B.tabIndex=-1,B.style.display="inline",B.style.position="absolute",B.style.left="0px",B.style.top="0px");JYa&&(y.dataset.debugMarkerImage=g);y=B||y;y.title=l;n&&this.rp().setAttribute("aria-label",n);this.fw();h&&!this.Kg&&(g=this.Kg=new _.iJa(y,this.Ug,this.targetElement),this.Ug?
(g.bindTo("deltaClientPosition",this),g.bindTo("position",this)):g.bindTo("position",this.Tg,"rawPosition"),g.bindTo("containerPixelBounds",this,"mapPixelBounds"),g.bindTo("anchorPoint",this),g.bindTo("size",this),g.bindTo("panningEnabled",this),this.Sg||(this.Sg=[_.gk(g,"dragstart",this),_.gk(g,"drag",this),_.gk(g,"dragend",this),_.gk(g,"panbynow",this)]));g=this.get("cursor")||"pointer";h?this.Kg.set("draggableCursor",g):y.style.cursor=e?g:"";pXa(this,y)}}}d=d.overlayLayer;if(h=e=this.get("cross"))h=
this.get("crossOnDrag"),h===void 0&&(h=this.get("raiseOnDrag")),h=h!=0&&this.getDraggable()&&this.Ek;h?this.Ig=rO(this,d,this.Ig,e):(this.Ig&&_.uu(this.Ig),this.Ig=null);this.Mg=[this.Eg,this.Ig,this.targetElement];lXa(this);for(e=0;e<this.Mg.length;++e)if(h=this.Mg[e])d=h,g=h.Mg,l=nO(h)||_.ml,h=sO(this),g=jXa(this,g,h,l),_.mu(d,g),(g=_.Om().transform)&&(d.style[g]=h!=1?"scale("+h+") ":""),d&&_.ou(d,kXa(this));nXa(this);for(d=0;d<this.Mg.length;++d)(e=this.Mg[d])&&_.EF(e);_.hk(this,"UPDATE_FOCUS")}},
0);this.yi=a;this.vi=c;this.Ug=b||!1;this.Tg=new FYa;this.Tg.bindTo("position",this);this.Jg=this.Eg=null;this.Ih=[];this.rh=!1;this.targetElement=null;this.Dh=!1;this.Ig=null;this.Mg=[];this.kh=new _.Zk(0,0);this.Xg=new _.al(0,0);this.Rg=new _.Zk(0,0);this.Vg=!0;this.Qg=0;this.Gg=this.Ah=this.Rh=this.Oh=null;this.Wg=!1;this.yh=[_.Vj(this,"dragstart",this.ii),_.Vj(this,"dragend",this.Zh),_.Vj(this,"panbynow",()=>_.zm(this.Lg))];this.ph=this.Og=this.Ng=this.Kg=this.Pg=this.Sg=null;this.Yg=!1;this.getPosition=
_.Mk("position");this.getPanes=_.Mk("panes");this.Ei=_.Mk("visible");this.Zg=_.Mk("icon");this.gh=_.Mk("label");this.mp=null}by(){}get xm(){return this.Yg}set xm(a){this.Yg!==a&&(this.Yg=a,_.hk(this,"UPDATE_FOCUS"))}get Ek(){return this.get("dragging")}panes_changed(){qO(this);_.ym(this.Lg)}Yn(a){this.set("position",a&&new _.Zk(a.hh,a.jh))}rs(){this.unbindAll();this.set("panes",null);this.Gg&&this.Gg.stop();this.Pg&&(_.Xj(this.Pg),this.Pg=null);this.Gg=null;oO(this.yh);this.yh=[];qO(this);_.hk(this,
"RELEASED")}lh(){var a;if(!(a=this.Oh!=(this.get("clickable")!=0)||this.Rh!=this.getDraggable())){a=this.Ah;var b=this.get("shape");a=!(a==null||b==null?a==b:a.type==b.type&&_.KE(a.coords,b.coords))}a&&(this.Oh=this.get("clickable")!=0,this.Rh=this.getDraggable(),this.Ah=this.get("shape"),pO(this),_.ym(this.Lg))}Fg(){_.ym(this.Lg)}position_changed(){this.Ug?_.zm(this.Lg):_.ym(this.Lg)}rp(){return this.targetElement}fw(){const a=this.rp();if(a){var b=!!this.get("title");b||(b=(b=this.gh())?!!b.text:
!1);this.xm?a.setAttribute("role","button"):b?a.setAttribute("role","img"):a.removeAttribute("role")}}Ex(a){_.hk(this,"click",a);_.Sk(window,"Mki");_.Q(window,171149)}Cs(){}xt(a){_.Vt(a);_.hk(this,"click",a);_.Sk(window,"Mmi");_.Q(window,171150)}Dx(){}getDraggable(){return!!this.get("draggable")}ii(){this.set("dragging",!0);this.Tg.set("snappingCallback",this.yi)}Zh(){this.Tg.set("snappingCallback",null);this.set("dragging",!1)}animation_changed(){this.Vg=!1;this.get("animation")?nXa(this):(this.set("animating",
!1),this.Gg&&this.Gg.stop())}EE(a){const b=this.get("markerPosition");return this.mp&&b&&this.mp.size?VWa(a,this.targetElement):!1}};_.H=YO.prototype;_.H.shape_changed=YO.prototype.lh;_.H.clickable_changed=YO.prototype.lh;_.H.draggable_changed=YO.prototype.lh;_.H.cursor_changed=YO.prototype.Fg;_.H.scale_changed=YO.prototype.Fg;_.H.raiseOnDrag_changed=YO.prototype.Fg;_.H.crossOnDrag_changed=YO.prototype.Fg;_.H.zIndex_changed=YO.prototype.Fg;_.H.opacity_changed=YO.prototype.Fg;_.H.title_changed=YO.prototype.Fg;
_.H.cross_changed=YO.prototype.Fg;_.H.icon_changed=YO.prototype.Fg;_.H.visible_changed=YO.prototype.Fg;_.H.dragging_changed=YO.prototype.Fg;var vXa="click dblclick mouseup mousedown mouseover mouseout rightclick dragstart drag dragend contextmenu".split(" "),GXa=class{constructor(a,b,c,d,e,f,g){this.marker=a;this.Fg=b;this.ah=e;this.Sg=f;this.Ig=g;this.Pg=!0;this.Qg=this.Rg=null;this.Kg=[];this.Og=b instanceof _.Ck;f=uO(this);b=this.Og&&f?_.bu(f,b.getProjection()):null;this.Eg=new YO(d,!!this.Og,h=>{this.Eg.mp=a.__gm.mp={...a.__gm.mp,uP:h};a.__gm.kx&&a.__gm.kx()});_.Vj(this.Eg,"RELEASED",()=>{var h=this.Eg;if(this.Ig&&this.Ig.has(h)){({YD:h}=
this.Ig.get(h));for(const l of h)l.remove()}this.Ig&&this.Ig.delete(this.Eg)});this.Sg&&this.Ig&&!this.Ig.has(this.Eg)&&(this.Ig.set(this.Eg,{marker:this.marker,YD:[]}),this.Sg.Pg(this.Eg),vO(this,this.Eg),sXa(this,this.Eg));(this.Jg=this.Og?new _.cK(e.tj,this.Eg,b,e,()=>{if(this.Eg.get("dragging")&&!this.marker.get("place")){var h=this.Jg.getPosition();h&&(h=_.Nl(h,this.Fg.get("projection")),this.Pg=!1,this.marker.set("position",h),this.Pg=!0)}}):null)&&e.Li(this.Jg);this.Lg=new HYa(c,(h,l,n)=>{this.Eg.mp=
a.__gm.mp={...a.__gm.mp,size:h,anchor:l,labelOrigin:n};a.__gm.kx&&a.__gm.kx()});this.Gg=this.Og?null:new _.IJ;this.Mg=this.Og?null:new IYa;this.Ng=new _.kk;this.Ng.bindTo("position",this.marker);this.Ng.bindTo("place",this.marker);this.Ng.bindTo("draggable",this.marker);this.Ng.bindTo("dragging",this.marker);this.Lg.bindTo("modelIcon",this.marker,"icon");this.Lg.bindTo("modelLabel",this.marker,"label");this.Lg.bindTo("modelCross",this.marker,"cross");this.Lg.bindTo("modelShape",this.marker,"shape");
this.Lg.bindTo("useDefaults",this.marker,"useDefaults");this.Eg.bindTo("icon",this.Lg,"viewIcon");this.Eg.bindTo("label",this.Lg,"viewLabel");this.Eg.bindTo("cross",this.Lg,"viewCross");this.Eg.bindTo("shape",this.Lg,"viewShape");this.Eg.bindTo("title",this.marker);this.Eg.bindTo("cursor",this.marker);this.Eg.bindTo("dragging",this.marker);this.Eg.bindTo("clickable",this.marker);this.Eg.bindTo("zIndex",this.marker);this.Eg.bindTo("opacity",this.marker);this.Eg.bindTo("anchorPoint",this.marker);this.Eg.bindTo("markerPosition",
this.marker,"position");this.Eg.bindTo("animation",this.marker);this.Eg.bindTo("crossOnDrag",this.marker);this.Eg.bindTo("raiseOnDrag",this.marker);this.Eg.bindTo("animating",this.marker);this.Mg||this.Eg.bindTo("visible",this.marker);tXa(this);uXa(this);wXa(this);this.Og?(xXa(this),yXa(this),AXa(this)):(BXa(this),this.Gg&&(this.Mg.bindTo("visible",this.marker),this.Mg.bindTo("cursor",this.marker),this.Mg.bindTo("icon",this.marker),this.Mg.bindTo("icon",this.Lg,"viewIcon"),this.Mg.bindTo("mapPixelBoundsQ",
this.Fg.__gm,"pixelBoundsQ"),this.Mg.bindTo("position",this.Gg,"pixelPosition"),this.Eg.bindTo("visible",this.Mg,"shouldRender")),DXa(this))}dispose(){this.Eg.set("animation",null);this.Eg.rs();this.ah&&this.Jg?this.ah.xl(this.Jg):this.Eg.rs();this.Mg&&this.Mg.unbindAll();this.Gg&&this.Gg.unbindAll();this.Lg.unbindAll();this.Ng.unbindAll();this.Kg.forEach(_.Xj);this.Kg.length=0}};var AO=class{constructor(a,b,c,d){this.div=a;this.Ji=b;this.Eg=c;this.Bh=d}getContext(){if(!this.context){const a=this.div,b=a.ownerDocument.createElement("canvas");_.Sm(b);b.style.position="absolute";b.style.top=b.style.left="0";const c=b.getContext("2d"),d=xO(c),e=this.Bh.size;b.width=Math.ceil(e.hh*d);b.height=Math.ceil(e.jh*d);b.style.width=_.gj(e.hh);b.style.height=_.gj(e.jh);a.appendChild(b);this.context=c}return this.context}VC(a){const b=IXa(this),c=this.getContext(),d=xO(c),e=Math.round(a.dx*
d),f=Math.round(a.dy*d),g=Math.ceil(a.nq*d);a=Math.ceil(a.kq*d);const h=HXa(this,g,a),l=h.getContext("2d");l.translate(-e,-f);b.forEach(n=>{l.globalAlpha=_.bj(n.opacity,1);l.drawImage(n.image,n.Ay,n.By,n.zy,n.ty,Math.round(n.dx*d),Math.round(n.dy*d),n.nq*d,n.kq*d)});c.clearRect(e,f,g,a);c.globalAlpha=1;c.drawImage(h,e,f)}};AO.prototype.DL=AO.prototype.VC;var SXa=class{constructor(){this.Eg=_.wF().Eg}load(a,b){return this.Eg.load(new _.WH(a.url),c=>{if(c){var d=c.size,e=a.size||a.scaledSize||d;a.size=e;var f=a.scaledSize||d,g=a.anchor||new _.Zk(e.width/2,e.height),h=f.width/d.width,l=f.height/d.height,n=a.origin?a.origin.x/h:0,p=a.origin?a.origin.y/l:0,r=-g.x;g=-g.y;var u=e.width/h,w=e.width,x=e.height/l,y=e.height;n*h+e.width>f.width&&(u=d.width-n*h,w=f.width);p*l+e.height>f.height&&(x=d.height-p*l,y=f.height);b({image:c,Ay:n,By:p,zy:u,ty:x,dx:r,
dy:g,nq:w,kq:y})}else b(null)})}cancel(a){this.Eg.cancel(a)}};var UXa=class{constructor(a,b,c,d){this.Eg=b;this.Fg=c;this.zIndex=40;this.Gg=new _.gK(a,d,c)}zs(a){return a!=="dragstart"&&a!=="drag"&&a!=="dragend"}Is(a,b){return b?yO(this,a,-8,0)||yO(this,a,0,-8)||yO(this,a,8,0)||yO(this,a,0,8):yO(this,a,0,0)}handleEvent(a,b,c){const d=b.mj;if(a==="mouseout")this.Eg.set("cursor",""),this.Eg.set("title",null);else if(a==="mouseover"){var e=d.Dv;this.Eg.set("cursor",e.cursor);(e=e.title)&&this.Eg.set("title",e)}let f;d&&a!=="mouseout"?f=d.Dv.latLng:f=b.latLng;a===
"dblclick"&&_.Tj(b.domEvent);_.hk(c,a,new _.Nz(f,b.domEvent))}};var VXa=class extends _.qo{constructor(a,b,c,d,e,f,g){super();this.Kg=a;this.Mg=d;this.Ig=c;this.Gg=e;this.Jg=f;this.Fg=g||_.Zz;b.Eg=h=>{MXa(this,h)};b.onRemove=h=>{NXa(this,h)};b.forEach(h=>{MXa(this,h)})}Eg(){return{Bh:this.Fg,rl:2,Pk:this.Lg.bind(this)}}Lg(a,b={}){const c=document.createElement("div"),d=this.Fg.size;c.style.width=`${d.hh}px`;c.style.height=`${d.jh}px`;c.style.overflow="hidden";a={div:c,zoom:a.xh,mi:new _.Zk(a.sh,a.th),Wo:{},Ji:new _.Bm};c.dk=a;OXa(this,a);let e=!1;return{Ii:()=>
c,gm:()=>e,loaded:new Promise(f=>{_.ek(c,"load",()=>{e=!0;f()})}),release:()=>{const f=c.dk;c.dk=null;PXa(this,f);c.textContent="";b.Xi&&b.Xi()}}}};var WXa=class{constructor(a,b,c){this.Fg=b;this.bo=null;this.Eg=!1;this.Ig=0;const d=this;a.Eg=e=>{d.Gq(e)};a.onRemove=e=>{d.ms(e)};this.Jg=c;a.getSize()?(this.Eg=!0,this.Gg()):_.vm(_.nE(_.hk,c,"load"))}Gq(a){QXa(this,a,!0)}ms(a){QXa(this,a,!1)}Gg(){this.Eg&&JXa(this.Fg);this.Eg=!1;this.bo=null;this.Ig=0;_.vm(_.nE(_.hk,this.Jg,"load"))}};var TXa=class{constructor(a,b,c,d,e){var f=RXa;this.Dp=a;this.Fg=b;this.Eg=c;this.Jg=f;this.Ig=d;this.Gg=e;this.Dp.Eg=g=>{this.Gq(g)};this.Dp.onRemove=g=>{this.ms(g)}}Gq(a){var b=a.get("internalPosition"),c=a.get("zIndex");const d=a.get("opacity"),e=a.__gm.Ix={Jz:a,latLng:b,zIndex:c,opacity:d,Ji:{}};b=a.get("useDefaults");c=a.get("icon");const f=a.get("shape")||c&&!b?a.get("shape"):this.Eg.shape,g=c?this.Jg(c):this.Eg.icon,h=IWa(()=>{e===a.__gm.Ix&&(e.Bt||e.RF)&&this.tl(a,e,g,f)});g.url?this.Ig.load(g,
l=>{e.Bt=l;h()}):(e.RF=this.Gg(g),h())}ms(a){this.Fg.remove(a.__gm.Ix);a.__gm.Ix=null}tl(a,b,c,d){if(b.Bt){c=c.size;var e=a.get("anchorPoint");if(!e||e.Eg)e=new _.Zk(b.Bt.dx+c.width/2,b.Bt.dy),e.Eg=!0,a.set("anchorPoint",e)}else c=b.RF.size;d?d.coords=d.coords||d.coord:d={type:"rect",coords:[0,0,c.width,c.height]};b.shape=d;b.clickable=a.get("clickable");b.title=a.get("title")||null;b.cursor=a.get("cursor")||"pointer";_.Cm(this.Fg,b)}};var zO=new Map;var KYa=class{constructor(a,b,c,d){this.Ou={};this.bo=0;this.Tv=!0;const e=this;this.XB=b;this.nt=c;this.ZD=d;const f={animating:1,animation:1,attribution:1,clickable:1,cursor:1,draggable:1,flat:1,icon:1,label:1,opacity:1,optimized:1,place:1,position:1,shape:1,__gmHiddenByCollision:1,title:1,visible:1,zIndex:1};this.YK=function(g){g in f&&(delete this.changed,e.Ou[_.jk(this)]=this,ZXa(e))};a.Eg=g=>{e.Gq(g)};a.onRemove=g=>{e.ms(g)};a=a.Fg;for(const g of Object.values(a))this.Gq(g)}Gq(a){this.Ou[_.jk(a)]=
a;ZXa(this)}ms(a){delete a.changed;delete this.Ou[_.jk(a)];this.XB.remove(a);this.nt.remove(a)}};var OO=class extends Event{constructor(){super("gmp-click",{bubbles:!0})}};var LYa=class{Ug(){}Rg(){}Fg(){}Gg(){}Ig(){}Ng(){}Pg(){}Lg(){}Jg(){}Kg(){}Og(){}Qg(){}Eg(){}Sg(){}Tg(){}Vg(){}Xg(){}Mg(){}};var MYa=(0,_.Tf)`.yNHHyP-marker-view .IPAZAH-content-container\u003e*{pointer-events:none}.yNHHyP-marker-view .IPAZAH-content-container.HJDHPx-interactive\u003e*{pointer-events:auto}\n`;var cYa=EWa("visible-gmp-advanced-markers"),bYa=EWa("hidden-gmp-advanced-markers"),fYa=class{constructor(a){this.Wi=NYa;this.oo=null;this.Og=!1;this.Mg=null;this.Lg=0;this.Ng=null;this.map=a;this.Ig=new Set;this.Jg=new Set;this.tm=`maps-aria-${_.Bk()}`;this.Gg=document.createElement("span");this.Gg.id=this.tm;this.Gg.textContent="To activate drag with keyboard, press Alt + Enter. Once in keyboard drag state, use the arrow keys to move the marker. To complete the drag, press the Enter key. To cancel, press Escape.";
this.Gg.style.display="none";this.Kg=document.createElement("div");this.Eg=document.createElement("div");CSS.supports("content-visibility: hidden")?this.Eg.style.contentVisibility="hidden":this.Eg.style.visibility="hidden";var b=document.createElement("slot");b.setAttribute("name",cYa);this.Kg.appendChild(b);b=document.createElement("slot");b.setAttribute("name",bYa);this.Eg.appendChild(b);this.Fg=document.createElement("div");this.Fg.append(this.Kg,this.Eg);const c=a.__gm;this.Qg=c.np;this.Pg=new Promise(d=>
{c.Gg.then(e=>{this.map&&(e&&(this.oo=$Xa(this,a)),this.Og=!0);d()})});_.$q(MYa,this.map.getDiv());Promise.all([c.Fg,this.Pg]).then(([{vl:d}])=>{this.map&&d.overlayMouseTarget.append(this.Gg,this.Fg);this.Ng=c.addListener("panes_changed",e=>{this.map&&e.overlayMouseTarget.append(this.Gg,this.Fg)})})}dispose(){this.oo&&(this.oo.setMap(null),this.oo=null);this.Ng&&this.Ng.remove();this.Gg.remove();this.Eg.remove();this.Kg.remove();this.Fg.remove();this.Eg.textContent="";this.Kg.textContent="";this.Ig.clear();
this.Jg.clear();this.map=null}isEmpty(){return this.Ig.size===0}requestRedraw(){this.Og?this.oo&&this.oo.requestRedraw():this.Pg.then(()=>{this.oo&&this.oo.requestRedraw()})}onDraw(a){if(this.map){var b=this.Qg.offsetWidth,c=this.Qg.offsetHeight,d=_.fm(this.map.getZoom()||1,this.map.getTilt()||0,this.map.getHeading()||0);for(const h of this.Ig.values()){var e=h.oK;var f=this.map.getCenter();if(e&&f){f=_.Vi(f.lng(),-180,180);var g=_.Vi(e.lng,-180,180);f>0&&g<f-180?g+=360:f<0&&g>f+180&&(g-=360);e=new _.Qp({altitude:e.altitude,
lat:e.lat,lng:g},!0)}else e=null;if(!e){h.Yn(null,d);continue}e=a.fromLatLngAltitude(e);f=Array.from(e);e=g=[0,0,0];const l=e[0],n=e[1],p=e[2],r=1/(f[3]*l+f[7]*n+f[11]*p+f[15]);e[0]=(f[0]*l+f[4]*n+f[8]*p+f[12])*r;e[1]=(f[1]*l+f[5]*n+f[9]*p+f[13])*r;e[2]=(f[2]*l+f[6]*n+f[10]*p+f[14])*r;const {fK:u,ZM:w}={fK:f[14]<0&&f[15]<0,ZM:g};u?h.Yn(null,d):h.Yn({hh:bO(w[0]/2*b),jh:bO(-w[1]/2*c)},d,{hh:b,jh:c})}}}};var DO=new Map,NYa=new class extends LYa{Ug(a){a&&this.Vi(a,181191,"Acamk")}Rg(a){if(a){var b=a.getRenderingType();b!=="UNINITIALIZED"&&this.Vi(a,159713,"Mlamk");b==="RASTER"?this.Vi(a,157416,"Raamk"):b==="VECTOR"&&this.Vi(a,157417,"Veamk")}}Fg(a,b=!1){this.Vi(a,158896,"Camk");b&&this.Vi(a,185214,"Cgmk")}Gg(a,b){b&&(b!=="REQUIRED"&&this.Vi(a,160097,"Csamk"),b==="REQUIRED_AND_HIDES_OPTIONAL"?this.Vi(a,160098,"Cramk"):b==="OPTIONAL_AND_HIDES_LOWER_PRIORITY"&&this.Vi(a,160099,"Cpamk"))}Ig(a,b){b?this.Vi(a,
159404,"Dcamk"):this.Vi(a,159405,"Ccamk")}Ng(a){this.Vi(a,159484,"Ceamk")}Pg(a){this.Vi(a,160438,"Dwaamk")}Lg(a){this.Vi(a,159521,"Ziamk")}Jg(a){this.Vi(a,160103,"Dgamk")}Kg(a){this.Vi(a,159805,"Tiamk")}Og(a){this.Vi(a,159490,"Ckamk")}Qg(a){this.Vi(a,159812,"Fcamk")}Eg(a){this.Vi(a,159609,"Atamk")}Sg(a){this.Vi(a,160122,"Kdamk")}Tg(a){this.Vi(a,160106,"Ldamk")}Vg(a){this.Vi(a,160478,"pdamk")}Xg(a,b){const c=[{threshold:1E4,Fo:160636,To:"Amk10K"},{threshold:5E3,Fo:160635,To:"Amk5K"},{threshold:2E3,
Fo:160634,To:"Amk2K"},{threshold:1E3,Fo:160633,To:"Amk1K"},{threshold:500,Fo:160632,To:"Amk500"},{threshold:200,Fo:160631,To:"Amk200"},{threshold:100,Fo:160630,To:"Amk100"},{threshold:50,Fo:159732,To:"Amk50"},{threshold:10,Fo:160629,To:"Amk10"},{threshold:1,Fo:160628,To:"Amk1"}];for(const {threshold:d,Fo:e,To:f}of c)if(b>=d){this.Vi(a,e,f);break}}Mg(a){a=a instanceof KeyboardEvent;this.Vi(window,a?171152:171153,a?"Amki":"Ammi")}Vi(a,b,c){a&&(_.Q(a,b),_.Sk(a,c))}},OYa=new LYa,CO=null;var PYa=class{constructor(a){this.Eg=a;this.Jg=this.Ig=!1;this.Ng=this.Gg=this.Kg=this.Og=this.Qg=this.Xg=null;this.Pg=0;this.Wg=null;this.Zg=b=>{this.Bs(b)};this.gh=b=>{b.touches.length===1&&this.Bs(b)};this.Yg=b=>{b.preventDefault();b.stopImmediatePropagation()};this.Tg=b=>{if(this.Jg||this.Mg||OWa(b,this.Xg))this.Mg=!0};a=this.Eg.Co;_.Pv!==2?(a.addEventListener("pointerdown",this.Zg),a.addEventListener("pointermove",this.Tg)):(a.addEventListener("touchstart",this.gh,{passive:!1}),a.addEventListener("touchmove",
this.Tg,{passive:!1}));a.addEventListener("mousedown",this.Yg);this.Sg=b=>{b.preventDefault();b.stopImmediatePropagation();this.Jg?qYa(this,b):this.Ig?(rYa(this,b),HO(this.Eg,"drag",b)):(sYa(this,b),b=this.Eg,b.Wi.Vg(b.map))};this.Lg=b=>{this.Ng&&b.timeStamp-this.Ng>=500&&(!this.Ig||this.Jg)?(this.Jg?qYa(this,b):(sYa(this,b),b=this.Eg,b.Wi.Tg(b.map),b.xp&&_.hk(b,"longpressdragstart")),this.Mg=!0):(this.Ig&&(this.Jg||this.Mg||OWa(b,this.Xg))&&(this.Mg=!0),this.Jg&&EO(this,b),b.type==="touchend"&&(this.Fg.style.display=
"none"),this.Ig?(b.stopImmediatePropagation(),rYa(this,b),KO(this),LO(this.Eg,!0),HO(this.Eg,"dragend",b)):KO(this))};this.lh=b=>{this.yh(b)};this.ph=b=>{this.rh(b)};this.kh=b=>{FO(this,b)};this.yh=b=>{if(b.altKey&&(_.Nx(b)||b.key===_.$sa))FO(this,b);else if(!b.altKey&&_.Nx(b))this.Mg=!0,EO(this,b);else if(_.Ox(b)||_.Qx(b)||_.Px(b)||_.Rx(b))b.preventDefault(),this.Rg.add(b.key),this.Pg||(this.Wg=new _.YI(100),uYa(this)),HO(this.Eg,"drag",b);else if(b.code==="Equal"||b.code==="Minus"){var c=this.Eg;
b=b.code==="Equal"?1:-1;const d=NWa(c.Jj,c.Oo);d&&c.ah.vG(b,d)}};this.rh=b=>{(_.Ox(b)||_.Qx(b)||_.Px(b)||_.Rx(b))&&this.Rg.delete(b.key)};this.Ug=()=>{this.Fg.style.display=""};this.Vg=()=>{this.Ig||(this.Fg.style.display="none")};this.Fg=document.createElement("div");mYa(this);this.Mg=!1;this.Rg=new Set}Gy(a){this.Gg&&_.ZI(this.Gg,a)}Bs(a){this.Mg=!1;if(this.Eg.gmpDraggable&&(a.button===0||a.type==="touchstart")){const b=this.Eg.Co;b.focus();const c=document;_.Pv!==2||a.preventDefault();a.stopImmediatePropagation();
this.Ng=a.timeStamp;_.Pv!==2?(c.addEventListener("pointermove",this.Sg),c.addEventListener("pointerup",this.Lg),c.addEventListener("pointercancel",this.Lg)):(c.addEventListener("touchmove",this.Sg,{passive:!1}),c.addEventListener("touchend",this.Lg),c.addEventListener("touchcancel",this.Lg),c.addEventListener("touchstart",this.Lg));this.Ig||(this.Xg=_.zI(a));b.style.cursor=_.Vx}}Ex(){this.Ig||(this.Mg=!1)}Cs(a){if(this.Eg.gmpDraggable&&!this.Jg&&!this.Ig){var b=this.Eg.Co;b.addEventListener("keydown",
this.lh);b.addEventListener("keyup",this.ph);b.addEventListener("blur",this.kh);this.Kg=this.Eg.en();this.Qg=this.Eg.position;this.Jg=this.Ig=!0;pYa(this);b=this.Eg.Co;b.setAttribute("aria-grabbed","true");JO(this.Eg);b.style.zIndex="2147483647";this.Fg.style.opacity="1";HO(this.Eg,"dragstart",a);a=this.Eg;a.Wi.Sg(a.map)}}Dx(a,b=!0){this.Jg?FO(this,a,b):this.Ig&&(this.Eg.position=this.Qg,a.stopImmediatePropagation(),KO(this),b&&HO(this.Eg,"dragend",a))}Ek(){return this.Ig}dispose(){KO(this);const a=
this.Eg.Co;_.Pv!==2?(a.removeEventListener("pointerdown",this.Zg),a.removeEventListener("pointermove",this.Tg)):(a.removeEventListener("touchstart",this.gh,{passive:!1}),a.removeEventListener("touchmove",this.Tg,{passive:!1}));a.removeEventListener("mousedown",this.Yg);a.removeEventListener("pointerenter",this.Ug);a.removeEventListener("pointerleave",this.Vg);a.removeEventListener("focus",this.Ug);a.removeEventListener("blur",this.Vg);this.Fg.remove()}};var ZO=!1,$O=class extends _.yq{constructor(a={}){super(a);this.Lu=this.Xj=this.Mi=null;this.Fz="";this.Hk=this.yv=this.Rp=this.ah=this.Hj=this.Ok=null;this.zC=!1;this.Iy=null;this.pB=this.HE=this.Jy=this.BC=!1;this.Fh=this.yw=this.WE=this.sF=this.wG=this.lD=null;this.yC=void 0;this.hu=this.AC=!1;this.rE=_.il(!1);this.Oo=this.iu=this.Tz=null;this.br="";this.Jj=this.Ky=void 0;this.oz=this.pz=!0;this.DA=this.RB=!1;this.Fv=!0;this.jD=document.createElement("div");wYa(this);this.Co=this.targetElement=
this.element;this.xp=ZO;Object.defineProperties(this,{xp:{value:ZO,writable:!1}});this.Wi=this.xp?OYa:NYa;this.element.addEventListener("focus",e=>{this.jA(e)},!0);this.element.addEventListener("resize",e=>{this.yq.set("anchorPoint",new _.Zk(0,-e.detail.height))});this.kl=(new gO).element;this.rj=document.createElement("div");_.el(this.rj,"content-container");this.Ok=document.createElement("slot");this.Ok.addEventListener("slotchange",()=>{this.Nr()});this.rj.appendChild(this.Ok);this.Ok.prepend(this.kl);
this.Nr();Promise.resolve().then(()=>{MO(this)});this.sD=getComputedStyle(this.element);this.jJ=(e,f,g)=>this.sx(e,f,g);const b=()=>{MO(this);NO(this);const e=_.Wj(this,"gmp-click");this.Wi.Fg(this.map,e)},c=()=>{MO(this);NO(this)},d=["click"];d.push("gmp-click");for(const e of d)HWa(this,e,b),GWa(this,e,c);this.yq=new _.kk;this.eE=e=>{this.Xj?.Qq(e==="cooperative"||e==="none")};this.collisionBehavior=a.collisionBehavior;a.content!=null&&(this.content=a.content);this.Cz=!!a.Cz;this.gmpClickable=a.gmpClickable;
this.gmpDraggable=a.gmpDraggable;this.position=a.position;this.title=a.title??"";this.zIndex=a.zIndex;this.map=a.map;this.Wh(a,$O,"AdvancedMarkerElement")}addEventListener(a,b,c){a!=="click"||this.RB||_.Pj(_.Jl(this,"Please use addEventListener('gmp-click', ...) instead of addEventListener('click', ...)."));super.addEventListener(a,b,c)}addListener(a,b){a==="click"&&_.Pj(_.Jl(this,"Please use addEventListener('gmp-click', ...) instead of addEventListener('click', ...)."));return _.Vj(this,a,b)}jA(a){var b=
a.target,c=a.relatedTarget;if(this.element!==b)if(a.stopPropagation(),a.stopImmediatePropagation(),console.debug('Focusable child elements in AdvancedMarkerElement are not supported. To make AdvancedMarkerElement focusable, use addListener() to register a "click" event on the AdvancedMarkerElement instance.'),this.Wi.Qg(this.map),a=[document.body,..._.su(document.body)],b=a.indexOf(b),c=a.indexOf(c),b===-1||c===-1)this.element.focus();else for(c=b>c?1:-1,b+=c;b>=0&&b<a.length;b+=c){const d=a[b];if(this.xm&&
d===this.element||!this.element.contains(d)){(d instanceof HTMLElement||d instanceof SVGElement)&&d.focus();break}}}Ex(a){this.Mi&&this.Mi.Ex();xYa(this,a)}Cs(a){this.Mi&&this.Mi.Cs(a)}Bs(a){this.Mi&&this.Mi.Bs(a)}XD(){return new Promise(a=>{if(this.xm){var b=()=>{this.element.isConnected&&this.Rp?setTimeout(()=>{this.element.focus();a()},0):_.Mx(_.Lx(),b)};b()}})}xt(){}Dx(a){this.Mi&&(this.Mi.Dx(a,!this.xp),this.xp&&_.hk(this,"dragcancel"))}get collisionBehavior(){return this.yC}set collisionBehavior(a){a=
this.mh("collisionBehavior",_.Bj(_.uj(_.Tp)),a)||"REQUIRED";this.collisionBehavior!==a&&(this.yC=a,this.Wi.Gg(this.map,this.yC),this.map&&(!BO(this)&&this.Fh?CWa(this.Fh.Vg,this):LO(this,!0)))}get element(){return this}get Mx(){return PO(this)[0]===this.kl}get content(){const a=PO(this);a.length>1&&console.debug("The content getter of AdvancedMarkerElement only returns the first content when there are multiple contents, use childNodes or children to get all the contents.");return a[0]}set content(a){var b=
_.Bj(_.zj([_.tj(Node,"Node"),_.yj(_.sj)]));if(a instanceof gO)throw _.pj(_.Jl(this,"`content` invalid: PinElement must currently be assigned as `pinElement.element`."));a=this.mh("content",b,a)||this.kl;b=PO(this);if(b.length!==1||b[0]!==a)a!==this.kl?this.replaceChildren(a):a===this.kl&&this.replaceChildren(),this.Nr()}Nr(){const a=()=>{this.Ok&&!this.Ok.contains(this.kl)&&this.Ok.prepend(this.kl);this.Hk=null;this.Mi&&IO(this.Mi);LO(this,!0);MO(this)};this.Wi.Ig(this.map,this.Mx);this.Yq.then(()=>
{a()})}get dragIndicator(){}set dragIndicator(a){}get gmpClickable(){return this.AC}set gmpClickable(a){a=this.mh("gmpClickable",_.Hp,a)||!1;this.AC!==a&&(this.AC=a,MO(this),NO(this))}get gmpDraggable(){return this.hu}set gmpDraggable(a){a=this.mh("gmpDraggable",_.Hp,a)||!1;zYa(this,this.position,a);this.rE.set(a);this.hu!==a&&((this.hu=a)?(this.Wi.Jg(this.map),this.element.setAttribute("aria-grabbed","false"),this.Kw(this.Fz),this.Mi=new PYa(this),lYa(this.Mi)):(this.element.removeAttribute("aria-grabbed"),
this.by(this.Fz),this.Mi.dispose(),this.Mi=null),MO(this),NO(this))}Kw(a){this.Fz=a;if(this.hu){var b=this.element.getAttribute("aria-describedby");b=b?b.split(" "):[];b.push(a);this.element.setAttribute("aria-describedby",b.join(" "))}}by(a){var b=this.element.getAttribute("aria-describedby");b=(b?b.split(" "):[]).filter(c=>c!==a);b.length>0?this.element.setAttribute("aria-describedby",b.join(" ")):this.element.removeAttribute("aria-describedby")}get map(){return this.Jj}set map(a){this.setMap(a);
this.Jj&&(a=_.Dk(this.Jj))&&this.parentElement!==a&&a.append(this)}setMap(a){this.Jj!==a&&(a=this.mh("map",_.Bj(_.tj(_.Ck,"MapsApiMap")),a),a instanceof _.Ck&&(a=a.cM),a&&this.element.isConnected?QO(this):this.dispose(),this.Jj=a,this.yq.set("map",this.Jj),this.Jj instanceof _.Ck?(this.Jj&&gYa(this,this.Jj),this.Fh=this.Jj.__gm,yYa(this),this.lD=this.Jj.addListener("bounds_changed",()=>{RO(this)}),this.wG=this.Jj.addListener("zoom_changed",()=>{RO(this)}),this.sF=this.Jj.addListener("projection_changed",
()=>{RO(this)}),this.WE=this.Jj.addListener("maptypeid_changed",()=>{BYa(this)}),Promise.all([this.Fh.Fg,this.Fh.Gg]).then(([b,c])=>{this.Jj===b.map&&(this.HE=c,this.Wi.Rg(b.map),c=this.Fh.Eg,this.xp||_.jm(c,"ADVANCED_MARKERS").isAvailable)&&(this.ah=b.ah,BYa(this))}),CYa(this),DYa(this)):this.Fh=null)}get position(){return this.iu}set position(a){a=(a=this.mh("position",_.Bj(_.er),a)||null)&&new _.Qp(a);const b=this.iu;zYa(this,a,this.gmpDraggable);if(b&&a){var c=new _.Qp(b,!0);const d=new _.Qp(a,
!0);c=!c.equals(d)}else c=b!==a;c&&(this.Oo=(this.iu=a)?new _.Hj(a):null,this.pB=!0,a||SO(this,null),this.yq.set("position",this.Oo),this.yw?hYa(this.map):AYa(this),this.Vk()>0&&this.Wi.Eg(this.map),_.El(this,"position",b))}get oK(){return this.iu}get title(){return this.br}set title(a){a=this.mh("title",_.Jo,a);const b=this.br;a!==this.title&&(this.br=a,this.title&&this.Wi.Kg(this.map),this.title===""?(this.element.removeAttribute("aria-label"),this.element.removeAttribute("title")):(this.element.setAttribute("aria-label",
this.title),this.element.setAttribute("title",this.title)),this.fw(),_.El(this,"title",b))}get zIndex(){return this.Ky}set zIndex(a){a=this.mh("zIndex",_.Bj(_.Cp),a);this.Ky=a==null?null:a;this.element.style.zIndex=this.Ky==null?"":`${this.Ky}`;this.zIndex!==null&&this.Wi.Lg(this.map);LO(this)}get qv(){const a=_.Wj(this,"click"),b=_.Wj(this,"gmp-click");return a||b||!!this.gmpClickable}get FE(){return this.qv||!!this.gmpDraggable}get xm(){return this.zC}set xm(a){vYa(this);this.zC!==a&&(this.zC=a,
RO(this))}get tv(){return this.Jy}set tv(a){if(a!==this.Jy){if(this.Jy=a)this.oz=this.pz=!1,JO(this);this.map&&iYa(this,this.map)}}get Ln(){return this.Iy}set Ln(a){a!==this.Iy&&(this.Iy=a,this.map&&iYa(this,this.map),RO(this),_.hk(this,"UPDATE_BASEMAP_COLLISION"))}gv(){if(!this.Rp)return null;if(!this.Hk)for(const c of PO(this)){var a=this.sD;const {offset:d,size:e}=KWa(this.element,c);var b=LWa(a);a=b.offsetY+d.y;b=b.offsetX+d.x;a=_.Zl(b,a,b+e.width,a+e.height);this.Hk?this.Hk.extendByBounds(a):
this.Hk=a}return this.Hk}Vk(){return this.iu?this.iu.altitude:0}sx(a,b,c){return this.Jj?(c=_.LFa(this.Jj.getProjection(),this.Oo,c))?a/c*Math.sin(b*Math.PI/180):0:0}Yn(a,b,c){if(a){if(this.Mi){b=this.Mi;var d=b.Eg;b=(d=d.Fh?d.Fh.np:null)&&b.Kg&&b.Ig&&!b.Jg?MWa(d,b.Kg):null}else b=null;b&&(a=b);this.yv=a;this.tv=!(!c||!(Math.abs(a.hh)>c.hh/2+512||Math.abs(a.jh)>c.jh/2+512));this.tv||(this.Fv&&this.map&&(c=_.ra(this.map),(c=DO.get(c))&&dYa(c,this)),(new _.Zk(a.hh,a.jh)).equals(this.Rp)||(SO(this,new _.Zk(a.hh,
a.jh)),this.Gy(this.pB)),this.pB=!1,this.oz=this.pz=!0)}else this.tv=!0,this.yv=null,SO(this,null)}Gy(a){this.Hk=null;this.Mi&&this.Mi.Gg&&this.Mi.Gy(this.gv());LO(this,a)}Wl(a){this.BC!==a&&(this.BC=a,this.yq.set("map",this.BC?this.map:null))}yx(){if(!BO(this)||this.Ln||!PO(this).length)return null;var a=this.map.getProjection();if(!a)return null;a=a.fromLatLngToPoint(this.Oo);const b=[];for(const g of PO(this)){a:{var c=this.element,d=g;var e=this.Rp;var f=this.sD;if(!e){e={size:new _.al(0,0),offset:new _.Zk(0,
0)};break a}const {size:n,offset:p}=KWa(c,d);c=LWa(f);e={size:n,offset:new _.Zk(c.offsetX-e.x+p.x,c.offsetY-e.y+p.y)}}const {size:h,offset:l}=e;e=new GYa(a.x,a.y,h.width,h.height,l.x,l.y);b.push(e)}return b}rs(){}rp(){return this.element}EE(a){return!this.position||this.Iy?!1:VWa(a,this.element)}fw(){const a=this.rp();this.xm?a.setAttribute("role","button"):this.title?a.setAttribute("role","img"):a.removeAttribute("role")}get Ek(){return this.Mi?this.Mi.Ek():!1}Tl(){SO(this,null);JO(this);this.pz&&
this.ah&&this.Hj&&(this.ah.xl(this.Hj),this.Hj=null);this.element.remove();this.Fv=!0}dispose(){if(!this.DA){this.DA=!0;try{this.Jj&&(QO(this),this.Tl())}finally{this.DA=!1}}}LB(a){{const c=this.Fh?.get("projectionController");if(this.Fh&&a&&c){var b=this.Fh.np.getBoundingClientRect();a=c.fromContainerPixelToLatLng(new _.Zk(a.clientX-b.left,a.clientY-b.top))}else a=null}a&&(this.position=a)}en(){var a=this.Fh?.get("projectionController");if(!this.Fh||!a||!this.Oo)return null;a=a.fromLatLngToContainerPixel(this.Oo);
const b=this.Fh.np.getBoundingClientRect();return{clientX:a.x+b.left,clientY:a.y+b.top}}connectedCallback(){super.connectedCallback();this.ki.appendChild(this.rj);this.Mi&&IO(this.Mi);if(this.isConnected&&this.parentNode){const a=aO(this);a?this.setMap(a):(QO(this),console.error("AdvancedMarkerElement: parent element must be a <gmp-map>."))}}disconnectedCallback(){!this.isConnected&&this.oz&&(this.map=null);this.Fv=!0;super.disconnectedCallback()}};$O.prototype.addListener=$O.prototype.addListener;
$O.prototype.addEventListener=$O.prototype.addEventListener;$O.prototype.constructor=$O.prototype.constructor;$O.pi={si:181577,ri:181576};_.Ea([_.jo({fh:"gmp-clickable",type:Boolean,nh:!0}),_.M("design:type",Object),_.M("design:paramtypes",[Object])],$O.prototype,"gmpClickable",null);_.Ea([_.jo({ci:_.aq,Mj:_.wl,nh:!0}),_.M("design:type",Object),_.M("design:paramtypes",[Object])],$O.prototype,"position",null);
_.Ea([_.jo({ci:{ml:a=>a||"",Ik:a=>a||null},nh:!0}),_.M("design:type",String),_.M("design:paramtypes",[String])],$O.prototype,"title",null);var QYa=!1,RYa=class extends $O{constructor(a={}){super(a);if(!ZO)throw Error("InternalUseAdvancedMarkerView is not allowed to be instantiated directly.");}};_.ul("gmp-internal-use-am",RYa);var aP={Marker:_.kl,CollisionBehavior:_.Tp,Animation:_.gja,kI:()=>{},jz:function(a,b,c){const d=_.DHa();if(b instanceof _.jl)FXa(a,b,d);else{const e=new _.Bm;FXa(e,b,d);const f=new _.Bm;c||XXa(f,b,d);new KYa(a,f,e,c)}},vD:function(a={}){ZO=!0;a=new RYa(a);ZO=!1;return a},AdvancedMarkerElement:$O,PinElement:gO,AdvancedMarkerClickEvent:OO,AdvancedMarkerView:void 0,PinView:void 0,connectForExplicitThirdPartyLoad:()=>{const a={AdvancedMarkerElement:$O,PinElement:gO,AdvancedMarkerClickEvent:OO,AdvancedMarkerView:void 0,
PinView:void 0};_.jj(a);_.ka.google.maps.marker=a;QYa||(QYa=!0,_.ul("gmp-advanced-marker",$O))}};_.kj(aP,["kI","jz","vD","connectForExplicitThirdPartyLoad"]);_.jj(aP);_.Ki("marker",aP);});
