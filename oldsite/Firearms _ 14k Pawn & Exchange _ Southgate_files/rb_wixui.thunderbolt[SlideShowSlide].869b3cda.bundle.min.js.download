!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("react")):"function"==typeof define&&define.amd?define("rb_wixui.thunderbolt[SlideShowSlide]",["react"],t):"object"==typeof exports?exports["rb_wixui.thunderbolt[SlideShowSlide]"]=t(require("react")):e["rb_wixui.thunderbolt[SlideShowSlide]"]=t(e.React)}("undefined"!=typeof self?self:this,(function(e){return function(){var t={5329:function(t){"use strict";t.exports=e},448:function(e){function t(){return e.exports=t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var a in i)({}).hasOwnProperty.call(i,a)&&(e[a]=i[a])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,t.apply(null,arguments)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}},i={};function a(e){var n=i[e];if(void 0!==n)return n.exports;var r=i[e]={exports:{}};return t[e](r,r.exports,a),r.exports}a.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return a.d(t,{a:t}),t},a.d=function(e,t){for(var i in t)a.o(t,i)&&!a.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:t[i]})},a.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},a.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};return function(){"use strict";a.r(n),a.d(n,{components:function(){return Jt}});var e={};a.r(e),a.d(e,{STATIC_MEDIA_URL:function(){return rt},ph:function(){return it}});var t=a(448),i=a.n(t),r=a(5329),o=a.n(r);function c(e){var t,i,a="";if("string"==typeof e||"number"==typeof e)a+=e;else if("object"==typeof e)if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(i=c(e[t]))&&(a&&(a+=" "),a+=i);else for(t in e)e[t]&&(a&&(a+=" "),a+=t);return a}var s=function(){for(var e,t,i=0,a="";i<arguments.length;)(e=arguments[i++])&&(t=c(e))&&(a&&(a+=" "),a+=t);return a};const d=13,l=27;function h(e){return t=>{t.keyCode===e&&(t.preventDefault(),t.stopPropagation(),t.currentTarget.click())}}h(32),h(d),h(l);const u=["aria-id","aria-metadata","aria-type"],g=(e,t)=>Object.entries(e).reduce(((e,[i,a])=>(t.includes(i)||(e[i]=a),e)),{});const m=e=>Object.entries(e).reduce(((e,[t,i])=>(t.includes("data-")&&(e[t]=i),e)),{});const _="mesh-container-content",p="inline-content",f=e=>o().createElement("div",{key:e.props.id+"-rotated-wrapper","data-mesh-id":e.props.id+"-rotated-wrapper"},e),T=(e,t)=>{const{id:a,className:n,wedges:r=[],rotatedComponents:c=[],children:d,fixedComponents:l=[],extraClassName:h="",renderRotatedComponents:u=f}=e,g=o().Children.toArray(d()),T=[],I=[];g.forEach((e=>l.includes(e.props.id)?T.push(e):I.push(e)));const E=(e=>{const{wedges:t,rotatedComponents:i,childrenArray:a,renderRotatedComponents:n}=e,r=i.reduce(((e,t)=>({...e,[t]:!0})),{});return[...a.map((e=>{return r[(t=e,t.props.id.split("__")[0])]?n(e):e;var t})),...t.map((e=>o().createElement("div",{key:e,"data-mesh-id":e})))]})({childrenArray:I,rotatedComponents:c,wedges:r,renderRotatedComponents:u});return o().createElement("div",i()({},m(e),{"data-mesh-id":a+"inlineContent","data-testid":p,className:s(n,h),ref:t}),o().createElement("div",{"data-mesh-id":a+"inlineContent-gridContainer","data-testid":_},E),T)};var I=o().forwardRef(T),E="jhxvbR";const L="v1",A=2,b=1920,w=1920,O=1e3,M=1e3,y={SCALE_TO_FILL:"fill",SCALE_TO_FIT:"fit",STRETCH:"stretch",ORIGINAL_SIZE:"original_size",TILE:"tile",TILE_HORIZONTAL:"tile_horizontal",TILE_VERTICAL:"tile_vertical",FIT_AND_TILE:"fit_and_tile",LEGACY_STRIP_TILE:"legacy_strip_tile",LEGACY_STRIP_TILE_HORIZONTAL:"legacy_strip_tile_horizontal",LEGACY_STRIP_TILE_VERTICAL:"legacy_strip_tile_vertical",LEGACY_STRIP_SCALE_TO_FILL:"legacy_strip_fill",LEGACY_STRIP_SCALE_TO_FIT:"legacy_strip_fit",LEGACY_STRIP_FIT_AND_TILE:"legacy_strip_fit_and_tile",LEGACY_STRIP_ORIGINAL_SIZE:"legacy_strip_original_size",LEGACY_ORIGINAL_SIZE:"actual_size",LEGACY_FIT_WIDTH:"fitWidth",LEGACY_FIT_HEIGHT:"fitHeight",LEGACY_FULL:"full",LEGACY_BG_FIT_AND_TILE:"legacy_tile",LEGACY_BG_FIT_AND_TILE_HORIZONTAL:"legacy_tile_horizontal",LEGACY_BG_FIT_AND_TILE_VERTICAL:"legacy_tile_vertical",LEGACY_BG_NORMAL:"legacy_normal"},R={FIT:"fit",FILL:"fill",FILL_FOCAL:"fill_focal",CROP:"crop",LEGACY_CROP:"legacy_crop",LEGACY_FILL:"legacy_fill"},S={CENTER:"center",TOP:"top",TOP_LEFT:"top_left",TOP_RIGHT:"top_right",BOTTOM:"bottom",BOTTOM_LEFT:"bottom_left",BOTTOM_RIGHT:"bottom_right",LEFT:"left",RIGHT:"right"},G={[S.CENTER]:{x:.5,y:.5},[S.TOP_LEFT]:{x:0,y:0},[S.TOP_RIGHT]:{x:1,y:0},[S.TOP]:{x:.5,y:0},[S.BOTTOM_LEFT]:{x:0,y:1},[S.BOTTOM_RIGHT]:{x:1,y:1},[S.BOTTOM]:{x:.5,y:1},[S.RIGHT]:{x:1,y:.5},[S.LEFT]:{x:0,y:.5}},C={center:"c",top:"t",top_left:"tl",top_right:"tr",bottom:"b",bottom_left:"bl",bottom_right:"br",left:"l",right:"r"},N={BG:"bg",IMG:"img",SVG:"svg"},F={AUTO:"auto",CLASSIC:"classic",SUPER:"super"},v={classic:1,super:2},P={radius:"0.66",amount:"1.00",threshold:"0.01"},x={uri:"",css:{img:{},container:{}},attr:{img:{},container:{}},transformed:!1},k=25e6,H=[1.5,2,4],Y={HIGH:{size:196e4,quality:90,maxUpscale:1},MEDIUM:{size:36e4,quality:85,maxUpscale:1},LOW:{size:16e4,quality:80,maxUpscale:1.2},TINY:{size:0,quality:80,maxUpscale:1.4}},B={HIGH:"HIGH",MEDIUM:"MEDIUM",LOW:"LOW",TINY:"TINY"},U={CONTRAST:"contrast",BRIGHTNESS:"brightness",SATURATION:"saturation",HUE:"hue",BLUR:"blur"},$={JPG:"jpg",JPEG:"jpeg",JPE:"jpe",PNG:"png",WEBP:"webp",WIX_ICO_MP:"wix_ico_mp",WIX_MP:"wix_mp",GIF:"gif",SVG:"svg",AVIF:"avif",UNRECOGNIZED:"unrecognized"},j={AVIF:"AVIF",PAVIF:"PAVIF"};$.JPG,$.JPEG,$.JPE,$.PNG,$.GIF,$.WEBP;function z(e,...t){return function(...i){const a=i[i.length-1]||{},n=[e[0]];return t.forEach((function(t,r){const o=Number.isInteger(t)?i[t]:a[t];n.push(o,e[r+1])})),n.join("")}}function V(e){return e[e.length-1]}const D=[$.PNG,$.JPEG,$.JPG,$.JPE,$.WIX_ICO_MP,$.WIX_MP,$.WEBP,$.AVIF],W=[$.JPEG,$.JPG,$.JPE];function Z(e,t,i){return i&&t&&!(!(a=t.id)||!a.trim()||"none"===a.toLowerCase())&&Object.values(y).includes(e);var a}function q(e,t,i){return function(e,t,i=!1){return!((X(e)||Q(e))&&t&&!i)}(e,t,i)&&(function(e){return D.includes(ne(e))}(e)||function(e,t=!1){return K(e)&&t}(e,i))&&!/(^https?)|(^data)|(^\/\/)/.test(e)}function J(e){return ne(e)===$.PNG}function X(e){return ne(e)===$.WEBP}function K(e){return ne(e)===$.GIF}function Q(e){return ne(e)===$.AVIF}const ee=["/","\\","?","<",">","|","\u201c",":",'"'].map(encodeURIComponent),te=["\\.","\\*"],ie="_";function ae(e){return function(e){return W.includes(ne(e))}(e)?$.JPG:J(e)?$.PNG:X(e)?$.WEBP:K(e)?$.GIF:Q(e)?$.AVIF:$.UNRECOGNIZED}function ne(e){return(/[.]([^.]+)$/.exec(e)&&/[.]([^.]+)$/.exec(e)[1]||"").toLowerCase()}function re(e,t,i,a,n){let r;return r=n===R.FILL?function(e,t,i,a){return Math.max(i/e,a/t)}(e,t,i,a):n===R.FIT?function(e,t,i,a){return Math.min(i/e,a/t)}(e,t,i,a):1,r}function oe(e,t,i,a,n,r){e=e||a.width,t=t||a.height;const{scaleFactor:o,width:c,height:s}=function(e,t,i,a,n){let r,o=i,c=a;if(r=re(e,t,i,a,n),n===R.FIT&&(o=e*r,c=t*r),o&&c&&o*c>k){const i=Math.sqrt(k/(o*c));o*=i,c*=i,r=re(e,t,o,c,n)}return{scaleFactor:r,width:o,height:c}}(e,t,a.width*n,a.height*n,i);return function(e,t,i,a,n,r,o){const{optimizedScaleFactor:c,upscaleMethodValue:s,forceUSM:d}=function(e,t,i,a){if("auto"===a)return function(e,t){const i=le(e,t);return{optimizedScaleFactor:Y[i].maxUpscale,upscaleMethodValue:v.classic,forceUSM:!1}}(e,t);if("super"===a)return function(e){return{optimizedScaleFactor:V(H),upscaleMethodValue:v.super,forceUSM:!(H.includes(e)||e>V(H))}}(i);return function(e,t){const i=le(e,t);return{optimizedScaleFactor:Y[i].maxUpscale,upscaleMethodValue:v.classic,forceUSM:!1}}(e,t)}(e,t,r,n);let l=i,h=a;if(r<=c)return{width:l,height:h,scaleFactor:r,upscaleMethodValue:s,forceUSM:d,cssUpscaleNeeded:!1};switch(o){case R.FILL:l=i*(c/r),h=a*(c/r);break;case R.FIT:l=e*c,h=t*c}return{width:l,height:h,scaleFactor:c,upscaleMethodValue:s,forceUSM:d,cssUpscaleNeeded:!0}}(e,t,c,s,r,o,i)}function ce(e,t,i,a){const n=de(i)||function(e=S.CENTER){return G[e]}(a);return{x:Math.max(0,Math.min(e.width-t.width,n.x*e.width-t.width/2)),y:Math.max(0,Math.min(e.height-t.height,n.y*e.height-t.height/2)),width:Math.min(e.width,t.width),height:Math.min(e.height,t.height)}}function se(e){return e.alignment&&C[e.alignment]||C[S.CENTER]}function de(e){let t;return!e||"number"!=typeof e.x||isNaN(e.x)||"number"!=typeof e.y||isNaN(e.y)||(t={x:he(Math.max(0,Math.min(100,e.x))/100,2),y:he(Math.max(0,Math.min(100,e.y))/100,2)}),t}function le(e,t){const i=e*t;return i>Y[B.HIGH].size?B.HIGH:i>Y[B.MEDIUM].size?B.MEDIUM:i>Y[B.LOW].size?B.LOW:B.TINY}function he(e,t){const i=Math.pow(10,t||0);return(e*i/i).toFixed(t)}function ue(e){return e&&e.upscaleMethod&&F[e.upscaleMethod.toUpperCase()]||F.AUTO}function ge(e,t){const i=X(e)||Q(e);return ne(e)===$.GIF||i&&t}const me={isMobile:!1},_e=function(e){return me[e]};function pe(){if("undefined"!=typeof window&&"undefined"!=typeof navigator){const t=window.matchMedia&&window.matchMedia("(max-width: 767px)").matches,i=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);e=t&&i,me["isMobile"]=e}var e}function fe(e,t){const i={css:{container:{}}},{css:a}=i,{fittingType:n}=e;switch(n){case y.ORIGINAL_SIZE:case y.LEGACY_ORIGINAL_SIZE:case y.LEGACY_STRIP_ORIGINAL_SIZE:a.container.backgroundSize="auto",a.container.backgroundRepeat="no-repeat";break;case y.SCALE_TO_FIT:case y.LEGACY_STRIP_SCALE_TO_FIT:a.container.backgroundSize="contain",a.container.backgroundRepeat="no-repeat";break;case y.STRETCH:a.container.backgroundSize="100% 100%",a.container.backgroundRepeat="no-repeat";break;case y.SCALE_TO_FILL:case y.LEGACY_STRIP_SCALE_TO_FILL:a.container.backgroundSize="cover",a.container.backgroundRepeat="no-repeat";break;case y.TILE_HORIZONTAL:case y.LEGACY_STRIP_TILE_HORIZONTAL:a.container.backgroundSize="auto",a.container.backgroundRepeat="repeat-x";break;case y.TILE_VERTICAL:case y.LEGACY_STRIP_TILE_VERTICAL:a.container.backgroundSize="auto",a.container.backgroundRepeat="repeat-y";break;case y.TILE:case y.LEGACY_STRIP_TILE:a.container.backgroundSize="auto",a.container.backgroundRepeat="repeat";break;case y.LEGACY_STRIP_FIT_AND_TILE:a.container.backgroundSize="contain",a.container.backgroundRepeat="repeat";break;case y.FIT_AND_TILE:case y.LEGACY_BG_FIT_AND_TILE:a.container.backgroundSize="auto",a.container.backgroundRepeat="repeat";break;case y.LEGACY_BG_FIT_AND_TILE_HORIZONTAL:a.container.backgroundSize="auto",a.container.backgroundRepeat="repeat-x";break;case y.LEGACY_BG_FIT_AND_TILE_VERTICAL:a.container.backgroundSize="auto",a.container.backgroundRepeat="repeat-y";break;case y.LEGACY_BG_NORMAL:a.container.backgroundSize="auto",a.container.backgroundRepeat="no-repeat"}switch(t.alignment){case S.CENTER:a.container.backgroundPosition="center center";break;case S.LEFT:a.container.backgroundPosition="left center";break;case S.RIGHT:a.container.backgroundPosition="right center";break;case S.TOP:a.container.backgroundPosition="center top";break;case S.BOTTOM:a.container.backgroundPosition="center bottom";break;case S.TOP_RIGHT:a.container.backgroundPosition="right top";break;case S.TOP_LEFT:a.container.backgroundPosition="left top";break;case S.BOTTOM_RIGHT:a.container.backgroundPosition="right bottom";break;case S.BOTTOM_LEFT:a.container.backgroundPosition="left bottom"}return i}const Te={[S.CENTER]:"center",[S.TOP]:"top",[S.TOP_LEFT]:"top left",[S.TOP_RIGHT]:"top right",[S.BOTTOM]:"bottom",[S.BOTTOM_LEFT]:"bottom left",[S.BOTTOM_RIGHT]:"bottom right",[S.LEFT]:"left",[S.RIGHT]:"right"},Ie={position:"absolute",top:"auto",right:"auto",bottom:"auto",left:"auto"};function Ee(e,t){const i={css:{container:{},img:{}}},{css:a}=i,{fittingType:n}=e,r=t.alignment;switch(a.container.position="relative",n){case y.ORIGINAL_SIZE:case y.LEGACY_ORIGINAL_SIZE:e.parts&&e.parts.length?(a.img.width=e.parts[0].width,a.img.height=e.parts[0].height):(a.img.width=e.src.width,a.img.height=e.src.height);break;case y.SCALE_TO_FIT:case y.LEGACY_FIT_WIDTH:case y.LEGACY_FIT_HEIGHT:case y.LEGACY_FULL:a.img.width=t.width,a.img.height=t.height,a.img.objectFit="contain",a.img.objectPosition=Te[r]||"unset";break;case y.LEGACY_BG_NORMAL:a.img.width="100%",a.img.height="100%",a.img.objectFit="none",a.img.objectPosition=Te[r]||"unset";break;case y.STRETCH:a.img.width=t.width,a.img.height=t.height,a.img.objectFit="fill";break;case y.SCALE_TO_FILL:a.img.width=t.width,a.img.height=t.height,a.img.objectFit="cover"}if("number"==typeof a.img.width&&"number"==typeof a.img.height&&(a.img.width!==t.width||a.img.height!==t.height)){const e=Math.round((t.height-a.img.height)/2),i=Math.round((t.width-a.img.width)/2);Object.assign(a.img,Ie,function(e,t,i){return{[S.TOP_LEFT]:{top:0,left:0},[S.TOP_RIGHT]:{top:0,right:0},[S.TOP]:{top:0,left:t},[S.BOTTOM_LEFT]:{bottom:0,left:0},[S.BOTTOM_RIGHT]:{bottom:0,right:0},[S.BOTTOM]:{bottom:0,left:t},[S.RIGHT]:{top:e,right:0},[S.LEFT]:{top:e,left:0},[S.CENTER]:{width:i.width,height:i.height,objectFit:"none"}}}(e,i,t)[r])}return i}function Le(e,t){const i={css:{container:{}},attr:{container:{},img:{}}},{css:a,attr:n}=i,{fittingType:r}=e,o=t.alignment,{width:c,height:s}=e.src;let d;switch(a.container.position="relative",r){case y.ORIGINAL_SIZE:case y.LEGACY_ORIGINAL_SIZE:case y.TILE:e.parts&&e.parts.length?(n.img.width=e.parts[0].width,n.img.height=e.parts[0].height):(n.img.width=c,n.img.height=s),n.img.preserveAspectRatio="xMidYMid slice";break;case y.SCALE_TO_FIT:case y.LEGACY_FIT_WIDTH:case y.LEGACY_FIT_HEIGHT:case y.LEGACY_FULL:n.img.width="100%",n.img.height="100%",n.img.transform="",n.img.preserveAspectRatio="";break;case y.STRETCH:n.img.width=t.width,n.img.height=t.height,n.img.x=0,n.img.y=0,n.img.transform="",n.img.preserveAspectRatio="none";break;case y.SCALE_TO_FILL:q(e.src.id)?(n.img.width=t.width,n.img.height=t.height):(d=function(e,t,i,a,n){const r=re(e,t,i,a,n);return{width:Math.round(e*r),height:Math.round(t*r)}}(c,s,t.width,t.height,R.FILL),n.img.width=d.width,n.img.height=d.height),n.img.x=0,n.img.y=0,n.img.transform="",n.img.preserveAspectRatio="xMidYMid slice"}if("number"==typeof n.img.width&&"number"==typeof n.img.height&&(n.img.width!==t.width||n.img.height!==t.height)){let e,i,a=0,c=0;r===y.TILE?(e=t.width%n.img.width,i=t.height%n.img.height):(e=t.width-n.img.width,i=t.height-n.img.height);const s=Math.round(e/2),d=Math.round(i/2);switch(o){case S.TOP_LEFT:a=0,c=0;break;case S.TOP:a=s,c=0;break;case S.TOP_RIGHT:a=e,c=0;break;case S.LEFT:a=0,c=d;break;case S.CENTER:a=s,c=d;break;case S.RIGHT:a=e,c=d;break;case S.BOTTOM_LEFT:a=0,c=i;break;case S.BOTTOM:a=s,c=i;break;case S.BOTTOM_RIGHT:a=e,c=i}n.img.x=a,n.img.y=c}return n.container.width=t.width,n.container.height=t.height,n.container.viewBox=[0,0,t.width,t.height].join(" "),i}function Ae(e,t,i){let a;switch(t.crop&&(a=function(e,t){const i=Math.max(0,Math.min(e.width,t.x+t.width)-Math.max(0,t.x)),a=Math.max(0,Math.min(e.height,t.y+t.height)-Math.max(0,t.y));return i&&a&&(e.width!==i||e.height!==a)?{x:Math.max(0,t.x),y:Math.max(0,t.y),width:i,height:a}:null}(t,t.crop),a&&(e.src.width=a.width,e.src.height=a.height,e.src.isCropped=!0,e.parts.push(we(a)))),e.fittingType){case y.SCALE_TO_FIT:case y.LEGACY_FIT_WIDTH:case y.LEGACY_FIT_HEIGHT:case y.LEGACY_FULL:case y.FIT_AND_TILE:case y.LEGACY_BG_FIT_AND_TILE:case y.LEGACY_BG_FIT_AND_TILE_HORIZONTAL:case y.LEGACY_BG_FIT_AND_TILE_VERTICAL:case y.LEGACY_BG_NORMAL:e.parts.push(be(e,i));break;case y.SCALE_TO_FILL:e.parts.push(function(e,t){const i=oe(e.src.width,e.src.height,R.FILL,t,e.devicePixelRatio,e.upscaleMethod),a=de(e.focalPoint);return{transformType:a?R.FILL_FOCAL:R.FILL,width:Math.round(i.width),height:Math.round(i.height),alignment:se(t),focalPointX:a&&a.x,focalPointY:a&&a.y,upscale:i.scaleFactor>1,forceUSM:i.forceUSM,scaleFactor:i.scaleFactor,cssUpscaleNeeded:i.cssUpscaleNeeded,upscaleMethodValue:i.upscaleMethodValue}}(e,i));break;case y.STRETCH:e.parts.push(function(e,t){const i=re(e.src.width,e.src.height,t.width,t.height,R.FILL),a={...t};return a.width=e.src.width*i,a.height=e.src.height*i,be(e,a)}(e,i));break;case y.TILE_HORIZONTAL:case y.TILE_VERTICAL:case y.TILE:case y.LEGACY_ORIGINAL_SIZE:case y.ORIGINAL_SIZE:a=ce(e.src,i,e.focalPoint,i.alignment),e.src.isCropped?(Object.assign(e.parts[0],a),e.src.width=a.width,e.src.height=a.height):e.parts.push(we(a));break;case y.LEGACY_STRIP_TILE_HORIZONTAL:case y.LEGACY_STRIP_TILE_VERTICAL:case y.LEGACY_STRIP_TILE:case y.LEGACY_STRIP_ORIGINAL_SIZE:e.parts.push(function(e){return{transformType:R.LEGACY_CROP,width:Math.round(e.width),height:Math.round(e.height),alignment:se(e),upscale:!1,forceUSM:!1,scaleFactor:1,cssUpscaleNeeded:!1}}(i));break;case y.LEGACY_STRIP_SCALE_TO_FIT:case y.LEGACY_STRIP_FIT_AND_TILE:e.parts.push(function(e){return{transformType:R.FIT,width:Math.round(e.width),height:Math.round(e.height),upscale:!1,forceUSM:!0,scaleFactor:1,cssUpscaleNeeded:!1}}(i));break;case y.LEGACY_STRIP_SCALE_TO_FILL:e.parts.push(function(e){return{transformType:R.LEGACY_FILL,width:Math.round(e.width),height:Math.round(e.height),alignment:se(e),upscale:!1,forceUSM:!0,scaleFactor:1,cssUpscaleNeeded:!1}}(i))}}function be(e,t){const i=oe(e.src.width,e.src.height,R.FIT,t,e.devicePixelRatio,e.upscaleMethod);return{transformType:!e.src.width||!e.src.height?R.FIT:R.FILL,width:Math.round(i.width),height:Math.round(i.height),alignment:C.center,upscale:i.scaleFactor>1,forceUSM:i.forceUSM,scaleFactor:i.scaleFactor,cssUpscaleNeeded:i.cssUpscaleNeeded,upscaleMethodValue:i.upscaleMethodValue}}function we(e){return{transformType:R.CROP,x:Math.round(e.x),y:Math.round(e.y),width:Math.round(e.width),height:Math.round(e.height),upscale:!1,forceUSM:!1,scaleFactor:1,cssUpscaleNeeded:!1}}function Oe(e,t){t=t||{},e.quality=function(e,t){const i=e.fileType===$.PNG,a=e.fileType===$.JPG,n=e.fileType===$.WEBP,r=e.fileType===$.AVIF,o=a||i||n||r;if(o){const a=V(e.parts),n=(c=a.width,s=a.height,Y[le(c,s)].quality);let r=t.quality&&t.quality>=5&&t.quality<=90?t.quality:n;return r=i?r+5:r,r}var c,s;return 0}(e,t),e.progressive=function(e){return!1!==e.progressive}(t),e.watermark=function(e){return e.watermark}(t),e.autoEncode=t.autoEncode??!0,e.encoding=t?.encoding,e.unsharpMask=function(e,t){if(function(e){const t="number"==typeof(e=e||{}).radius&&!isNaN(e.radius)&&e.radius>=.1&&e.radius<=500,i="number"==typeof e.amount&&!isNaN(e.amount)&&e.amount>=0&&e.amount<=10,a="number"==typeof e.threshold&&!isNaN(e.threshold)&&e.threshold>=0&&e.threshold<=255;return t&&i&&a}(t.unsharpMask))return{radius:he(t.unsharpMask?.radius,2),amount:he(t.unsharpMask?.amount,2),threshold:he(t.unsharpMask?.threshold,2)};if(("number"!=typeof(i=(i=t.unsharpMask)||{}).radius||isNaN(i.radius)||0!==i.radius||"number"!=typeof i.amount||isNaN(i.amount)||0!==i.amount||"number"!=typeof i.threshold||isNaN(i.threshold)||0!==i.threshold)&&function(e){const t=V(e.parts);return!(t.scaleFactor>=1)||t.forceUSM||t.transformType===R.FIT}(e))return P;var i;return}(e,t),e.filters=function(e){const t=e.filters||{},i={};Me(t[U.CONTRAST],-100,100)&&(i[U.CONTRAST]=t[U.CONTRAST]);Me(t[U.BRIGHTNESS],-100,100)&&(i[U.BRIGHTNESS]=t[U.BRIGHTNESS]);Me(t[U.SATURATION],-100,100)&&(i[U.SATURATION]=t[U.SATURATION]);Me(t[U.HUE],-180,180)&&(i[U.HUE]=t[U.HUE]);Me(t[U.BLUR],0,100)&&(i[U.BLUR]=t[U.BLUR]);return i}(t)}function Me(e,t,i){return"number"==typeof e&&!isNaN(e)&&0!==e&&e>=t&&e<=i}function ye(e,t,i,a){const n=function(e){return e?.isSEOBot??!1}(a),r=ae(t.id),o=function(e,t){const i=/\.([^.]*)$/,a=new RegExp(`(${ee.concat(te).join("|")})`,"g");if(t&&t.length){let e=t;const n=t.match(i);return n&&D.includes(n[1])&&(e=t.replace(i,"")),encodeURIComponent(e).replace(a,ie)}const n=e.match(/\/(.*?)$/);return(n?n[1]:e).replace(i,"")}(t.id,t.name),c=n?1:function(e){return Math.min(e.pixelAspectRatio||1,A)}(i),s=ne(t.id),d=s,l=q(t.id,a?.hasAnimation,a?.allowAnimatedTransform),h={fileName:o,fileExtension:s,fileType:r,fittingType:e,preferredExtension:d,src:{id:t.id,width:t.width,height:t.height,isCropped:!1,isAnimated:ge(t.id,a?.hasAnimation)},focalPoint:{x:t.focalPoint&&t.focalPoint.x,y:t.focalPoint&&t.focalPoint.y},parts:[],devicePixelRatio:c,quality:0,upscaleMethod:ue(a),progressive:!0,watermark:"",unsharpMask:{},filters:{},transformed:l};return l&&(Ae(h,t,i),Oe(h,a)),h}function Re(e,t,i){const a={...i},n=_e("isMobile");switch(e){case y.LEGACY_BG_FIT_AND_TILE:case y.LEGACY_BG_FIT_AND_TILE_HORIZONTAL:case y.LEGACY_BG_FIT_AND_TILE_VERTICAL:case y.LEGACY_BG_NORMAL:const e=n?O:b,i=n?M:w;a.width=Math.min(e,t.width),a.height=Math.min(i,Math.round(a.width/(t.width/t.height))),a.pixelAspectRatio=1}return a}const Se=z`fit/w_${"width"},h_${"height"}`,Ge=z`fill/w_${"width"},h_${"height"},al_${"alignment"}`,Ce=z`fill/w_${"width"},h_${"height"},fp_${"focalPointX"}_${"focalPointY"}`,Ne=z`crop/x_${"x"},y_${"y"},w_${"width"},h_${"height"}`,Fe=z`crop/w_${"width"},h_${"height"},al_${"alignment"}`,ve=z`fill/w_${"width"},h_${"height"},al_${"alignment"}`,Pe=z`,lg_${"upscaleMethodValue"}`,xe=z`,q_${"quality"}`,ke=z`,quality_auto`,He=z`,usm_${"radius"}_${"amount"}_${"threshold"}`,Ye=z`,bl`,Be=z`,wm_${"watermark"}`,Ue={[U.CONTRAST]:z`,con_${"contrast"}`,[U.BRIGHTNESS]:z`,br_${"brightness"}`,[U.SATURATION]:z`,sat_${"saturation"}`,[U.HUE]:z`,hue_${"hue"}`,[U.BLUR]:z`,blur_${"blur"}`},$e=z`,enc_auto`,je=z`,enc_avif`,ze=z`,enc_pavif`,Ve=z`,pstr`;function De(e,t,i,a={},n){if(q(t.id,a?.hasAnimation,a?.allowAnimatedTransform)){if(X(t.id)||Q(t.id)){const{alignment:r,...o}=i;t.focalPoint={x:void 0,y:void 0},delete t?.crop,n=ye(e,t,o,a)}else n=n||ye(e,t,i,a);return function(e){const t=[];e.parts.forEach((e=>{switch(e.transformType){case R.CROP:t.push(Ne(e));break;case R.LEGACY_CROP:t.push(Fe(e));break;case R.LEGACY_FILL:let i=ve(e);e.upscale&&(i+=Pe(e)),t.push(i);break;case R.FIT:let a=Se(e);e.upscale&&(a+=Pe(e)),t.push(a);break;case R.FILL:let n=Ge(e);e.upscale&&(n+=Pe(e)),t.push(n);break;case R.FILL_FOCAL:let r=Ce(e);e.upscale&&(r+=Pe(e)),t.push(r)}}));let i=t.join("/");return e.quality&&(i+=xe(e)),e.unsharpMask&&(i+=He(e.unsharpMask)),e.progressive||(i+=Ye(e)),e.watermark&&(i+=Be(e)),e.filters&&(i+=Object.keys(e.filters).map((t=>Ue[t](e.filters))).join("")),e.fileType!==$.GIF&&(e.encoding===j.AVIF?(i+=je(e),i+=ke(e)):e.encoding===j.PAVIF?(i+=ze(e),i+=ke(e)):e.autoEncode&&(i+=$e(e))),e.src?.isAnimated&&e.transformed&&(i+=Ve(e)),`${e.src.id}/${L}/${i}/${e.fileName}.${e.preferredExtension}`}(n)}return t.id}const We={[S.CENTER]:"50% 50%",[S.TOP_LEFT]:"0% 0%",[S.TOP_RIGHT]:"100% 0%",[S.TOP]:"50% 0%",[S.BOTTOM_LEFT]:"0% 100%",[S.BOTTOM_RIGHT]:"100% 100%",[S.BOTTOM]:"50% 100%",[S.RIGHT]:"100% 50%",[S.LEFT]:"0% 50%"},Ze=Object.entries(We).reduce(((e,[t,i])=>(e[i]=t,e)),{}),qe=[y.TILE,y.TILE_HORIZONTAL,y.TILE_VERTICAL,y.LEGACY_BG_FIT_AND_TILE,y.LEGACY_BG_FIT_AND_TILE_HORIZONTAL,y.LEGACY_BG_FIT_AND_TILE_VERTICAL],Je=[y.LEGACY_ORIGINAL_SIZE,y.ORIGINAL_SIZE,y.LEGACY_BG_NORMAL];function Xe(e,t,{width:i,height:a}){return e===y.TILE&&t.width>i&&t.height>a}function Ke(e,{width:t,height:i}){if(!t||!i){const a=t||Math.min(980,e.width),n=a/e.width;return{width:a,height:i||e.height*n}}return{width:t,height:i}}function Qe(e,t,i,a="center"){const n={img:{},container:{}};if(e===y.SCALE_TO_FILL){const e=t.focalPoint&&function(e){const t=`${e.x}% ${e.y}%`;return Ze[t]||""}(t.focalPoint),r=e||a;t.focalPoint&&!e?n.img={objectPosition:et(t,i,t.focalPoint)}:n.img={objectPosition:We[r]}}else[y.LEGACY_ORIGINAL_SIZE,y.ORIGINAL_SIZE].includes(e)?n.img={objectFit:"none",top:"auto",left:"auto",right:"auto",bottom:"auto"}:qe.includes(e)&&(n.container={backgroundSize:`${t.width}px ${t.height}px`});return n}function et(e,t,i){const{width:a,height:n}=e,{width:r,height:o}=t,{x:c,y:s}=i;if(!r||!o)return`${c}% ${s}%`;const d=Math.max(r/a,o/n),l=a*d,h=n*d,u=Math.max(0,Math.min(l-r,l*(c/100)-r/2)),g=Math.max(0,Math.min(h-o,h*(s/100)-o/2));return`${u&&Math.floor(u/(l-r)*100)}% ${g&&Math.floor(g/(h-o)*100)}%`}const tt={width:"100%",height:"100%"};function it(e,t,i,a={}){const{autoEncode:n=!0,isSEOBot:r,shouldLoadHQImage:o,hasAnimation:c,allowAnimatedTransform:s,encoding:d}=a;if(!Z(e,t,i))return x;const l=void 0===s||s,h=q(t.id,c,l);if(!h||o)return at(e,t,i,{...a,autoEncode:n,useSrcset:h});const u={...i,...Ke(t,i)},{alignment:g,htmlTag:m}=u,_=Xe(e,t,u),p=function(e,t,{width:i,height:a},n=!1){if(n)return{width:i,height:a};const r=!Je.includes(e),o=Xe(e,t,{width:i,height:a}),c=!o&&qe.includes(e),s=c?t.width:i,d=c?t.height:a,l=r?function(e,t){return e>900?t?.05:.15:e>500?t?.1:.18:e>200?.25:1}(s,J(t.id)):1;return{width:o?1920:s*l,height:d*l}}(e,t,u,r),f=function(e,t,i){return i?0:qe.includes(t)?1:e>200?2:3}(u.width,e,r),T=function(e,t){const i=qe.includes(e)&&!t;return e===y.SCALE_TO_FILL||i?y.SCALE_TO_FIT:e}(e,_),I=Qe(e,t,i,g),{uri:E}=at(T,t,{...p,alignment:g,htmlTag:m},{autoEncode:n,filters:f?{blur:f}:{},hasAnimation:c,allowAnimatedTransform:l,encoding:d}),{attr:L={},css:A}=at(e,t,{...u,alignment:g,htmlTag:m},{});return A.img=A.img||{},A.container=A.container||{},Object.assign(A.img,I.img,tt),Object.assign(A.container,I.container),{uri:E,css:A,attr:L,transformed:!0}}function at(e,t,i,a){let n={};if(Z(e,t,i)){const r=Re(e,t,i),o=ye(e,t,r,a);n.uri=De(e,t,r,a,o),a?.useSrcset&&(n.srcset=function(e,t,i,a,n){const r=i.pixelAspectRatio||1;return{dpr:[`${1===r?n.uri:De(e,t,{...i,pixelAspectRatio:1},a)} 1x`,`${2===r?n.uri:De(e,t,{...i,pixelAspectRatio:2},a)} 2x`]}}(e,t,r,a,n)),Object.assign(n,function(e,t){let i;return i=t.htmlTag===N.BG?fe:t.htmlTag===N.SVG?Le:Ee,i(e,t)}(o,r),{transformed:o.transformed})}else n=x;return n}const nt="https://static.wixstatic.com/media/";"undefined"!=typeof window&&window.devicePixelRatio;pe();pe();const rt=nt,{STATIC_MEDIA_URL:ot}=e,ct=({fittingType:e,src:t,target:i,options:a})=>{const n=it(e,t,i,{...a,autoEncode:!0});return n?.uri&&!/^[a-z]+:/.test(n.uri)&&(n.uri=`${ot}${n.uri}`),n},st=/^[a-z]+:/,dt=e=>{const{id:t,containerId:i,uri:a,alt:n,name:o="",role:c,width:s,height:d,displayMode:l,devicePixelRatio:h,quality:u,alignType:g,bgEffectName:m="",focalPoint:_,upscaleMethod:p,className:f="",crop:T,imageStyles:I={},targetWidth:L,targetHeight:A,targetScale:b,onLoad:w=()=>{},onError:O=()=>{},shouldUseLQIP:M,containerWidth:y,containerHeight:R,getPlaceholder:S,isInFirstFold:G,placeholderTransition:C,socialAttrs:N,isSEOBot:F,skipMeasure:v,hasAnimation:P,encoding:x}=e,k=r.useRef(null);let H="";const Y="blur"===C,B=r.useRef(null);if(!B.current)if(S||M||G||F){const e={upscaleMethod:p,...u||{},shouldLoadHQImage:G,isSEOBot:F,hasAnimation:P,encoding:x};B.current=(S||ct)({fittingType:l,src:{id:a,width:s,height:d,crop:T,name:o,focalPoint:_},target:{width:y,height:R,alignment:g,htmlTag:"img"},options:e}),H=!B.current.transformed||G||F?"":"true"}else B.current={uri:void 0,css:{img:{}},attr:{img:{},container:{}},transformed:!1};const U=!F&&(S||M)&&!G&&B.current.transformed,$=r.useMemo((()=>JSON.stringify({containerId:i,...i&&{containerId:i},...g&&{alignType:g},...v&&{skipMeasure:!0},displayMode:l,...y&&{targetWidth:y},...R&&{targetHeight:R},...L&&{targetWidth:L},...A&&{targetHeight:A},...b&&{targetScale:b},isLQIP:U,isSEOBot:F,lqipTransition:C,encoding:x,imageData:{width:s,height:d,uri:a,name:o,displayMode:l,hasAnimation:P,...u&&{quality:u},...h&&{devicePixelRatio:h},..._&&{focalPoint:_},...T&&{crop:T},...p&&{upscaleMethod:p}}})),[i,g,v,l,y,R,L,A,b,U,F,C,x,s,d,a,o,P,u,h,_,T,p]),j=B.current,z=j?.uri,V=j?.srcset,D=j.css?.img,W=`${E} ${f}`;r.useEffect((()=>{const e=k.current;w&&e?.currentSrc&&e?.complete&&w({target:e})}),[]);const Z=j&&!j?.transformed?`max(${s}px, 100%)`:L?`${L}px`:null;return r.createElement("wow-image",{id:t,class:W,"data-image-info":$,"data-motion-part":`BG_IMG ${i}`,"data-bg-effect-name":m,"data-has-ssr-src":H,"data-animate-blur":!F&&U&&Y?"":void 0,style:Z?{"--wix-img-max-width":Z}:{}},r.createElement("img",{src:z,ref:k,alt:n||"",role:c,style:{...D,...I},onLoad:w,onError:O,width:y||void 0,height:R||void 0,...N,srcSet:G?V?.dpr?.map((e=>st.test(e)?e:`${ot}${e}`)).join(", "):void 0,fetchpriority:G?"high":void 0,loading:!1===G?"lazy":void 0,suppressHydrationWarning:!0}))};var lt="Tj01hh";var ht=e=>{var t,a;const{id:n,alt:o,role:c,className:d,imageStyles:l={},targetWidth:h,targetHeight:u,onLoad:g,onError:m,containerWidth:_,containerHeight:p,isInFirstFold:f,socialAttrs:T,skipMeasure:I,responsiveImageProps:E,zoomedImageResponsiveOverride:L,displayMode:A}=e,b=h||_,w=u||p,{fallbackSrc:O,srcset:M,sources:y,css:R}=E||{},{width:S,height:G,...C}=(null==E||null==(t=E.css)?void 0:t.img)||{},N="original_size"===A?null==E||null==(a=E.css)?void 0:a.img:C;var F;return O&&M&&R?r.createElement("img",i()({fetchpriority:f?"high":void 0,loading:!1===f?"lazy":void 0,sizes:b+"px",srcSet:I?null==L?void 0:L.srcset:null==E?void 0:E.srcset,id:n,src:O,alt:o||"",role:c,style:{...l,...I?{...null==L||null==(F=L.css)?void 0:F.img}:{...N}},onLoad:g,onError:m,className:s(d,lt),width:b,height:w},T)):O&&y&&R?r.createElement("picture",null,y.map((e=>{let{srcset:t,media:i,sizes:a}=e;return r.createElement("source",{key:i,srcSet:t,media:i,sizes:a})})),r.createElement("img",i()({fetchpriority:f?"high":void 0,loading:!1===f?"lazy":void 0,id:n,src:y[0].fallbackSrc,alt:o||"",role:c,style:{...l,objectFit:y[0].imgStyle.objectFit,objectPosition:y[0].imgStyle.objectPosition},onLoad:g,onError:m,className:s(d,lt),width:b,height:w},T))):r.createElement(dt,e)};var ut=e=>{var t,i,a;const{className:n,customIdPrefix:o,getPlaceholder:c,hasAnimation:s,...d}=e,l=r.useMemo((()=>JSON.stringify({containerId:d.containerId,alignType:d.alignType,fittingType:d.displayMode,hasAnimation:s,imageData:{width:d.width,height:d.height,uri:d.uri,name:d.name,...d.quality&&{quality:d.quality},displayMode:d.displayMode}})),[d,s]),h=r.useRef(null);h.current||(h.current=c?c({fittingType:d.displayMode,src:{id:d.uri,width:d.width,height:d.height,name:d.name},target:{width:d.containerWidth,height:d.containerHeight,alignment:d.alignType,htmlTag:"bg"},options:{hasAnimation:s,allowAnimatedTransform:!1}}):{uri:void 0,css:{img:{}},attr:{img:{},container:{}}});const u=h.current,g=null!=(t=null==u?void 0:u.uri)?t:"",m=null!=(i=null==(a=u.css)?void 0:a.container)?i:{},_=Object.assign(g?{backgroundImage:"url("+g+")"}:{},m);return r.createElement("wix-bg-image",{id:""+(o||"bgImg_")+d.containerId,class:n,style:_,"data-tiled-image-info":l,"data-has-bg-scroll-effect":d.hasBgScrollEffect||"","data-bg-effect-name":d.bgEffectName||"","data-motion-part":"BG_IMG "+d.containerId})};const gt=new RegExp("<%= compId %>","g"),mt=(e,t)=>e.replace(gt,t);var _t=e=>null==e?void 0:e.replace(":hover",""),pt="bX9O_S",ft="Z_wCwr",Tt="Jxk_UL",It="K8MSra",Et="YTb3b4";const Lt={quality:{unsharpMask:{radius:.33,amount:1,threshold:0}},devicePixelRatio:1};var At=e=>{const{id:t,videoRef:a,videoInfo:n,posterImageInfo:o,muted:c,preload:d,loop:l,alt:h,isVideoEnabled:u,getPlaceholder:g,extraClassName:m=""}=e;n.containerId=_t(n.containerId);const _=r.useMemo((()=>JSON.stringify(n)),[n]),p=r.createElement(r.Fragment,null,o.filterEffectSvgString&&r.createElement("svg",{id:"svg_"+n.containerId,className:Et},r.createElement("defs",{dangerouslySetInnerHTML:{__html:mt(o.filterEffectSvgString,n.containerId)}})),r.createElement(ht,i()({key:n.videoId+"_img",id:o.containerId+"_img",className:s(ft,Tt,"bgVideoposter",m),imageStyles:{width:"100%",height:"100%"}},o,Lt,{getPlaceholder:g})));return u?r.createElement("wix-video",{id:t,"data-video-info":_,"data-motion-part":"BG_IMG "+n.containerId,class:s(pt,"bgVideo",m)},r.createElement("video",{key:n.videoId+"_video",ref:a,id:n.containerId+"_video",className:It,crossOrigin:"anonymous","aria-label":h,playsInline:!0,preload:d,muted:c,loop:l}),p):p},bt="SUz0WK";var wt=e=>{const{id:t,containerId:i,pageId:a,children:n,bgEffectName:o="",containerSize:c}=e;return r.createElement("wix-bg-media",{id:t,class:bt,"data-container-id":i,"data-container-size":((null==c?void 0:c.width)||0)+", "+((null==c?void 0:c.height)||0),"data-page-id":a,"data-bg-effect-name":o,"data-motion-part":"BG_MEDIA "+i},n)};const Ot="bgOverlay";var Mt="m4khSP",yt="FNxOn5";var Rt=e=>{const{imageOverlay:t}=e;return r.createElement("div",{"data-testid":Ot,className:Mt},t&&r.createElement(ut,i()({customIdPrefix:"bgImgOverlay_",className:yt},t)))};const St="bgLayers",Gt="colorUnderlay",Ct="mediaPadding",Nt="canvas";var Ft="MW5IWV",vt="N3eg0s",Pt="Kv1aVt",xt="dLPlxY",kt="VgO9Yg",Ht="LWbAav",Yt="yK6aSC",Bt="K_YxMd",Ut="NGjcJN",$t="mNGsUM",jt="I8xA4L";const zt="bgImage";var Vt=e=>{const{videoRef:t,canvasRef:a,hasBgFullscreenScrollEffect:n,image:o,backgroundImage:c,backgroundMedia:d,video:l,backgroundOverlay:h,shouldPadMedia:u,extraClass:g="",shouldRenderUnderlay:m=!l,reducedMotion:_=!1,getPlaceholder:p,hasCanvasAnimation:f,useWixMediaCanvas:T,onClick:I}=e,{onImageLoad:E}=(e=>{let{onReady:t,image:i}=e;return(0,r.useEffect)((()=>{t&&!i&&t()}),[t,i]),{onImageLoad:e=>{null!=i&&i.onLoad&&i.onLoad(e),t&&t()}}})(e),L=_t(e.containerId),A="img_"+_t(L),b=o&&r.createElement(ht,i()({id:A,className:s(Pt,xt,$t,zt),imageStyles:{width:"100%",height:"100%"},getPlaceholder:p},o,{onLoad:E})),w=c&&r.createElement(ut,i()({},c,{containerId:L,className:s(Pt,xt,$t,zt),getPlaceholder:p})),O=l&&r.createElement(At,i()({id:"videoContainer_"+L},l,{extraClassName:Yt,reducedMotion:_,videoRef:t,getPlaceholder:p})),M=T&&a||f?r.createElement("wix-media-canvas",{"data-container-id":L,class:f?jt:""},b,w,O,r.createElement("canvas",{id:L+"webglcanvas",className:s(Bt,"webglcanvas"),"aria-label":(null==l?void 0:l.alt)||"",role:"presentation","data-testid":Nt})):r.createElement(r.Fragment,null,b,w,O,a&&r.createElement("canvas",{id:L+"webglcanvas",ref:a,className:s(Bt,"webglcanvas"),"aria-label":(null==l?void 0:l.alt)||"",role:"presentation","data-testid":Nt})),y=d?r.createElement(wt,i()({id:"bgMedia_"+L},d),M):r.createElement("div",{id:"bgMedia_"+L,"data-motion-part":"BG_MEDIA "+L,className:kt},M),R=h&&r.createElement(Rt,h);return r.createElement("div",{id:St+"_"+L,"data-hook":St,"data-motion-part":"BG_LAYER "+L,className:s(Ft,g,{[vt]:n}),onClick:I},m&&r.createElement("div",{"data-testid":Gt,className:s(Ht,Pt)}),u?r.createElement("div",{"data-testid":Ct,className:Ut},y,R):r.createElement(r.Fragment,null,y,R))},Dt="imK94d",Wt="eF_jBL",Zt="wfm0FO";const qt=(e,t)=>{const{id:a,className:n,fillLayers:r,children:c,skin:d,meshProps:l,onMouseEnter:h,onMouseLeave:_,getPlaceholder:p,onStop:f,a11y:T}=e,E=(({role:e,tabIndex:t,tabindex:i,...a}={})=>{const n=Object.entries(a).reduce(((e,[t,i])=>({...e,[`aria-${t}`.toLowerCase()]:i})),{role:e,tabIndex:t??i});return Object.keys(n).forEach((e=>{void 0!==n[e]&&null!==n[e]||delete n[e]})),g(n,u)})({...T}),L=o().createElement(I,i()({id:a},l),c),A=function(e,t,i){const a=o().useRef(null),n=o().useRef(null);return t?n.current||(n.current={play:()=>a.current?.play(),load:()=>a.current?.load(),pause:()=>a.current?.pause(),stop:()=>{a.current&&(a.current.pause(),a.current.currentTime=0,i&&i(a.current))}}):n.current=null,o().useImperativeHandle(e,(()=>n.current||{load(){},stop(){}})),a}(t,!!r.video,f);return o().createElement("div",i()({id:a},m(e),{className:s(n,Dt),onMouseEnter:h,onMouseLeave:_},E),o().createElement(Vt,i()({},r,{extraClass:Wt,getPlaceholder:p,videoRef:A})),o().createElement("div",{className:Zt}),"StateBoxStateFormSkin"===d?o().createElement("form",{onSubmit:e=>{e.preventDefault()}},L):L)};const Jt={SlideShowSlide:{component:o().forwardRef(qt)}}}(),n}()}));
//# sourceMappingURL=https://static.parastorage.com/services/editor-elements-library/dist/thunderbolt/rb_wixui.thunderbolt[SlideShowSlide].869b3cda.bundle.min.js.map