google.maps.__gjsload__('infowindow', function(_){var pM=function(a){return!!a.infoWindow.get("logAsInternal")},eRa=function(a,b){if(a.Eg.size===1){const c=Array.from(a.Eg.values())[0];c.uv!==b.uv&&(c.set("map",null),a.Eg.delete(c))}a.Eg.add(b)},gRa=function(a,b){var c=a.__gm;a=c.get("panes");c=c.get("innerContainer");b={vl:a,Fj:_.lA.Fj(),px:c,shouldFocus:b};return new fRa(b)},qM=function(a,b){a.container.style.visibility=b?"":"hidden";b&&a.shouldFocus&&(a.focus(),a.shouldFocus=!1);b?hRa(a):a.Pg=!1},iRa=function(a){a.rj.setAttribute("aria-labelledby",
a.Kg.id)},jRa=function(a){const b=!!a.get("open");var c=a.get("content");c=b?c:null;if(c==a.Ig)qM(a,b&&a.get("position"));else{if(a.Ig){const d=a.Ig.parentNode;d==a.Eg&&d.removeChild(a.Ig)}c&&(a.Og=!1,a.Eg.appendChild(c));qM(a,b&&a.get("position"));a.Ig=c;rM(a)}},sM=function(a){var b=!!a.get("open"),c=a.get("headerContent");const d=!!a.get("ariaLabel"),e=!a.get("headerDisabled");b=b?c:null;a.rj.style.paddingTop=e?"0":"12px";b===a.Jg?a.Gg.style.display=e?"":"none":(a.Jg&&(c=a.Jg.parentNode,c===a.Kg&&
c.removeChild(a.Jg)),b&&(a.Og=!1,a.Kg.appendChild(b),e&&!d&&iRa(a)),a.Gg.style.display=e?"":"none",a.Jg=b,rM(a))},rM=function(a){var b=a.getSize();if(b){var c=b.km;b=b.minWidth;a.rj.style.maxWidth=_.gj(c.width);a.rj.style.maxHeight=_.gj(c.height);a.rj.style.minWidth=_.gj(b);a.Eg.style.maxHeight=_.Mm.Eg?_.gj(c.height-18):_.gj(c.height-36);tM(a);a.Ng.start()}},kRa=function(a){const b=a.get("pixelOffset")||new _.al(0,0);var c=new _.al(a.rj.offsetWidth,a.rj.offsetHeight);a=-b.height+c.height+11+60;let d=
b.height+60;const e=-b.width+c.width/2+60;c=b.width+c.width/2+60;b.height<0&&(d-=b.height);return{top:a,bottom:d,left:e,right:c}},hRa=function(a){!a.Pg&&a.get("open")&&a.get("visible")&&a.get("position")&&(_.hk(a,"visible"),a.Pg=!0)},tM=function(a){var b=a.get("position");if(b&&a.get("pixelOffset")){var c=kRa(a);const d=b.x-c.left,e=b.y-c.top,f=b.x+c.right;c=b.y+c.bottom;_.mu(a.anchor,b);b=a.get("zIndex");_.ou(a.container,_.$i(b)?b:e+60);a.set("pixelBounds",_.Zl(d,e,f,c))}},mRa=function(a,b,c){return b instanceof
_.Ck?new lRa(a,b,c):new lRa(a,b)},oRa=function(a){a.Eg&&a.ui.push(_.fk(a.Eg,"pixelposition_changed",()=>{nRa(a)}))},nRa=function(a){const b=a.model.get("pixelPosition")||a.Eg&&a.Eg.get("pixelPosition");a.Ig.set("position",b)},qRa=function(a){a=a.__gm;a.get("IW_AUTO_CLOSER")||a.set("IW_AUTO_CLOSER",new pRa);return a.get("IW_AUTO_CLOSER")},pRa=class{constructor(){this.Eg=new Set}};var fRa=class extends _.kk{constructor(a){super();this.Ig=this.Jg=this.Mg=null;this.Pg=this.Og=!1;this.px=a.px;this.shouldFocus=a.shouldFocus;this.container=document.createElement("div");this.container.style.cursor="default";this.container.style.position="absolute";this.container.style.left=this.container.style.top="0";a.vl.floatPane.appendChild(this.container);this.anchor=_.nu("div",this.container);this.Lg=_.nu("div",this.anchor);this.rj=_.nu("div",this.Lg);this.rj.setAttribute("role","dialog");
this.rj.tabIndex=-1;this.Gg=_.nu("div",this.rj);this.Kg=_.nu("div",this.Gg);this.Sg=_.nu("div",this.Lg);this.Eg=_.nu("div",this.rj);_.dHa(this.container);_.hu(this.rj,"gm-style-iw");_.hu(this.anchor,"gm-style-iw-a");_.hu(this.Lg,"gm-style-iw-t");_.hu(this.Sg,"gm-style-iw-tc");_.hu(this.rj,"gm-style-iw-c");_.hu(this.Gg,"gm-style-iw-chr");_.hu(this.Kg,"gm-style-iw-ch");_.hu(this.Eg,"gm-style-iw-d");this.Kg.setAttribute("id",_.Bk());_.Mm.Eg&&!_.Mm.Mg&&(this.rj.style.paddingInlineEnd="0",this.rj.style.paddingBottom=
"0",this.Eg.style.overflow="scroll");qM(this,!1);_.bk(this.container,"mousedown",_.Tj);_.bk(this.container,"mouseup",_.Tj);_.bk(this.container,"mousemove",_.Tj);_.bk(this.container,"pointerdown",_.Tj);_.bk(this.container,"pointerup",_.Tj);_.bk(this.container,"pointermove",_.Tj);_.bk(this.container,"dblclick",_.Tj);_.bk(this.container,"click",_.Tj);_.bk(this.container,"touchstart",_.Tj);_.bk(this.container,"touchend",_.Tj);_.bk(this.container,"touchmove",_.Tj);_.Xt(this.container,"contextmenu",this,
this.Rg);_.Xt(this.container,"wheel",this,_.Tj);_.Xt(this.container,"mousewheel",this,_.Qj);_.Xt(this.container,"MozMousePixelScroll",this,_.Qj);this.Fg=new _.Io({xq:new _.Zk(12,12),Pr:new _.al(24,24),offset:new _.Zk(-6,-6),QB:!0,ownerElement:this.Gg});this.Gg.appendChild(this.Fg.element);_.bk(this.Fg.element,"click",b=>{_.Tj(b);_.hk(this,"closeclick");this.set("open",!1)});this.Ng=new _.xm(()=>{!this.Og&&this.get("content")&&this.get("visible")&&(_.hk(this,"domready"),this.Og=!0)},0);this.Qg=_.bk(this.container,
"keydown",b=>{b.key!=="Escape"&&b.key!=="Esc"||!this.rj.contains(document.activeElement)||(b.stopPropagation(),_.hk(this,"closeclick"),this.set("open",!1))})}ariaLabel_changed(){const a=this.get("ariaLabel");a?this.rj.setAttribute("aria-label",a):(this.rj.removeAttribute("aria-label"),this.get("headerDisabled")||iRa(this))}open_changed(){jRa(this);sM(this)}headerContent_changed(){sM(this)}headerDisabled_changed(){sM(this)}content_changed(){jRa(this)}pendingFocus_changed(){this.get("pendingFocus")&&
(this.get("open")&&this.get("visible")&&this.get("position")?_.Tm(this.rj,!0):console.warn("Setting focus on InfoWindow was ignored. This is most likely due to InfoWindow not being visible yet."),this.set("pendingFocus",!1))}dispose(){setTimeout(()=>{document.activeElement&&document.activeElement!==document.body||(this.Mg&&this.Mg!==document.body?_.Tm(this.Mg,!0)||_.Tm(this.px,!0):_.Tm(this.px,!0))});this.Qg&&_.Xj(this.Qg);this.container.parentNode.removeChild(this.container);this.Ng.stop();this.Ng.dispose()}getSize(){var a=
this.get("layoutPixelBounds"),b=this.get("pixelOffset");const c=this.get("maxWidth")||648,d=this.get("minWidth")||0;if(!b)return null;a?(b=a.maxY-a.minY-(11+-b.height),a=a.maxX-a.minX-6,a>=240&&(a-=120),b>=240&&(b-=120)):(a=648,b=654);a=Math.min(a,c);a=Math.max(d,a);a=Math.max(0,a);b=Math.max(0,b);return{km:new _.al(a,b),minWidth:d}}pixelOffset_changed(){const a=this.get("pixelOffset")||new _.al(0,0);this.Lg.style.right=_.gj(-a.width);this.Lg.style.bottom=_.gj(-a.height+11);rM(this)}layoutPixelBounds_changed(){rM(this)}position_changed(){this.get("position")?
(tM(this),qM(this,!!this.get("open"))):qM(this,!1)}zIndex_changed(){tM(this)}visible_changed(){this.container.style.display=this.get("visible")?"":"none";this.Ng.start();if(this.get("visible")){const a=this.Fg.element.style.display;this.Fg.element.style.display="none";this.Fg.element.getBoundingClientRect();this.Fg.element.style.display=a;hRa(this)}else this.Pg=!1}Rg(a){let b=!1;const c=this.get("content");let d=a.target;for(;!b&&d;)b=d==c,d=d.parentNode;b?_.Qj(a):_.Sj(a)}focus(){this.Mg=document.activeElement;
let a;_.Mm.Ng&&(a=this.Eg.getBoundingClientRect());if(this.get("disableAutoPan"))_.Tm(this.rj,!0);else{var b=_.su(this.Eg);if(b.length){b=b[0];a=a||this.Eg.getBoundingClientRect();var c=b.getBoundingClientRect();_.Tm(c.bottom<=a.bottom&&c.right<=a.right?b:this.rj,!0)}else _.Tm(this.Fg.element,!0)}}};var lRa=class{constructor(a,b,c){this.model=a;this.isOpen=!0;this.Eg=this.Gg=this.ah=null;this.ui=[];var d=a.get("shouldFocus");this.Ig=gRa(b,d);const e=b.__gm;(d=b instanceof _.Ck)&&c?c.then(h=>{this.isOpen&&(this.ah=h,this.Eg=new _.IJ(l=>{this.Gg=new _.Lz(b,h,l,()=>{});h.Li(this.Gg);return this.Gg}),this.Eg.bindTo("latLngPosition",a,"position"),oRa(this))}):(this.Eg=new _.IJ,this.Eg.bindTo("latLngPosition",a,"position"),this.Eg.bindTo("center",e,"projectionCenterQ"),this.Eg.bindTo("zoom",e),this.Eg.bindTo("offset",
e),this.Eg.bindTo("projection",b),this.Eg.bindTo("focus",b,"position"),oRa(this));this.Jg=d?pM(a)?"Ia":"Id":null;this.Kg=d?pM(a)?148284:148285:null;const f=new _.tJ(["scale"],"visible",h=>h==null||h>=.3);this.Eg&&f.bindTo("scale",this.Eg);const g=this.Ig;g.set("logAsInternal",pM(a));g.bindTo("ariaLabel",a);g.bindTo("zIndex",a);g.bindTo("layoutPixelBounds",e,"pixelBounds");g.bindTo("disableAutoPan",a);g.bindTo("pendingFocus",a);g.bindTo("maxWidth",a);g.bindTo("minWidth",a);g.bindTo("content",a);g.bindTo("headerContent",
a);g.bindTo("headerDisabled",a);g.bindTo("pixelOffset",a);g.bindTo("visible",f);this.Fg=new _.xm(()=>{if(b instanceof _.Ck)if(this.ah){var h=a.get("position");h&&(0,_.spa.mF)(b,this.ah,new _.Jk(h),kRa(g))}else c.then(()=>{this.Fg.start()});else(h=g.get("pixelBounds"))?_.hk(e,"pantobounds",h):this.Fg.start()},150);if(d){let h=null;this.ui.push(_.fk(a,"position_changed",()=>{const l=a.get("position");!l||a.get("disableAutoPan")||l.equals(h)||(this.Fg.start(),h=l)}))}else a.get("disableAutoPan")||this.Fg.start();
g.set("open",!0);this.ui.push(_.Vj(g,"domready",()=>{a.trigger("domready")}));this.ui.push(_.Vj(g,"visible",()=>{a.trigger("visible")}));this.ui.push(_.Vj(g,"closeclick",()=>{a.close();a.trigger("closeclick")}));this.ui.push(_.fk(a,"pixelposition_changed",()=>{nRa(this)}));this.Jg&&_.Sk(b,this.Jg);this.Kg&&_.Q(b,this.Kg)}close(){if(this.isOpen){this.isOpen=!1;this.model.trigger("close");for(var a of this.ui)_.Xj(a);this.ui.length=0;this.Fg.stop();this.Fg.dispose();this.ah&&this.Gg&&this.ah.xl(this.Gg);
a=this.Ig;a.unbindAll();a.set("open",!1);a.dispose();this.Eg&&this.Eg.unbindAll()}}};_.Ki("infowindow",{uH:function(a){let b=null;_.fk(a,"map_changed",function d(){const e=a.get("map");b&&(b.hD.Eg.delete(a),b.xL.close(),b=null);if(e){const f=e.__gm;f.get("panes")?f.get("innerContainer")?(b={xL:mRa(a,e,e instanceof _.Ck?f.Fg.then(({ah:g})=>g):void 0),hD:qRa(e)},eRa(b.hD,a)):_.ek(f,"innercontainer_changed",d):_.ek(f,"panes_changed",d)}})}});});
