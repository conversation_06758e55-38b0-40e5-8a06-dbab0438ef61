
// Copyright 2012 Google Inc. All rights reserved.
 
(function(){

var data = {
"resource": {
  "version":"1",
  
  "macros":[{"function":"__e"}],
  "tags":[{"function":"__ogt_1p_data_v2","priority":2,"vtp_isAutoEnabled":true,"vtp_autoCollectExclusionSelectors":["list",["map","exclusionSelector",""]],"vtp_isEnabled":true,"vtp_autoEmailEnabled":true,"vtp_autoPhoneEnabled":false,"vtp_autoAddressEnabled":false,"vtp_isAutoCollectPiiEnabledFlag":false,"tag_id":6},{"function":"__ccd_ga_first","priority":1,"vtp_instanceDestinationId":"UA-180993812-1","tag_id":9},{"function":"__rep","vtp_containerId":"UA-180993812-1","vtp_remoteConfig":["map"],"tag_id":1},{"function":"__zone","vtp_childContainers":["list",["map","publicId","G-CRVK8RDV60"]],"vtp_inheritParentConfig":true,"vtp_enableConfiguration":false,"tag_id":3},{"function":"__ccd_ga_last","priority":0,"vtp_instanceDestinationId":"UA-180993812-1","tag_id":8}],
  "predicates":[{"function":"_eq","arg0":["macro",0],"arg1":"gtm.js"},{"function":"_eq","arg0":["macro",0],"arg1":"gtm.init"}],
  "rules":[[["if",0],["add",2,3]],[["if",1],["add",0,4,1]]]
},
"runtime":[ [50,"__ccd_ga_first",[46,"a"],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_ga_last",[46,"a"],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__e",[46,"a"],[36,[13,[41,"$0"],[3,"$0",["require","internal.getEventData"]],["$0","event"]]]]
 ,[50,"__ogt_1p_data_v2",[46,"a"],[50,"q",[46,"v","w"],[52,"x",[7]],[52,"y",[2,[15,"b"],"keys",[7,[15,"v"]]]],[65,"z",[15,"y"],[46,[53,[52,"aA",[30,[16,[15,"v"],[15,"z"]],[7]]],[52,"aB",[39,[18,[17,[15,"aA"],"length"],0],"1","0"]],[52,"aC",[39,["r",[15,"w"],[15,"z"]],"1","0"]],[2,[15,"x"],"push",[7,[0,[0,[0,[16,[15,"p"],[15,"z"]],"-"],[15,"aB"]],[15,"aC"]]]]]]],[36,[2,[15,"x"],"join",[7,"~"]]]],[50,"r",[46,"v","w"],[22,[28,[15,"v"]],[46,[53,[36,false]]]],[38,[15,"w"],[46,"email","phone_number","first_name","last_name","street","city","region","postal_code","country"],[46,[5,[46,[36,[28,[28,[16,[15,"v"],"email"]]]]]],[5,[46,[36,[28,[28,[16,[15,"v"],"phone_number"]]]]]],[5,[46]],[5,[46]],[5,[46]],[5,[46]],[5,[46]],[5,[46]],[5,[46,[36,["s",[15,"v"],[15,"w"]]]]],[9,[46,[36,false]]]]]],[50,"s",[46,"v","w"],[36,[1,[28,[28,[16,[15,"v"],"address"]]],[28,[28,[16,[16,[15,"v"],"address"],[15,"w"]]]]]]],[50,"t",[46,"v","w","x","y"],[22,[20,[16,[15,"w"],"type"],[15,"x"]],[46,[53,[22,[28,[15,"v"]],[46,[53,[3,"v",[8]]]]],[22,[28,[16,[15,"v"],[15,"x"]]],[46,[53,[43,[15,"v"],[15,"x"],[16,[15,"w"],"userData"]],[52,"z",[8,"mode","a"]],[22,[16,[15,"w"],"tagName"],[46,[53,[43,[15,"z"],"location",[16,[15,"w"],"tagName"]]]]],[22,[16,[15,"w"],"querySelector"],[46,[53,[43,[15,"z"],"selector",[16,[15,"w"],"querySelector"]]]]],[43,[15,"y"],[15,"x"],[15,"z"]]]]]]]],[36,[15,"v"]]],[50,"u",[46,"v","w","x"],[22,[28,[16,[15,"a"],[15,"x"]]],[46,[36]]],[43,[15,"v"],[15,"w"],[8,"value",[16,[15,"a"],[15,"x"]]]]],[22,[28,[17,[15,"a"],"isEnabled"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"b",["require","Object"]],[52,"c",["require","internal.isFeatureEnabled"]],[52,"d",[15,"__module_featureFlags"]],[52,"e",["require","internal.getDestinationIds"]],[52,"f",["require","internal.getProductSettingsParameter"]],[52,"g",["require","internal.detectUserProvidedData"]],[52,"h",["require","queryPermission"]],[52,"i",["require","internal.setRemoteConfigParameter"]],[52,"j",["require","internal.registerCcdCallback"]],[52,"k",[15,"__module_metadataSchema"]],[52,"l","_z"],[52,"m",["c",[17,[15,"d"],"DZ"]]],[52,"n",[30,["e"],[7]]],[52,"o",[8,"enable_code",true]],[52,"p",[8,"email","1","phone_number","2","first_name","3","last_name","4","country","5","postal_code","6","street","7","city","8","region","9"]],[22,[17,[15,"a"],"isAutoEnabled"],[46,[53,[52,"v",[7]],[22,[1,[17,[15,"a"],"autoCollectExclusionSelectors"],[17,[17,[15,"a"],"autoCollectExclusionSelectors"],"length"]],[46,[53,[53,[41,"y"],[3,"y",0],[63,[7,"y"],[23,[15,"y"],[17,[17,[15,"a"],"autoCollectExclusionSelectors"],"length"]],[33,[15,"y"],[3,"y",[0,[15,"y"],1]]],[46,[53,[52,"z",[17,[16,[17,[15,"a"],"autoCollectExclusionSelectors"],[15,"y"]],"exclusionSelector"]],[22,[15,"z"],[46,[53,[2,[15,"v"],"push",[7,[15,"z"]]]]]]]]]]]]],[52,"w",[30,["c",[17,[15,"d"],"Z"]],[17,[15,"a"],"isAutoCollectPiiEnabledFlag"]]],[52,"x",[39,[17,[15,"a"],"isAutoCollectPiiEnabledFlag"],[17,[15,"a"],"autoEmailEnabled"],true]],[43,[15,"o"],"auto_detect",[8,"email",[15,"x"],"phone",[1,[15,"w"],[17,[15,"a"],"autoPhoneEnabled"]],"address",[1,[15,"w"],[17,[15,"a"],"autoAddressEnabled"]],"exclude_element_selectors",[15,"v"]]]]]],[22,[17,[15,"a"],"isManualEnabled"],[46,[53,[52,"v",[8]],[22,[17,[15,"a"],"manualEmailEnabled"],[46,[53,["u",[15,"v"],"email","emailValue"]]]],[22,[17,[15,"a"],"manualPhoneEnabled"],[46,[53,["u",[15,"v"],"phone","phoneValue"]]]],[22,[17,[15,"a"],"manualAddressEnabled"],[46,[53,[52,"w",[8]],["u",[15,"w"],"first_name","firstNameValue"],["u",[15,"w"],"last_name","lastNameValue"],["u",[15,"w"],"street","streetValue"],["u",[15,"w"],"city","cityValue"],["u",[15,"w"],"region","regionValue"],["u",[15,"w"],"country","countryValue"],["u",[15,"w"],"postal_code","postalCodeValue"],[43,[15,"v"],"name_and_address",[7,[15,"w"]]]]]],[43,[15,"o"],"selectors",[15,"v"]]]]],[65,"v",[15,"n"],[46,[53,["i",[15,"v"],"user_data_settings",[15,"o"]],[52,"w",[16,[15,"o"],"auto_detect"]],[22,[28,[15,"w"]],[46,[53,[6]]]],[52,"x",[51,"",[7,"y"],[52,"z",[2,[15,"y"],"getMetadata",[7,[17,[15,"k"],"AL"]]]],[22,[15,"z"],[46,[53,[36,[15,"z"]]]]],[52,"aA",[1,["c",[17,[15,"d"],"CN"]],[20,[2,[15,"v"],"indexOf",[7,"G-"]],0]]],[41,"aB"],[22,["h","detect_user_provided_data","auto"],[46,[53,[3,"aB",["g",[8,"excludeElementSelectors",[16,[15,"w"],"exclude_element_selectors"],"fieldFilters",[8,"email",[16,[15,"w"],"email"],"phone",[16,[15,"w"],"phone"],"address",[16,[15,"w"],"address"]],"performDataLayerSearch",[15,"aA"]]]]]]],[52,"aC",[1,[15,"aB"],[16,[15,"aB"],"elements"]]],[52,"aD",[8]],[52,"aE",[8]],[22,[1,[15,"aC"],[18,[17,[15,"aC"],"length"],0]],[46,[53,[41,"aF"],[41,"aG"],[3,"aG",[8]],[53,[41,"aH"],[3,"aH",0],[63,[7,"aH"],[23,[15,"aH"],[17,[15,"aC"],"length"]],[33,[15,"aH"],[3,"aH",[0,[15,"aH"],1]]],[46,[53,[52,"aI",[16,[15,"aC"],[15,"aH"]]],["t",[15,"aD"],[15,"aI"],"email",[15,"aE"]],[22,["c",[17,[15,"d"],"AA"]],[46,[53,["t",[15,"aD"],[15,"aI"],"phone_number",[15,"aE"]],[3,"aF",["t",[15,"aF"],[15,"aI"],"first_name",[15,"aG"]]],[3,"aF",["t",[15,"aF"],[15,"aI"],"last_name",[15,"aG"]]],[3,"aF",["t",[15,"aF"],[15,"aI"],"country",[15,"aG"]]],[3,"aF",["t",[15,"aF"],[15,"aI"],"postal_code",[15,"aG"]]]]]]]]]],[22,[1,[15,"aF"],[28,[16,[15,"aD"],"address"]]],[46,[53,[43,[15,"aD"],"address",[15,"aF"]],[22,[15,"m"],[46,[53,[43,[16,[15,"aD"],"address"],"_tag_metadata",[15,"aG"]]]]]]]]]]],[22,[15,"aA"],[46,[53,[52,"aF",[1,[15,"aB"],[16,[15,"aB"],"dataLayerSearchResults"]]],[22,[15,"aF"],[46,[53,[52,"aG",["q",[15,"aF"],[15,"aD"]]],[22,[15,"aG"],[46,[53,[2,[15,"y"],"setHitData",[7,[15,"l"],[15,"aG"]]]]]]]]]]]],[22,[15,"m"],[46,[53,[22,[30,[16,[15,"aD"],"email"],[16,[15,"aD"],"phone_number"]],[46,[53,[43,[15,"aD"],"_tag_metadata",[15,"aE"]]]]]]]],[2,[15,"y"],"setMetadata",[7,[17,[15,"k"],"AL"],[15,"aD"]]],[36,[15,"aD"]]]],["j",[15,"v"],[51,"",[7,"y"],[2,[15,"y"],"setMetadata",[7,[17,[15,"k"],"AM"],[15,"x"]]]]]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[52,"__module_featureFlags",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b",30],[52,"c",32],[52,"d",33],[52,"e",34],[52,"f",40],[52,"g",42],[52,"h",43],[52,"i",44],[52,"j",45],[52,"k",46],[52,"l",47],[52,"m",56],[52,"n",113],[52,"o",129],[52,"p",142],[52,"q",156],[52,"r",168],[52,"s",174],[52,"t",178],[52,"u",188],[52,"v",212],[36,[8,"DS",[15,"r"],"X",[15,"b"],"Z",[15,"c"],"AA",[15,"d"],"AB",[15,"e"],"AH",[15,"f"],"AJ",[15,"g"],"AK",[15,"h"],"AL",[15,"i"],"AM",[15,"j"],"AN",[15,"k"],"AO",[15,"l"],"ED",[15,"u"],"AT",[15,"m"],"DW",[15,"s"],"DZ",[15,"t"],"CA",[15,"n"],"CN",[15,"o"],"DA",[15,"p"],"EY",[15,"v"],"DK",[15,"q"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_metadataSchema",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b","accept_by_default"],[52,"c","add_tag_timing"],[52,"d","consent_state"],[52,"e","consent_updated"],[52,"f","conversion_linker_enabled"],[52,"g","cookie_options"],[52,"h","em_event"],[52,"i","event_start_timestamp_ms"],[52,"j","event_usage"],[52,"k","ga4_collection_subdomain"],[52,"l","hit_type"],[52,"m","hit_type_override"],[52,"n","is_conversion"],[52,"o","is_external_event"],[52,"p","is_first_visit"],[52,"q","is_first_visit_conversion"],[52,"r","is_fpm_encryption"],[52,"s","is_fpm_split"],[52,"t","is_gcp_conversion"],[52,"u","is_google_signals_allowed"],[52,"v","is_server_side_destination"],[52,"w","is_session_start"],[52,"x","is_session_start_conversion"],[52,"y","is_sgtm_ga_ads_conversion_study_control_group"],[52,"z","is_sgtm_prehit"],[52,"aA","is_split_conversion"],[52,"aB","is_syn"],[52,"aC","prehit_for_retry"],[52,"aD","redact_ads_data"],[52,"aE","redact_click_ids"],[52,"aF","send_ccm_parallel_ping"],[52,"aG","send_user_data_hit"],[52,"aH","speculative"],[52,"aI","syn_or_mod"],[52,"aJ","transient_ecsid"],[52,"aK","transmission_type"],[52,"aL","user_data"],[52,"aM","user_data_from_automatic"],[52,"aN","user_data_from_automatic_getter"],[52,"aO","user_data_from_code"],[52,"aP","user_data_from_manual"],[36,[8,"A",[15,"b"],"B",[15,"c"],"C",[15,"d"],"D",[15,"e"],"E",[15,"f"],"F",[15,"g"],"G",[15,"h"],"H",[15,"i"],"I",[15,"j"],"J",[15,"k"],"K",[15,"l"],"L",[15,"m"],"M",[15,"n"],"N",[15,"o"],"O",[15,"p"],"P",[15,"q"],"Q",[15,"r"],"R",[15,"s"],"S",[15,"t"],"T",[15,"u"],"U",[15,"v"],"V",[15,"w"],"W",[15,"x"],"X",[15,"y"],"Y",[15,"z"],"Z",[15,"aA"],"AA",[15,"aB"],"AB",[15,"aC"],"AC",[15,"aD"],"AD",[15,"aE"],"AE",[15,"aF"],"AF",[15,"aG"],"AG",[15,"aH"],"AH",[15,"aI"],"AI",[15,"aJ"],"AJ",[15,"aK"],"AK",[15,"aL"],"AL",[15,"aM"],"AM",[15,"aN"],"AN",[15,"aO"],"AO",[15,"aP"]]]],[36,["a"]]]],["$0"]]]
 
]
,"entities":{
"__ccd_ga_first":{"2":true,"5":true}
,
"__ccd_ga_last":{"2":true,"5":true}
,
"__e":{"2":true,"5":true}
,
"__ogt_1p_data_v2":{"2":true,"5":true}


}
,"blob":{"1":"1"}
,"permissions":{
"__ccd_ga_first":{}
,
"__ccd_ga_last":{}
,
"__e":{"read_event_data":{"eventDataAccess":"specific","keyPatterns":["event"]}}
,
"__ogt_1p_data_v2":{"detect_user_provided_data":{"limitDataSources":true,"allowAutoDataSources":true,"allowManualDataSources":false,"allowCodeDataSources":false}}


}



,"security_groups":{
"google":[
"__ccd_ga_first"
,
"__ccd_ga_last"
,
"__e"
,
"__ogt_1p_data_v2"

]


}



};




var k,aa=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},ba=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a},ea=function(a){for(var b=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global],c=0;c<b.length;++c){var d=b[c];if(d&&d.Math==Math)return d}throw Error("Cannot find global object");
},fa=ea(this),ia=function(a,b){if(b)a:{for(var c=fa,d=a.split("."),e=0;e<d.length-1;e++){var f=d[e];if(!(f in c))break a;c=c[f]}var g=d[d.length-1],h=c[g],m=b(h);m!=h&&m!=null&&ba(c,g,{configurable:!0,writable:!0,value:m})}};
ia("Symbol",function(a){if(a)return a;var b=function(f,g){this.C=f;ba(this,"description",{configurable:!0,writable:!0,value:g})};b.prototype.toString=function(){return this.C};var c="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(f||"")+"_"+d++,f)};return e});var ja=typeof Object.create=="function"?Object.create:function(a){var b=function(){};b.prototype=a;return new b},la;
if(typeof Object.setPrototypeOf=="function")la=Object.setPrototypeOf;else{var ma;a:{var oa={a:!0},pa={};try{pa.__proto__=oa;ma=pa.a;break a}catch(a){}ma=!1}la=ma?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}
var qa=la,sa=function(a,b){a.prototype=ja(b.prototype);a.prototype.constructor=a;if(qa)qa(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.Bq=b.prototype},l=function(a){var b=typeof Symbol!="undefined"&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:aa(a)};throw Error(String(a)+" is not an iterable or ArrayLike");},ua=function(a){for(var b,
c=[];!(b=a.next()).done;)c.push(b.value);return c},va=function(a){return a instanceof Array?a:ua(l(a))},xa=function(a){return wa(a,a)},wa=function(a,b){a.raw=b;Object.freeze&&(Object.freeze(a),Object.freeze(b));return a},ya=typeof Object.assign=="function"?Object.assign:function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)Object.prototype.hasOwnProperty.call(d,e)&&(a[e]=d[e])}return a};ia("Object.assign",function(a){return a||ya});
var za=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var Aa=this||self,Ba=function(a,b){function c(){}c.prototype=b.prototype;a.Bq=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.zr=function(d,e,f){for(var g=Array(arguments.length-2),h=2;h<arguments.length;h++)g[h-2]=arguments[h];return b.prototype[e].apply(d,g)}};var Ca=function(a,b){this.type=a;this.data=b};var Da=function(){this.map=new Map;this.C=new Set};k=Da.prototype;k.get=function(a){return this.map.get(String(a))};k.set=function(a,b){this.C.has(a)||this.map.set(String(a),b)};k.pl=function(a,b){this.set(a,b);this.C.add(a)};k.has=function(a){return this.map.has(String(a))};k.remove=function(a){this.C.has(a)||this.map.delete(String(a))};var Ea=function(a,b){switch(b){case 1:return a.map.keys();case 2:return a.map.values();case 3:return a.map.entries();default:return[]}};
Da.prototype.wa=function(){return Ea(this,1)};Da.prototype.Zb=function(){return Ea(this,2)};Da.prototype.Ib=function(){return Ea(this,3)};var Fa=function(){this.map={};this.C={}};k=Fa.prototype;k.get=function(a){return this.map["dust."+a]};k.set=function(a,b){var c="dust."+a;this.C.hasOwnProperty(c)||(this.map[c]=b)};k.pl=function(a,b){this.set(a,b);this.C["dust."+a]=!0};k.has=function(a){return this.map.hasOwnProperty("dust."+a)};k.remove=function(a){var b="dust."+a;this.C.hasOwnProperty(b)||delete this.map[b]};
var Ga=function(a,b){var c=[],d;for(d in a.map)if(a.map.hasOwnProperty(d)){var e=d.substring(5);switch(b){case 1:c.push(e);break;case 2:c.push(a.map[d]);break;case 3:c.push([e,a.map[d]])}}return c};Fa.prototype.wa=function(){return Ga(this,1)};Fa.prototype.Zb=function(){return Ga(this,2)};Fa.prototype.Ib=function(){return Ga(this,3)};var Ha=function(){};Ha.prototype.reset=function(){};var Ia=[],Ja={};function Ka(a){return Ia[a]===void 0?!1:Ia[a]};var La=function(a,b){this.P=a;this.parent=b;this.N=this.C=void 0;this.tb=!1;this.H=function(c,d,e){return c.apply(d,e)};this.values=Ka(15)?new Da:new Fa};k=La.prototype;k.add=function(a,b){this.tb||this.values.set(a,b)};k.nh=function(a,b){this.tb||this.values.pl(a,b)};k.set=function(a,b){this.tb||(!this.values.has(a)&&this.parent&&this.parent.has(a)?this.parent.set(a,b):this.values.set(a,b))};k.get=function(a){return this.values.has(a)?this.values.get(a):this.parent?this.parent.get(a):void 0};
k.has=function(a){return!!this.values.has(a)||!(!this.parent||!this.parent.has(a))};k.rb=function(){var a=new La(this.P,this);this.C&&a.Nb(this.C);a.Vc(this.H);a.Ld(this.N);return a};k.Cd=function(){return this.P};k.Nb=function(a){this.C=a};k.am=function(){return this.C};k.Vc=function(a){this.H=a};k.aj=function(){return this.H};k.Ua=function(){this.tb=!0};k.Ld=function(a){this.N=a};k.sb=function(){return this.N};var Ma=function(a,b){this.ba=a;this.parent=b;this.P=this.H=void 0;this.tb=!1;this.N=function(c,d,e){return c.apply(d,e)};this.C=new Map;this.R=new Set};Ma.prototype.add=function(a,b){Na(this,a,b,!1)};Ma.prototype.nh=function(a,b){Na(this,a,b,!0)};var Na=function(a,b,c,d){a.tb||a.R.has(b)||(d&&a.R.add(b),a.C.set(b,c))};k=Ma.prototype;k.set=function(a,b){this.tb||(!this.C.has(a)&&this.parent&&this.parent.has(a)?this.parent.set(a,b):this.R.has(a)||this.C.set(a,b))};
k.get=function(a){return this.C.has(a)?this.C.get(a):this.parent?this.parent.get(a):void 0};k.has=function(a){return!!this.C.has(a)||!(!this.parent||!this.parent.has(a))};k.rb=function(){var a=new Ma(this.ba,this);this.H&&a.Nb(this.H);a.Vc(this.N);a.Ld(this.P);return a};k.Cd=function(){return this.ba};k.Nb=function(a){this.H=a};k.am=function(){return this.H};k.Vc=function(a){this.N=a};k.aj=function(){return this.N};k.Ua=function(){this.tb=!0};k.Ld=function(a){this.P=a};k.sb=function(){return this.P};var Oa=function(a,b,c){var d;d=Error.call(this,a.message);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.om=a;this.Sl=c===void 0?!1:c;this.debugInfo=[];this.C=b};sa(Oa,Error);var Pa=function(a){return a instanceof Oa?a:new Oa(a,void 0,!0)};var Qa=new Map;function Ra(a,b){for(var c,d=l(b),e=d.next();!e.done&&!(c=Sa(a,e.value),c instanceof Ca);e=d.next());return c}
function Sa(a,b){try{var c,d;if(Ka(20))c=b[0],d=b.slice(1);else{var e=l(b);c=e.next().value;d=ua(e)}var f,g=String(c);f=Ka(17)?Qa.has(g)?Qa.get(g):a.get(g):a.get(g);if(!f||typeof f.invoke!=="function")throw Pa(Error("Attempting to execute non-function "+b[0]+"."));return Ka(18)?f.apply(a,d):f.invoke.apply(f,[a].concat(va(d)))}catch(m){var h=a.am();h&&h(m,b.context?{id:b[0],line:b.context.line}:null);throw m;}};var Ta=function(){this.H=new Ha;this.C=Ka(16)?new Ma(this.H):new La(this.H)};k=Ta.prototype;k.Cd=function(){return this.H};k.Nb=function(a){this.C.Nb(a)};k.Vc=function(a){this.C.Vc(a)};k.execute=function(a){return this.Bj([a].concat(va(za.apply(1,arguments))))};k.Bj=function(){for(var a,b=l(za.apply(0,arguments)),c=b.next();!c.done;c=b.next())a=Sa(this.C,c.value);return a};
k.eo=function(a){var b=za.apply(1,arguments),c=this.C.rb();c.Ld(a);for(var d,e=l(b),f=e.next();!f.done;f=e.next())d=Sa(c,f.value);return d};k.Ua=function(){this.C.Ua()};var Ua=function(){this.Ca=!1;this.aa=new Fa};k=Ua.prototype;k.get=function(a){return this.aa.get(a)};k.set=function(a,b){this.Ca||this.aa.set(a,b)};k.has=function(a){return this.aa.has(a)};k.remove=function(a){this.Ca||this.aa.remove(a)};k.wa=function(){return this.aa.wa()};k.Zb=function(){return this.aa.Zb()};k.Ib=function(){return this.aa.Ib()};k.Ua=function(){this.Ca=!0};k.tb=function(){return this.Ca};function Wa(){for(var a=Xa,b={},c=0;c<a.length;++c)b[a[c]]=c;return b}function Za(){var a="ABCDEFGHIJKLMNOPQRSTUVWXYZ";a+=a.toLowerCase()+"0123456789-_";return a+"."}var Xa,$a;function ab(a){Xa=Xa||Za();$a=$a||Wa();for(var b=[],c=0;c<a.length;c+=3){var d=c+1<a.length,e=c+2<a.length,f=a.charCodeAt(c),g=d?a.charCodeAt(c+1):0,h=e?a.charCodeAt(c+2):0,m=f>>2,n=(f&3)<<4|g>>4,p=(g&15)<<2|h>>6,q=h&63;e||(q=64,d||(p=64));b.push(Xa[m],Xa[n],Xa[p],Xa[q])}return b.join("")}
function bb(a){function b(m){for(;d<a.length;){var n=a.charAt(d++),p=$a[n];if(p!=null)return p;if(!/^[\s\xa0]*$/.test(n))throw Error("Unknown base64 encoding at char: "+n);}return m}Xa=Xa||Za();$a=$a||Wa();for(var c="",d=0;;){var e=b(-1),f=b(0),g=b(64),h=b(64);if(h===64&&e===-1)return c;c+=String.fromCharCode(e<<2|f>>4);g!==64&&(c+=String.fromCharCode(f<<4&240|g>>2),h!==64&&(c+=String.fromCharCode(g<<6&192|h)))}};var cb={};function db(a,b){cb[a]=cb[a]||[];cb[a][b]=!0}function eb(){cb.GTAG_EVENT_FEATURE_CHANNEL=fb}function gb(a){var b=cb[a];if(!b||b.length===0)return"";for(var c=[],d=0,e=0;e<b.length;e++)e%8===0&&e>0&&(c.push(String.fromCharCode(d)),d=0),b[e]&&(d|=1<<e%8);d>0&&c.push(String.fromCharCode(d));return ab(c.join("")).replace(/\.+$/,"")}function hb(){for(var a=[],b=cb.fdr||[],c=0;c<b.length;c++)b[c]&&a.push(c);return a.length>0?a:void 0};function ib(){}function jb(a){return typeof a==="function"}function kb(a){return typeof a==="string"}function lb(a){return typeof a==="number"&&!isNaN(a)}function mb(a){return Array.isArray(a)?a:[a]}function nb(a,b){if(a&&Array.isArray(a))for(var c=0;c<a.length;c++)if(a[c]&&b(a[c]))return a[c]}function ob(a,b){if(!lb(a)||!lb(b)||a>b)a=0,b=2147483647;return Math.floor(Math.random()*(b-a+1)+a)}
function pb(a,b){for(var c=new qb,d=0;d<a.length;d++)c.set(a[d],!0);for(var e=0;e<b.length;e++)if(c.get(b[e]))return!0;return!1}function rb(a,b){for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(c,a[c])}function tb(a){return!!a&&(Object.prototype.toString.call(a)==="[object Arguments]"||Object.prototype.hasOwnProperty.call(a,"callee"))}function ub(a){return Math.round(Number(a))||0}function vb(a){return"false"===String(a).toLowerCase()?!1:!!a}
function wb(a){var b=[];if(Array.isArray(a))for(var c=0;c<a.length;c++)b.push(String(a[c]));return b}function xb(a){return a?a.replace(/^\s+|\s+$/g,""):""}function yb(){return new Date(Date.now())}function zb(){return yb().getTime()}var qb=function(){this.prefix="gtm.";this.values={}};qb.prototype.set=function(a,b){this.values[this.prefix+a]=b};qb.prototype.get=function(a){return this.values[this.prefix+a]};qb.prototype.contains=function(a){return this.get(a)!==void 0};
function Ab(a,b,c){return a&&a.hasOwnProperty(b)?a[b]:c}function Bb(a){var b=a;return function(){if(b){var c=b;b=void 0;try{c()}catch(d){}}}}function Cb(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])}function Db(a,b){for(var c=[],d=0;d<a.length;d++)c.push(a[d]),c.push.apply(c,b[a[d]]||[]);return c}function Eb(a,b){return a.length>=b.length&&a.substring(0,b.length)===b}
function Fb(a,b,c){c=c||[];for(var d=a,e=0;e<b.length-1;e++){if(!d.hasOwnProperty(b[e]))return;d=d[b[e]];if(c.indexOf(d)>=0)return}return d}function Gb(a,b){for(var c={},d=c,e=a.split("."),f=0;f<e.length-1;f++)d=d[e[f]]={};d[e[e.length-1]]=b;return c}var Hb=/^\w{1,9}$/;function Jb(a,b){a=a||{};b=b||",";var c=[];rb(a,function(d,e){Hb.test(d)&&e&&c.push(d)});return c.join(b)}function Kb(a,b){function c(){e&&++d===b&&(e(),e=null,c.done=!0)}var d=0,e=a;c.done=!1;return c}
function Lb(a){if(!a)return a;var b=a;try{b=decodeURIComponent(a)}catch(d){}var c=b.split(",");return c.length===2&&c[0]===c[1]?c[0]:a}
function Mb(a,b,c){function d(n){var p=n.split("=")[0];if(a.indexOf(p)<0)return n;if(c!==void 0)return p+"="+c}function e(n){return n.split("&").map(d).filter(function(p){return p!==void 0}).join("&")}var f=b.href.split(/[?#]/)[0],g=b.search,h=b.hash;g[0]==="?"&&(g=g.substring(1));h[0]==="#"&&(h=h.substring(1));g=e(g);h=e(h);g!==""&&(g="?"+g);h!==""&&(h="#"+h);var m=""+f+g+h;m[m.length-1]==="/"&&(m=m.substring(0,m.length-1));return m}
function Nb(a){for(var b=0;b<3;++b)try{var c=decodeURIComponent(a).replace(/\+/g," ");if(c===a)break;a=c}catch(d){return""}return a}function Ob(){var a=x.crypto||x.msCrypto;if(a&&a.getRandomValues)try{var b=new Uint8Array(25);a.getRandomValues(b);return btoa(String.fromCharCode.apply(String,va(b))).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}catch(c){}};/*

 Copyright Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var Pb=globalThis.trustedTypes,Qb;function Rb(){var a=null;if(!Pb)return a;try{var b=function(c){return c};a=Pb.createPolicy("goog#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(c){}return a}function Sb(){Qb===void 0&&(Qb=Rb());return Qb};var Tb=function(a){this.C=a};Tb.prototype.toString=function(){return this.C+""};function Ub(a){var b=a,c=Sb(),d=c?c.createScriptURL(b):b;return new Tb(d)}function Vb(a){if(a instanceof Tb)return a.C;throw Error("");};var Wb=xa([""]),Xb=wa(["\x00"],["\\0"]),Yb=wa(["\n"],["\\n"]),Zb=wa(["\x00"],["\\u0000"]);function $b(a){return a.toString().indexOf("`")===-1}$b(function(a){return a(Wb)})||$b(function(a){return a(Xb)})||$b(function(a){return a(Yb)})||$b(function(a){return a(Zb)});var bc=function(a){this.C=a};bc.prototype.toString=function(){return this.C};var cc=function(a){this.Rp=a};function dc(a){return new cc(function(b){return b.substr(0,a.length+1).toLowerCase()===a+":"})}var ec=[dc("data"),dc("http"),dc("https"),dc("mailto"),dc("ftp"),new cc(function(a){return/^[^:]*([/?#]|$)/.test(a)})];function hc(a){var b;b=b===void 0?ec:b;if(a instanceof bc)return a;for(var c=0;c<b.length;++c){var d=b[c];if(d instanceof cc&&d.Rp(a))return new bc(a)}}var ic=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;
function jc(a){var b;if(a instanceof bc)if(a instanceof bc)b=a.C;else throw Error("");else b=ic.test(a)?a:void 0;return b};function kc(a,b){var c=jc(b);c!==void 0&&(a.action=c)};function lc(a,b){throw Error(b===void 0?"unexpected value "+a+"!":b);};var mc=function(a){this.C=a};mc.prototype.toString=function(){return this.C+""};var oc=function(){this.C=nc[0].toLowerCase()};oc.prototype.toString=function(){return this.C};function pc(a,b){var c=[new oc];if(c.length===0)throw Error("");var d=c.map(function(f){var g;if(f instanceof oc)g=f.C;else throw Error("");return g}),e=b.toLowerCase();if(d.every(function(f){return e.indexOf(f)!==0}))throw Error('Attribute "'+b+'" does not match any of the allowed prefixes.');a.setAttribute(b,"true")};var qc=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1};"ARTICLE SECTION NAV ASIDE H1 H2 H3 H4 H5 H6 HEADER FOOTER ADDRESS P HR PRE BLOCKQUOTE OL UL LH LI DL DT DD FIGURE FIGCAPTION MAIN DIV EM STRONG SMALL S CITE Q DFN ABBR RUBY RB RT RTC RP DATA TIME CODE VAR SAMP KBD SUB SUP I B U MARK BDI BDO SPAN BR WBR NOBR INS DEL PICTURE PARAM TRACK MAP TABLE CAPTION COLGROUP COL TBODY THEAD TFOOT TR TD TH SELECT DATALIST OPTGROUP OPTION OUTPUT PROGRESS METER FIELDSET LEGEND DETAILS SUMMARY MENU DIALOG SLOT CANVAS FONT CENTER ACRONYM BASEFONT BIG DIR HGROUP STRIKE TT".split(" ").concat(["BUTTON",
"INPUT"]);function rc(a){return a===null?"null":a===void 0?"undefined":a};var x=window,sc=window.history,z=document,tc=navigator;function uc(){var a;try{a=tc.serviceWorker}catch(b){return}return a}var vc=z.currentScript,wc=vc&&vc.src;function xc(a,b){var c=x,d=c[a];c[a]=d===void 0?b:d;return c[a]}function yc(a){return(tc.userAgent||"").indexOf(a)!==-1}function zc(){return yc("Firefox")||yc("FxiOS")}function Ac(){return(yc("GSA")||yc("GoogleApp"))&&(yc("iPhone")||yc("iPad"))}function Bc(){return yc("Edg/")||yc("EdgA/")||yc("EdgiOS/")}
var Cc={async:1,nonce:1,onerror:1,onload:1,src:1,type:1},Dc={onload:1,src:1,width:1,height:1,style:1};function Ec(a,b,c){b&&rb(b,function(d,e){d=d.toLowerCase();c.hasOwnProperty(d)||a.setAttribute(d,e)})}
function Fc(a,b,c,d,e){var f=z.createElement("script");Ec(f,d,Cc);f.type="text/javascript";f.async=d&&d.async===!1?!1:!0;var g;g=Ub(rc(a));f.src=Vb(g);var h,m=f.ownerDocument;m=m===void 0?document:m;var n,p,q=(p=(n=m).querySelector)==null?void 0:p.call(n,"script[nonce]");(h=q==null?"":q.nonce||q.getAttribute("nonce")||"")&&f.setAttribute("nonce",h);b&&(f.onload=b);c&&(f.onerror=c);if(e)e.appendChild(f);else{var r=z.getElementsByTagName("script")[0]||z.body||z.head;r.parentNode.insertBefore(f,r)}return f}
function Gc(){if(wc){var a=wc.toLowerCase();if(a.indexOf("https://")===0)return 2;if(a.indexOf("http://")===0)return 3}return 1}function Hc(a,b,c,d,e,f){f=f===void 0?!0:f;var g=e,h=!1;g||(g=z.createElement("iframe"),h=!0);Ec(g,c,Dc);d&&rb(d,function(n,p){g.dataset[n]=p});f&&(g.height="0",g.width="0",g.style.display="none",g.style.visibility="hidden");a!==void 0&&(g.src=a);if(h){var m=z.body&&z.body.lastChild||z.body||z.head;m.parentNode.insertBefore(g,m)}b&&(g.onload=b);return g}
function Ic(a,b,c,d){return Jc(a,b,c,d)}function Kc(a,b,c,d){a.addEventListener&&a.addEventListener(b,c,!!d)}function Lc(a,b,c){a.removeEventListener&&a.removeEventListener(b,c,!1)}function B(a){x.setTimeout(a,0)}function Mc(a,b){return a&&b&&a.attributes&&a.attributes[b]?a.attributes[b].value:null}function Nc(a){var b=a.innerText||a.textContent||"";b&&b!==" "&&(b=b.replace(/^[\s\xa0]+/g,""),b=b.replace(/[\s\xa0]+$/g,""));b&&(b=b.replace(/(\xa0+|\s{2,}|\n|\r\t)/g," "));return b}
function Oc(a){var b=z.createElement("div"),c=b,d,e=rc("A<div>"+a+"</div>"),f=Sb(),g=f?f.createHTML(e):e;d=new mc(g);if(c.nodeType===1&&/^(script|style)$/i.test(c.tagName))throw Error("");var h;if(d instanceof mc)h=d.C;else throw Error("");c.innerHTML=h;b=b.lastChild;for(var m=[];b&&b.firstChild;)m.push(b.removeChild(b.firstChild));return m}
function Pc(a,b,c){c=c||100;for(var d={},e=0;e<b.length;e++)d[b[e]]=!0;for(var f=a,g=0;f&&g<=c;g++){if(d[String(f.tagName).toLowerCase()])return f;f=f.parentElement}return null}function Qc(a,b,c){var d;try{d=tc.sendBeacon&&tc.sendBeacon(a)}catch(e){db("TAGGING",15)}d?b==null||b():Jc(a,b,c)}function Rc(a,b){try{return tc.sendBeacon(a,b)}catch(c){db("TAGGING",15)}return!1}var Sc={cache:"no-store",credentials:"include",keepalive:!0,method:"POST",mode:"no-cors",redirect:"follow"};
function Uc(a,b,c,d,e){if(Vc()){var f=Object.assign({},Sc);b&&(f.body=b);c&&(c.attributionReporting&&(f.attributionReporting=c.attributionReporting),c.browsingTopics&&(f.browsingTopics=c.browsingTopics),c.credentials&&(f.credentials=c.credentials),c.mode&&(f.mode=c.mode),c.method&&(f.method=c.method));try{var g=x.fetch(a,f);if(g)return g.then(function(m){m&&(m.ok||m.status===0)?d==null||d():e==null||e()}).catch(function(){e==null||e()}),!0}catch(m){}}if(c&&c.Ch)return e==null||e(),!1;if(b){var h=
Rc(a,b);h?d==null||d():e==null||e();return h}Wc(a,d,e);return!0}function Vc(){return typeof x.fetch==="function"}function Xc(a,b){var c=a[b];c&&typeof c.animVal==="string"&&(c=c.animVal);return c}function Yc(){var a=x.performance;if(a&&jb(a.now))return a.now()}
function Zc(){var a,b=x.performance;if(b&&b.getEntriesByType)try{var c=b.getEntriesByType("navigation");c&&c.length>0&&(a=c[0].type)}catch(d){return"e"}if(!a)return"u";switch(a){case "navigate":return"n";case "back_forward":return"h";case "reload":return"r";case "prerender":return"p";default:return"x"}}function $c(){return x.performance||void 0}function ad(){var a=x.webPixelsManager;return a?a.createShopifyExtend!==void 0:!1}
var Jc=function(a,b,c,d){var e=new Image(1,1);Ec(e,d,{});e.onload=function(){e.onload=null;b&&b()};e.onerror=function(){e.onerror=null;c&&c()};e.src=a;return e},Wc=Qc;function bd(a,b){return this.evaluate(a)&&this.evaluate(b)}function cd(a,b){return this.evaluate(a)===this.evaluate(b)}function dd(a,b){return this.evaluate(a)||this.evaluate(b)}function ed(a,b){var c=this.evaluate(a),d=this.evaluate(b);return String(c).indexOf(String(d))>-1}function fd(a,b){var c=String(this.evaluate(a)),d=String(this.evaluate(b));return c.substring(0,d.length)===d}
function gd(a,b){var c=this.evaluate(a),d=this.evaluate(b);switch(c){case "pageLocation":var e=x.location.href;d instanceof Ua&&d.get("stripProtocol")&&(e=e.replace(/^https?:\/\//,""));return e}};/*
 jQuery (c) 2005, 2012 jQuery Foundation, Inc. jquery.org/license.
*/
var hd=/\[object (Boolean|Number|String|Function|Array|Date|RegExp)\]/,id=function(a){if(a==null)return String(a);var b=hd.exec(Object.prototype.toString.call(Object(a)));return b?b[1].toLowerCase():"object"},jd=function(a,b){return Object.prototype.hasOwnProperty.call(Object(a),b)},kd=function(a){if(!a||id(a)!="object"||a.nodeType||a==a.window)return!1;try{if(a.constructor&&!jd(a,"constructor")&&!jd(a.constructor.prototype,"isPrototypeOf"))return!1}catch(c){return!1}for(var b in a);return b===void 0||
jd(a,b)},ld=function(a,b){var c=b||(id(a)=="array"?[]:{}),d;for(d in a)if(jd(a,d)){var e=a[d];id(e)=="array"?(id(c[d])!="array"&&(c[d]=[]),c[d]=ld(e,c[d])):kd(e)?(kd(c[d])||(c[d]={}),c[d]=ld(e,c[d])):c[d]=e}return c};function md(a){if(a==void 0||Array.isArray(a)||kd(a))return!0;switch(typeof a){case "boolean":case "number":case "string":case "function":return!0}return!1}function nd(a){return typeof a==="number"&&a>=0&&isFinite(a)&&a%1===0||typeof a==="string"&&a[0]!=="-"&&a===""+parseInt(a)};var od=function(a){a=a===void 0?[]:a;this.aa=new Fa;this.values=[];this.Ca=!1;for(var b in a)a.hasOwnProperty(b)&&(nd(b)?this.values[Number(b)]=a[Number(b)]:this.aa.set(b,a[b]))};k=od.prototype;k.toString=function(a){if(a&&a.indexOf(this)>=0)return"";for(var b=[],c=0;c<this.values.length;c++){var d=this.values[c];d===null||d===void 0?b.push(""):d instanceof od?(a=a||[],a.push(this),b.push(d.toString(a)),a.pop()):b.push(String(d))}return b.join(",")};
k.set=function(a,b){if(!this.Ca)if(a==="length"){if(!nd(b))throw Pa(Error("RangeError: Length property must be a valid integer."));this.values.length=Number(b)}else nd(a)?this.values[Number(a)]=b:this.aa.set(a,b)};k.get=function(a){return a==="length"?this.length():nd(a)?this.values[Number(a)]:this.aa.get(a)};k.length=function(){return this.values.length};k.wa=function(){for(var a=this.aa.wa(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push(String(b));return a};
k.Zb=function(){for(var a=this.aa.Zb(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push(this.values[b]);return a};k.Ib=function(){for(var a=this.aa.Ib(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push([String(b),this.values[b]]);return a};k.remove=function(a){nd(a)?delete this.values[Number(a)]:this.Ca||this.aa.remove(a)};k.pop=function(){return this.values.pop()};k.push=function(){return this.values.push.apply(this.values,va(za.apply(0,arguments)))};k.shift=function(){return this.values.shift()};
k.splice=function(a,b){var c=za.apply(2,arguments);return b===void 0&&c.length===0?new od(this.values.splice(a)):new od(this.values.splice.apply(this.values,[a,b||0].concat(va(c))))};k.unshift=function(){return this.values.unshift.apply(this.values,va(za.apply(0,arguments)))};k.has=function(a){return nd(a)&&this.values.hasOwnProperty(a)||this.aa.has(a)};k.Ua=function(){this.Ca=!0;Object.freeze(this.values)};k.tb=function(){return this.Ca};
function pd(a){for(var b=[],c=0;c<a.length();c++)a.has(c)&&(b[c]=a.get(c));return b};var qd=function(a,b){this.functionName=a;this.zd=b;this.aa=new Fa;this.Ca=!1};k=qd.prototype;k.toString=function(){return this.functionName};k.getName=function(){return this.functionName};k.getKeys=function(){return new od(this.wa())};k.invoke=function(a){return this.zd.call.apply(this.zd,[new rd(this,a)].concat(va(za.apply(1,arguments))))};k.apply=function(a,b){return this.zd.apply(new rd(this,a),b)};k.Lb=function(a){var b=za.apply(1,arguments);try{return this.invoke.apply(this,[a].concat(va(b)))}catch(c){}};
k.get=function(a){return this.aa.get(a)};k.set=function(a,b){this.Ca||this.aa.set(a,b)};k.has=function(a){return this.aa.has(a)};k.remove=function(a){this.Ca||this.aa.remove(a)};k.wa=function(){return this.aa.wa()};k.Zb=function(){return this.aa.Zb()};k.Ib=function(){return this.aa.Ib()};k.Ua=function(){this.Ca=!0};k.tb=function(){return this.Ca};var sd=function(a,b){qd.call(this,a,b)};sa(sd,qd);var td=function(a,b){qd.call(this,a,b)};sa(td,qd);var rd=function(a,b){this.zd=a;this.K=b};
rd.prototype.evaluate=function(a){var b=this.K;return Array.isArray(a)?Sa(b,a):a};rd.prototype.getName=function(){return this.zd.getName()};rd.prototype.Cd=function(){return this.K.Cd()};var ud=function(){this.map=new Map};ud.prototype.set=function(a,b){this.map.set(a,b)};ud.prototype.get=function(a){return this.map.get(a)};var vd=function(){this.keys=[];this.values=[]};vd.prototype.set=function(a,b){this.keys.push(a);this.values.push(b)};vd.prototype.get=function(a){var b=this.keys.indexOf(a);if(b>-1)return this.values[b]};function wd(){try{return Map?new ud:new vd}catch(a){return new vd}};var xd=function(a){if(a instanceof xd)return a;if(md(a))throw Error("Type of given value has an equivalent Pixie type.");this.value=a};xd.prototype.getValue=function(){return this.value};xd.prototype.toString=function(){return String(this.value)};var zd=function(a){this.promise=a;this.Ca=!1;this.aa=new Fa;this.aa.set("then",yd(this));this.aa.set("catch",yd(this,!0));this.aa.set("finally",yd(this,!1,!0))};k=zd.prototype;k.get=function(a){return this.aa.get(a)};k.set=function(a,b){this.Ca||this.aa.set(a,b)};k.has=function(a){return this.aa.has(a)};k.remove=function(a){this.Ca||this.aa.remove(a)};k.wa=function(){return this.aa.wa()};k.Zb=function(){return this.aa.Zb()};k.Ib=function(){return this.aa.Ib()};
var yd=function(a,b,c){b=b===void 0?!1:b;c=c===void 0?!1:c;return new sd("",function(d,e){b&&(e=d,d=void 0);c&&(e=d);d instanceof sd||(d=void 0);e instanceof sd||(e=void 0);var f=this.K.rb(),g=function(m){return function(n){try{return c?(m.invoke(f),a.promise):m.invoke(f,n)}catch(p){return Promise.reject(p instanceof Error?new xd(p):String(p))}}},h=a.promise.then(d&&g(d),e&&g(e));return new zd(h)})};zd.prototype.Ua=function(){this.Ca=!0};zd.prototype.tb=function(){return this.Ca};function Ad(a,b,c){var d=wd(),e=function(g,h){for(var m=g.wa(),n=0;n<m.length;n++)h[m[n]]=f(g.get(m[n]))},f=function(g){if(g===null||g===void 0)return g;var h=d.get(g);if(h)return h;if(g instanceof od){var m=[];d.set(g,m);for(var n=g.wa(),p=0;p<n.length;p++)m[n[p]]=f(g.get(n[p]));return m}if(g instanceof zd)return g.promise.then(function(u){return Ad(u,b,1)},function(u){return Promise.reject(Ad(u,b,1))});if(g instanceof Ua){var q={};d.set(g,q);e(g,q);return q}if(g instanceof sd){var r=function(){for(var u=
za.apply(0,arguments),v=[],w=0;w<u.length;w++)v[w]=Bd(u[w],b,c);var y=new La(b?b.Cd():new Ha);b&&y.Ld(b.sb());return f(g.invoke.apply(g,[y].concat(va(v))))};d.set(g,r);e(g,r);return r}var t=!1;switch(c){case 1:t=!0;break;case 2:t=!1;break;case 3:t=!1;break;default:}if(g instanceof xd&&t)return g.getValue();switch(typeof g){case "boolean":case "number":case "string":case "undefined":return g;
case "object":if(g===null)return null}};return f(a)}
function Bd(a,b,c){var d=wd(),e=function(g,h){for(var m in g)g.hasOwnProperty(m)&&h.set(m,f(g[m]))},f=function(g){var h=d.get(g);if(h)return h;if(Array.isArray(g)||tb(g)){var m=new od;d.set(g,m);for(var n in g)g.hasOwnProperty(n)&&m.set(n,f(g[n]));return m}if(kd(g)){var p=new Ua;d.set(g,p);e(g,p);return p}if(typeof g==="function"){var q=new sd("",function(){for(var u=za.apply(0,arguments),v=[],w=0;w<u.length;w++)v[w]=Ad(this.evaluate(u[w]),b,c);return f(this.K.aj()(g,g,v))});d.set(g,q);e(g,q);return q}var r=typeof g;if(g===null||r==="string"||r==="number"||r==="boolean")return g;var t=!1;switch(c){case 1:t=!0;break;case 2:t=!1;break;default:}if(g!==void 0&&t)return new xd(g)};return f(a)};var Cd={supportedMethods:"concat every filter forEach hasOwnProperty indexOf join lastIndexOf map pop push reduce reduceRight reverse shift slice some sort splice unshift toString".split(" "),concat:function(a){for(var b=[],c=0;c<this.length();c++)b.push(this.get(c));for(var d=1;d<arguments.length;d++)if(arguments[d]instanceof od)for(var e=arguments[d],f=0;f<e.length();f++)b.push(e.get(f));else b.push(arguments[d]);return new od(b)},every:function(a,b){for(var c=this.length(),d=0;d<this.length()&&
d<c;d++)if(this.has(d)&&!b.invoke(a,this.get(d),d,this))return!1;return!0},filter:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&b.invoke(a,this.get(e),e,this)&&d.push(this.get(e));return new od(d)},forEach:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)this.has(d)&&b.invoke(a,this.get(d),d,this)},hasOwnProperty:function(a,b){return this.has(b)},indexOf:function(a,b,c){var d=this.length(),e=c===void 0?0:Number(c);e<0&&(e=Math.max(d+e,0));for(var f=
e;f<d;f++)if(this.has(f)&&this.get(f)===b)return f;return-1},join:function(a,b){for(var c=[],d=0;d<this.length();d++)c.push(this.get(d));return c.join(b)},lastIndexOf:function(a,b,c){var d=this.length(),e=d-1;c!==void 0&&(e=c<0?d+c:Math.min(c,e));for(var f=e;f>=0;f--)if(this.has(f)&&this.get(f)===b)return f;return-1},map:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&(d[e]=b.invoke(a,this.get(e),e,this));return new od(d)},pop:function(){return this.pop()},push:function(a){return this.push.apply(this,
va(za.apply(1,arguments)))},reduce:function(a,b,c){var d=this.length(),e,f=0;if(c!==void 0)e=c;else{if(d===0)throw Pa(Error("TypeError: Reduce on List with no elements."));for(var g=0;g<d;g++)if(this.has(g)){e=this.get(g);f=g+1;break}if(g===d)throw Pa(Error("TypeError: Reduce on List with no elements."));}for(var h=f;h<d;h++)this.has(h)&&(e=b.invoke(a,e,this.get(h),h,this));return e},reduceRight:function(a,b,c){var d=this.length(),e,f=d-1;if(c!==void 0)e=c;else{if(d===0)throw Pa(Error("TypeError: ReduceRight on List with no elements."));
for(var g=1;g<=d;g++)if(this.has(d-g)){e=this.get(d-g);f=d-(g+1);break}if(g>d)throw Pa(Error("TypeError: ReduceRight on List with no elements."));}for(var h=f;h>=0;h--)this.has(h)&&(e=b.invoke(a,e,this.get(h),h,this));return e},reverse:function(){for(var a=pd(this),b=a.length-1,c=0;b>=0;b--,c++)a.hasOwnProperty(b)?this.set(c,a[b]):this.remove(c);return this},shift:function(){return this.shift()},slice:function(a,b,c){var d=this.length();b===void 0&&(b=0);b=b<0?Math.max(d+b,0):Math.min(b,d);c=c===
void 0?d:c<0?Math.max(d+c,0):Math.min(c,d);c=Math.max(b,c);for(var e=[],f=b;f<c;f++)e.push(this.get(f));return new od(e)},some:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)if(this.has(d)&&b.invoke(a,this.get(d),d,this))return!0;return!1},sort:function(a,b){var c=pd(this);b===void 0?c.sort():c.sort(function(e,f){return Number(b.invoke(a,e,f))});for(var d=0;d<c.length;d++)c.hasOwnProperty(d)?this.set(d,c[d]):this.remove(d);return this},splice:function(a,b,c){return this.splice.apply(this,
[b,c].concat(va(za.apply(3,arguments))))},toString:function(){return this.toString()},unshift:function(a){return this.unshift.apply(this,va(za.apply(1,arguments)))}};var Dd={charAt:1,concat:1,indexOf:1,lastIndexOf:1,match:1,replace:1,search:1,slice:1,split:1,substring:1,toLowerCase:1,toLocaleLowerCase:1,toString:1,toUpperCase:1,toLocaleUpperCase:1,trim:1},Ed=new Ca("break"),Fd=new Ca("continue");function Gd(a,b){return this.evaluate(a)+this.evaluate(b)}function Hd(a,b){return this.evaluate(a)&&this.evaluate(b)}
function Id(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(!(f instanceof od))throw Error("Error: Non-List argument given to Apply instruction.");if(d===null||d===void 0)throw Pa(Error("TypeError: Can't read property "+e+" of "+d+"."));var g=typeof d==="number";if(typeof d==="boolean"||g){if(e==="toString"){if(g&&f.length()){var h=Ad(f.get(0));try{return d.toString(h)}catch(v){}}return d.toString()}throw Pa(Error("TypeError: "+d+"."+e+" is not a function."));}if(typeof d===
"string"){if(Dd.hasOwnProperty(e)){var m=2;m=1;var n=Ad(f,void 0,m);return Bd(d[e].apply(d,n),this.K)}throw Pa(Error("TypeError: "+e+" is not a function"));}if(d instanceof od){if(d.has(e)){var p=d.get(String(e));if(p instanceof sd){var q=pd(f);return p.invoke.apply(p,[this.K].concat(va(q)))}throw Pa(Error("TypeError: "+e+" is not a function"));}if(Cd.supportedMethods.indexOf(e)>=
0){var r=pd(f);return Cd[e].call.apply(Cd[e],[d,this.K].concat(va(r)))}}if(d instanceof sd||d instanceof Ua||d instanceof zd){if(d.has(e)){var t=d.get(e);if(t instanceof sd){var u=pd(f);return t.invoke.apply(t,[this.K].concat(va(u)))}throw Pa(Error("TypeError: "+e+" is not a function"));}if(e==="toString")return d instanceof sd?d.getName():d.toString();if(e==="hasOwnProperty")return d.has(f.get(0))}if(d instanceof xd&&e==="toString")return d.toString();throw Pa(Error("TypeError: Object has no '"+
e+"' property."));}function Jd(a,b){a=this.evaluate(a);if(typeof a!=="string")throw Error("Invalid key name given for assignment.");var c=this.K;if(!c.has(a))throw Error("Attempting to assign to undefined value "+b);var d=this.evaluate(b);c.set(a,d);return d}function Kd(){var a=za.apply(0,arguments),b=this.K.rb(),c=Ra(b,a);if(c instanceof Ca)return c}function Ld(){return Ed}function Md(a){for(var b=this.evaluate(a),c=0;c<b.length;c++){var d=this.evaluate(b[c]);if(d instanceof Ca)return d}}
function Nd(){for(var a=this.K,b=0;b<arguments.length-1;b+=2){var c=arguments[b];if(typeof c==="string"){var d=this.evaluate(arguments[b+1]);a.nh(c,d)}}}function Od(){return Fd}function Pd(a,b){return new Ca(a,this.evaluate(b))}function Qd(a,b){for(var c=za.apply(2,arguments),d=new od,e=this.evaluate(b),f=0;f<e.length;f++)d.push(e[f]);var g=[51,a,d].concat(va(c));this.K.add(a,this.evaluate(g))}function Rd(a,b){return this.evaluate(a)/this.evaluate(b)}
function Sd(a,b){var c=this.evaluate(a),d=this.evaluate(b),e=c instanceof xd,f=d instanceof xd;return e||f?e&&f?c.getValue()===d.getValue():!1:c==d}function Td(){for(var a,b=0;b<arguments.length;b++)a=this.evaluate(arguments[b]);return a}function Ud(a,b,c,d){for(var e=0;e<b();e++){var f=a(c(e)),g=Ra(f,d);if(g instanceof Ca){if(g.type==="break")break;if(g.type==="return")return g}}}
function Vd(a,b,c){if(typeof b==="string")return Ud(a,function(){return b.length},function(f){return f},c);if(b instanceof Ua||b instanceof zd||b instanceof od||b instanceof sd){var d=b.wa(),e=d.length;return Ud(a,function(){return e},function(f){return d[f]},c)}}function Wd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return Vd(function(h){g.set(d,h);return g},e,f)}
function Xd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return Vd(function(h){var m=g.rb();m.nh(d,h);return m},e,f)}function Yd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return Vd(function(h){var m=g.rb();m.add(d,h);return m},e,f)}function Zd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return $d(function(h){g.set(d,h);return g},e,f)}
function ae(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return $d(function(h){var m=g.rb();m.nh(d,h);return m},e,f)}function be(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return $d(function(h){var m=g.rb();m.add(d,h);return m},e,f)}
function $d(a,b,c){if(typeof b==="string")return Ud(a,function(){return b.length},function(d){return b[d]},c);if(b instanceof od)return Ud(a,function(){return b.length()},function(d){return b.get(d)},c);throw Pa(Error("The value is not iterable."));}
function ce(a,b,c,d){function e(q,r){for(var t=0;t<f.length();t++){var u=f.get(t);r.add(u,q.get(u))}}var f=this.evaluate(a);if(!(f instanceof od))throw Error("TypeError: Non-List argument given to ForLet instruction.");var g=this.K,h=this.evaluate(d),m=g.rb();for(e(g,m);Sa(m,b);){var n=Ra(m,h);if(n instanceof Ca){if(n.type==="break")break;if(n.type==="return")return n}var p=g.rb();e(m,p);Sa(p,c);m=p}}
function de(a,b){var c=za.apply(2,arguments),d=this.K,e=this.evaluate(b);if(!(e instanceof od))throw Error("Error: non-List value given for Fn argument names.");return new sd(a,function(){return function(){var f=za.apply(0,arguments),g=d.rb();g.sb()===void 0&&g.Ld(this.K.sb());for(var h=[],m=0;m<f.length;m++){var n=this.evaluate(f[m]);h[m]=n}for(var p=e.get("length"),q=0;q<p;q++)q<h.length?g.add(e.get(q),h[q]):g.add(e.get(q),void 0);g.add("arguments",new od(h));var r=Ra(g,c);if(r instanceof Ca)return r.type===
"return"?r.data:r}}())}function ee(a){var b=this.evaluate(a),c=this.K;if(fe&&!c.has(b))throw new ReferenceError(b+" is not defined.");return c.get(b)}
function ge(a,b){var c,d=this.evaluate(a),e=this.evaluate(b);if(d===void 0||d===null)throw Pa(Error("TypeError: Cannot read properties of "+d+" (reading '"+e+"')"));if(d instanceof Ua||d instanceof zd||d instanceof od||d instanceof sd)c=d.get(e);else if(typeof d==="string")e==="length"?c=d.length:nd(e)&&(c=d[e]);else if(d instanceof xd)return;return c}function he(a,b){return this.evaluate(a)>this.evaluate(b)}function ie(a,b){return this.evaluate(a)>=this.evaluate(b)}
function je(a,b){var c=this.evaluate(a),d=this.evaluate(b);c instanceof xd&&(c=c.getValue());d instanceof xd&&(d=d.getValue());return c===d}function ke(a,b){return!je.call(this,a,b)}function le(a,b,c){var d=[];this.evaluate(a)?d=this.evaluate(b):c&&(d=this.evaluate(c));var e=Ra(this.K,d);if(e instanceof Ca)return e}var fe=!1;
function me(a,b){return this.evaluate(a)<this.evaluate(b)}function ne(a,b){return this.evaluate(a)<=this.evaluate(b)}function oe(){for(var a=new od,b=0;b<arguments.length;b++){var c=this.evaluate(arguments[b]);a.push(c)}return a}function pe(){for(var a=new Ua,b=0;b<arguments.length-1;b+=2){var c=String(this.evaluate(arguments[b])),d=this.evaluate(arguments[b+1]);a.set(c,d)}return a}function qe(a,b){return this.evaluate(a)%this.evaluate(b)}
function re(a,b){return this.evaluate(a)*this.evaluate(b)}function se(a){return-this.evaluate(a)}function te(a){return!this.evaluate(a)}function ue(a,b){return!Sd.call(this,a,b)}function ve(){return null}function we(a,b){return this.evaluate(a)||this.evaluate(b)}function xe(a,b){var c=this.evaluate(a);this.evaluate(b);return c}function ye(a){return this.evaluate(a)}function ze(){return za.apply(0,arguments)}function Ae(a){return new Ca("return",this.evaluate(a))}
function Be(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(d===null||d===void 0)throw Pa(Error("TypeError: Can't set property "+e+" of "+d+"."));(d instanceof sd||d instanceof od||d instanceof Ua)&&d.set(String(e),f);return f}function Ce(a,b){return this.evaluate(a)-this.evaluate(b)}
function De(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(!Array.isArray(e)||!Array.isArray(f))throw Error("Error: Malformed switch instruction.");for(var g,h=!1,m=0;m<e.length;m++)if(h||d===this.evaluate(e[m]))if(g=this.evaluate(f[m]),g instanceof Ca){var n=g.type;if(n==="break")return;if(n==="return"||n==="continue")return g}else h=!0;if(f.length===e.length+1&&(g=this.evaluate(f[f.length-1]),g instanceof Ca&&(g.type==="return"||g.type==="continue")))return g}
function Ee(a,b,c){return this.evaluate(a)?this.evaluate(b):this.evaluate(c)}function Fe(a){var b=this.evaluate(a);return b instanceof sd?"function":typeof b}function Ge(){for(var a=this.K,b=0;b<arguments.length;b++){var c=arguments[b];typeof c!=="string"||a.add(c,void 0)}}
function He(a,b,c,d){var e=this.evaluate(d);if(this.evaluate(c)){var f=Ra(this.K,e);if(f instanceof Ca){if(f.type==="break")return;if(f.type==="return")return f}}for(;this.evaluate(a);){var g=Ra(this.K,e);if(g instanceof Ca){if(g.type==="break")break;if(g.type==="return")return g}this.evaluate(b)}}function Ie(a){return~Number(this.evaluate(a))}function Je(a,b){return Number(this.evaluate(a))<<Number(this.evaluate(b))}function Ke(a,b){return Number(this.evaluate(a))>>Number(this.evaluate(b))}
function Le(a,b){return Number(this.evaluate(a))>>>Number(this.evaluate(b))}function Me(a,b){return Number(this.evaluate(a))&Number(this.evaluate(b))}function Ne(a,b){return Number(this.evaluate(a))^Number(this.evaluate(b))}function Oe(a,b){return Number(this.evaluate(a))|Number(this.evaluate(b))}function Pe(){}
function Qe(a,b,c){try{var d=this.evaluate(b);if(d instanceof Ca)return d}catch(h){if(!(h instanceof Oa&&h.Sl))throw h;var e=this.K.rb();a!==""&&(h instanceof Oa&&(h=h.om),e.add(a,new xd(h)));var f=this.evaluate(c),g=Ra(e,f);if(g instanceof Ca)return g}}function Re(a,b){var c,d;try{d=this.evaluate(a)}catch(f){if(!(f instanceof Oa&&f.Sl))throw f;c=f}var e=this.evaluate(b);if(e instanceof Ca)return e;if(c)throw c;if(d instanceof Ca)return d};var Te=function(){this.C=new Ta;Se(this)};Te.prototype.execute=function(a){return this.C.Bj(a)};var Se=function(a){var b=function(c,d){var e=new td(String(c),d);e.Ua();var f=String(c);a.C.C.set(f,e);Qa.set(f,e)};b("map",pe);b("and",bd);b("contains",ed);b("equals",cd);b("or",dd);b("startsWith",fd);b("variable",gd)};Te.prototype.Nb=function(a){this.C.Nb(a)};var Ve=function(){this.H=!1;this.C=new Ta;Ue(this);this.H=!0};Ve.prototype.execute=function(a){return We(this.C.Bj(a))};var Xe=function(a,b,c){return We(a.C.eo(b,c))};Ve.prototype.Ua=function(){this.C.Ua()};
var Ue=function(a){var b=function(c,d){var e=String(c),f=new td(e,d);f.Ua();a.C.C.set(e,f);Qa.set(e,f)};b(0,Gd);b(1,Hd);b(2,Id);b(3,Jd);b(56,Me);b(57,Je);b(58,Ie);b(59,Oe);b(60,Ke);b(61,Le);b(62,Ne);b(53,Kd);b(4,Ld);b(5,Md);b(68,Qe);b(52,Nd);b(6,Od);b(49,Pd);b(7,oe);b(8,pe);b(9,Md);b(50,Qd);b(10,Rd);b(12,Sd);b(13,Td);b(67,Re);b(51,de);b(47,Wd);b(54,Xd);b(55,Yd);b(63,ce);b(64,Zd);b(65,ae);b(66,be);b(15,ee);b(16,ge);b(17,ge);b(18,he);b(19,ie);b(20,je);b(21,ke);b(22,le);b(23,me);b(24,ne);b(25,qe);b(26,
re);b(27,se);b(28,te);b(29,ue);b(45,ve);b(30,we);b(32,xe);b(33,xe);b(34,ye);b(35,ye);b(46,ze);b(36,Ae);b(43,Be);b(37,Ce);b(38,De);b(39,Ee);b(40,Fe);b(44,Pe);b(41,Ge);b(42,He)};Ve.prototype.Cd=function(){return this.C.Cd()};Ve.prototype.Nb=function(a){this.C.Nb(a)};Ve.prototype.Vc=function(a){this.C.Vc(a)};
function We(a){if(a instanceof Ca||a instanceof sd||a instanceof od||a instanceof Ua||a instanceof zd||a instanceof xd||a===null||a===void 0||typeof a==="string"||typeof a==="number"||typeof a==="boolean")return a};var Ye=function(a){this.message=a};function Ze(a){a.Gr=!0;return a};var $e=Ze(function(a){return typeof a==="string"});function af(a){var b="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[a];return b===void 0?new Ye("Value "+a+" can not be encoded in web-safe base64 dictionary."):b};function bf(a){switch(a){case 1:return"1";case 2:case 4:return"0";default:return"-"}};var cf=/^[1-9a-zA-Z_-][1-9a-c][1-9a-v]\d$/;function df(a,b){for(var c="",d=!0;a>7;){var e=a&31;a>>=5;d?d=!1:e|=32;c=""+af(e)+c}a<<=2;d||(a|=32);return c=""+af(a|b)+c}
function ef(a,b){var c;var d=a.Uc,e=a.Sc;d===void 0?c="":(e||(e=0),c=""+df(1,1)+af(d<<2|e));var f=a.Rl,g=a.Ko,h="4"+c+(f?""+df(2,1)+af(f):"")+(g?""+df(12,1)+af(g):""),m,n=a.Cj;m=n&&cf.test(n)?""+df(3,2)+n:"";var p,q=a.yj;p=q?""+df(4,1)+af(q):"";var r;var t=a.ctid;if(t&&b){var u=df(5,3),v=t.split("-"),w=v[0].toUpperCase();if(w!=="GTM"&&w!=="OPT")r="";else{var y=v[1];r=""+u+af(1+y.length)+(a.fm||0)+y}}else r="";var A=a.Aq,C=a.ve,E=a.Ma,G=a.Kr,I=h+m+p+r+(A?""+df(6,1)+af(A):"")+(C?""+df(7,3)+af(C.length)+
C:"")+(E?""+df(8,3)+af(E.length)+E:"")+(G?""+df(9,3)+af(G.length)+G:""),N;var T=a.Tl;T=T===void 0?{}:T;for(var ca=[],P=l(Object.keys(T)),ha=P.next();!ha.done;ha=P.next()){var da=ha.value;ca[Number(da)]=T[da]}if(ca.length){var ka=df(10,3),X;if(ca.length===0)X=af(0);else{for(var W=[],ta=0,ra=!1,na=0;na<ca.length;na++){ra=!0;var Va=na%6;ca[na]&&(ta|=1<<Va);Va===5&&(W.push(af(ta)),ta=0,ra=!1)}ra&&W.push(af(ta));X=W.join("")}var Ya=X;N=""+ka+af(Ya.length)+Ya}else N="";var sb=a.qm,ac=a.qq;return I+N+(sb?
""+df(11,3)+af(sb.length)+sb:"")+(ac?""+df(13,3)+af(ac.length)+ac:"")};var ff=function(){function a(b){return{toString:function(){return b}}}return{Pm:a("consent"),Rj:a("convert_case_to"),Sj:a("convert_false_to"),Tj:a("convert_null_to"),Uj:a("convert_true_to"),Vj:a("convert_undefined_to"),Mq:a("debug_mode_metadata"),Ra:a("function"),zi:a("instance_name"),io:a("live_only"),jo:a("malware_disabled"),METADATA:a("metadata"),mo:a("original_activity_id"),hr:a("original_vendor_template_id"),gr:a("once_on_load"),lo:a("once_per_event"),rl:a("once_per_load"),jr:a("priority_override"),
mr:a("respected_consent_types"),Cl:a("setup_tags"),kh:a("tag_id"),Kl:a("teardown_tags")}}();var Cf;var Df=[],Ef=[],Ff=[],Gf=[],Hf=[],If,Kf,Lf;function Mf(a){Lf=Lf||a}
function Nf(){for(var a=data.resource||{},b=a.macros||[],c=0;c<b.length;c++)Df.push(b[c]);for(var d=a.tags||[],e=0;e<d.length;e++)Gf.push(d[e]);for(var f=a.predicates||[],g=0;g<f.length;g++)Ff.push(f[g]);for(var h=a.rules||[],m=0;m<h.length;m++){for(var n=h[m],p={},q=0;q<n.length;q++){var r=n[q][0];p[r]=Array.prototype.slice.call(n[q],1);r!=="if"&&r!=="unless"||Of(p[r])}Ef.push(p)}}
function Of(a){}var Pf,Qf=[],Rf=[];function Sf(a,b){var c={};c[ff.Ra]="__"+a;for(var d in b)b.hasOwnProperty(d)&&(c["vtp_"+d]=b[d]);return c}
function Tf(a,b,c){try{return Kf(Uf(a,b,c))}catch(d){JSON.stringify(a)}return 2}
var Uf=function(a,b,c){c=c||[];var d={},e;for(e in a)a.hasOwnProperty(e)&&(d[e]=Vf(a[e],b,c));return d},Vf=function(a,b,c){if(Array.isArray(a)){var d;switch(a[0]){case "function_id":return a[1];case "list":d=[];for(var e=1;e<a.length;e++)d.push(Vf(a[e],b,c));return d;case "macro":var f=a[1];if(c[f])return;var g=Df[f];if(!g||b.isBlocked(g))return;c[f]=!0;var h=String(g[ff.zi]);try{var m=Uf(g,b,c);m.vtp_gtmEventId=b.id;b.priorityId&&(m.vtp_gtmPriorityId=b.priorityId);d=Wf(m,{event:b,index:f,type:2,
name:h});Pf&&(d=Pf.No(d,m))}catch(A){b.logMacroError&&b.logMacroError(A,Number(f),h),d=!1}c[f]=!1;return d;case "map":d={};for(var n=1;n<a.length;n+=2)d[Vf(a[n],b,c)]=Vf(a[n+1],b,c);return d;case "template":d=[];for(var p=!1,q=1;q<a.length;q++){var r=Vf(a[q],b,c);Lf&&(p=p||Lf.Op(r));d.push(r)}return Lf&&p?Lf.So(d):d.join("");case "escape":d=Vf(a[1],b,c);if(Lf&&Array.isArray(a[1])&&a[1][0]==="macro"&&Lf.Pp(a))return Lf.fq(d);d=String(d);for(var t=2;t<a.length;t++)nf[a[t]]&&(d=nf[a[t]](d));return d;
case "tag":var u=a[1];if(!Gf[u])throw Error("Unable to resolve tag reference "+u+".");return{Xl:a[2],index:u};case "zb":var v={arg0:a[2],arg1:a[3],ignore_case:a[5]};v[ff.Ra]=a[1];var w=Tf(v,b,c),y=!!a[4];return y||w!==2?y!==(w===1):null;default:throw Error("Attempting to expand unknown Value type: "+a[0]+".");}}return a},Wf=function(a,b){var c=a[ff.Ra],d=b&&b.event;if(!c)throw Error("Error: No function name given for function call.");var e=If[c],f=b&&b.type===2&&(d==null?void 0:d.reportMacroDiscrepancy)&&
e&&Qf.indexOf(c)!==-1,g={},h={},m;for(m in a)a.hasOwnProperty(m)&&Eb(m,"vtp_")&&(e&&(g[m]=a[m]),!e||f)&&(h[m.substring(4)]=a[m]);e&&d&&d.cachedModelValues&&(g.vtp_gtmCachedValues=d.cachedModelValues);if(b){if(b.name==null){var n;a:{var p=b.type,q=b.index;if(q==null)n="";else{var r;switch(p){case 2:r=Df[q];break;case 1:r=Gf[q];break;default:n="";break a}var t=r&&r[ff.zi];n=t?String(t):""}}b.name=n}e&&(g.vtp_gtmEntityIndex=b.index,g.vtp_gtmEntityName=b.name)}var u,v,w;if(f&&Rf.indexOf(c)===-1){Rf.push(c);
var y=zb();u=e(g);var A=zb()-y,C=zb();v=Cf(c,h,b);w=A-(zb()-C)}else if(e&&(u=e(g)),!e||f)v=Cf(c,h,b);f&&d&&(d.reportMacroDiscrepancy(d.id,c,void 0,!0),md(u)?(Array.isArray(u)?Array.isArray(v):kd(u)?kd(v):typeof u==="function"?typeof v==="function":u===v)||d.reportMacroDiscrepancy(d.id,c):u!==v&&d.reportMacroDiscrepancy(d.id,c),w!==void 0&&d.reportMacroDiscrepancy(d.id,c,w));return e?u:v};var Xf=function(a,b,c){var d;d=Error.call(this,c);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.permissionId=a;this.parameters=b;this.name="PermissionError"};sa(Xf,Error);Xf.prototype.getMessage=function(){return this.message};function Yf(a,b){if(Array.isArray(a)){Object.defineProperty(a,"context",{value:{line:b[0]}});for(var c=1;c<a.length;c++)Yf(a[c],b[c])}};function Zf(){return function(a,b){var c;var d=$f;a instanceof Oa?(a.C=d,c=a):c=new Oa(a,d);var e=c;b&&e.debugInfo.push(b);throw e;}}function $f(a){if(!a.length)return a;a.push({id:"main",line:0});for(var b=a.length-1;b>0;b--)lb(a[b].id)&&a.splice(b++,1);for(var c=a.length-1;c>0;c--)a[c].line=a[c-1].line;a.splice(0,1);return a};function ag(a){function b(r){for(var t=0;t<r.length;t++)d[r[t]]=!0}for(var c=[],d=[],e=bg(a),f=0;f<Ef.length;f++){var g=Ef[f],h=cg(g,e);if(h){for(var m=g.add||[],n=0;n<m.length;n++)c[m[n]]=!0;b(g.block||[])}else h===null&&b(g.block||[]);}for(var p=[],q=0;q<Gf.length;q++)c[q]&&!d[q]&&(p[q]=!0);return p}
function cg(a,b){for(var c=a["if"]||[],d=0;d<c.length;d++){var e=b(c[d]);if(e===0)return!1;if(e===2)return null}for(var f=a.unless||[],g=0;g<f.length;g++){var h=b(f[g]);if(h===2)return null;if(h===1)return!1}return!0}function bg(a){var b=[];return function(c){b[c]===void 0&&(b[c]=Tf(Ff[c],a));return b[c]}};function dg(a,b){b[ff.Rj]&&typeof a==="string"&&(a=b[ff.Rj]===1?a.toLowerCase():a.toUpperCase());b.hasOwnProperty(ff.Tj)&&a===null&&(a=b[ff.Tj]);b.hasOwnProperty(ff.Vj)&&a===void 0&&(a=b[ff.Vj]);b.hasOwnProperty(ff.Uj)&&a===!0&&(a=b[ff.Uj]);b.hasOwnProperty(ff.Sj)&&a===!1&&(a=b[ff.Sj]);return a};var eg=function(){this.C={}},gg=function(a,b){var c=fg.C,d;(d=c.C)[a]!=null||(d[a]=[]);c.C[a].push(function(){return b.apply(null,va(za.apply(0,arguments)))})};function hg(a,b,c,d){if(a)for(var e=0;e<a.length;e++){var f=void 0,g="A policy function denied the permission request";try{f=a[e](b,c,d),g+="."}catch(h){g=typeof h==="string"?g+(": "+h):h instanceof Error?g+(": "+h.message):g+"."}if(!f)throw new Xf(c,d,g);}}
function ig(a,b,c){return function(d){if(d){var e=a.C[d],f=a.C.all;if(e||f){var g=c.apply(void 0,[d].concat(va(za.apply(1,arguments))));hg(e,b,d,g);hg(f,b,d,g)}}}};var mg=function(){var a=data.permissions||{},b=jg.ctid,c=this;this.H={};this.C=new eg;var d={},e={},f=ig(this.C,b,function(g){return g&&d[g]?d[g].apply(void 0,[g].concat(va(za.apply(1,arguments)))):{}});rb(a,function(g,h){function m(p){var q=za.apply(1,arguments);if(!n[p])throw kg(p,{},"The requested additional permission "+p+" is not configured.");f.apply(null,[p].concat(va(q)))}var n={};rb(h,function(p,q){var r=lg(p,q);n[p]=r.assert;d[p]||(d[p]=r.T);r.Pl&&!e[p]&&(e[p]=r.Pl)});c.H[g]=function(p,
q){var r=n[p];if(!r)throw kg(p,{},"The requested permission "+p+" is not configured.");var t=Array.prototype.slice.call(arguments,0);r.apply(void 0,t);f.apply(void 0,t);var u=e[p];u&&u.apply(null,[m].concat(va(t.slice(1))))}})},ng=function(a){return fg.H[a]||function(){}};
function lg(a,b){var c=Sf(a,b);c.vtp_permissionName=a;c.vtp_createPermissionError=kg;try{return Wf(c)}catch(d){return{assert:function(e){throw new Xf(e,{},"Permission "+e+" is unknown.");},T:function(){throw new Xf(a,{},"Permission "+a+" is unknown.");}}}}function kg(a,b,c){return new Xf(a,b,c)};var og=!1;var pg={};pg.Im=vb('');pg.cp=vb('');function ug(a,b){if(a==="")return b;var c=Number(a);return isNaN(c)?b:c};var vg=[];function wg(a){switch(a){case 1:return 0;case 216:return 19;case 38:return 12;case 53:return 1;case 54:return 2;case 52:return 6;case 215:return 18;case 211:return 17;case 75:return 3;case 103:return 13;case 197:return 14;case 203:return 15;case 114:return 11;case 116:return 4;case 209:return 16;case 217:return 20;case 135:return 8;case 136:return 5}}function xg(a,b){vg[a]=b;var c=wg(a);c!==void 0&&(Ia[c]=b)}function D(a){xg(a,!0)}D(39);
D(34);D(35);D(36);D(56);
D(145);D(153);
D(144);
D(120);D(5);D(111);
D(139);D(87);D(92);D(159);
D(132);D(20);D(72);
D(113);D(154);D(116);xg(23,!1),D(24);Ja[1]=ug('1',6E4);Ja[3]=ug('10',1);Ja[2]=ug('',50);D(29);
yg(26,25);
D(37);D(9);
D(91);D(123);
D(158);D(71);D(136);D(127);D(27);D(69);D(135);
D(95);D(38);D(103);D(112);
D(63);
D(152);
D(101);D(122);D(121);
D(134);
D(31);D(22);

D(19);

D(90);
D(59);
D(175);D(185);D(186);
D(192);D(198);
D(200);
D(207);function F(a){return!!vg[a]}function yg(a,b){for(var c=!1,d=!1,e=0;c===d;)if(c=((Math.random()*4294967296|0)&1)===0,d=((Math.random()*4294967296|0)&1)===0,e++,e>30)return;c?D(b):D(a)};var Ag={},Bg=(Ag.uaa=!0,Ag.uab=!0,Ag.uafvl=!0,Ag.uamb=!0,Ag.uam=!0,Ag.uap=!0,Ag.uapv=!0,Ag.uaw=!0,Ag);
var Jg=function(a,b){for(var c=0;c<b.length;c++){var d=a,e=b[c];if(!Hg.exec(e))throw Error("Invalid key wildcard");var f=e.indexOf(".*"),g=f!==-1&&f===e.length-2,h=g?e.slice(0,e.length-2):e,m;a:if(d.length===0)m=!1;else{for(var n=d.split("."),p=0;p<n.length;p++)if(!Ig.exec(n[p])){m=!1;break a}m=!0}if(!m||h.length>d.length||!g&&d.length!==e.length?0:g?Eb(d,h)&&(d===h||d.charAt(h.length)==="."):d===h)return!0}return!1},Ig=/^[a-z$_][\w-$]*$/i,Hg=/^(?:[a-z_$][a-z-_$0-9]*\.)*[a-z_$][a-z-_$0-9]*(?:\.\*)?$/i;
var Kg=["matches","webkitMatchesSelector","mozMatchesSelector","msMatchesSelector","oMatchesSelector"];function Lg(a,b){var c=String(a),d=String(b),e=c.length-d.length;return e>=0&&c.indexOf(d,e)===e}function Mg(a,b){return String(a).split(",").indexOf(String(b))>=0}var Ng=new qb;function Og(a,b,c){var d=c?"i":void 0;try{var e=String(b)+String(d),f=Ng.get(e);f||(f=new RegExp(b,d),Ng.set(e,f));return f.test(a)}catch(g){return!1}}function Pg(a,b){return String(a).indexOf(String(b))>=0}
function Qg(a,b){return String(a)===String(b)}function Rg(a,b){return Number(a)>=Number(b)}function Sg(a,b){return Number(a)<=Number(b)}function Tg(a,b){return Number(a)>Number(b)}function Ug(a,b){return Number(a)<Number(b)}function Vg(a,b){return Eb(String(a),String(b))};var bh=/^([a-z][a-z0-9]*):(!|\?)(\*|string|boolean|number|Fn|PixieMap|List|OpaqueValue)$/i,ch={Fn:"function",PixieMap:"Object",List:"Array"};
function dh(a,b){for(var c=["input:!*"],d=0;d<c.length;d++){var e=bh.exec(c[d]);if(!e)throw Error("Internal Error in "+a);var f=e[1],g=e[2]==="!",h=e[3],m=b[d];if(m==null){if(g)throw Error("Error in "+a+". Required argument "+f+" not supplied.");}else if(h!=="*"){var n=typeof m;m instanceof sd?n="Fn":m instanceof od?n="List":m instanceof Ua?n="PixieMap":m instanceof zd?n="PixiePromise":m instanceof xd&&(n="OpaqueValue");if(n!==h)throw Error("Error in "+a+". Argument "+f+" has type "+((ch[n]||n)+", which does not match required type ")+
((ch[h]||h)+"."));}}}function H(a,b,c){for(var d=[],e=l(c),f=e.next();!f.done;f=e.next()){var g=f.value;g instanceof sd?d.push("function"):g instanceof od?d.push("Array"):g instanceof Ua?d.push("Object"):g instanceof zd?d.push("Promise"):g instanceof xd?d.push("OpaqueValue"):d.push(typeof g)}return Error("Argument error in "+a+". Expected argument types ["+(b.join(",")+"], but received [")+(d.join(",")+"]."))}function eh(a){return a instanceof Ua}function fh(a){return eh(a)||a===null||gh(a)}
function hh(a){return a instanceof sd}function ih(a){return hh(a)||a===null||gh(a)}function jh(a){return a instanceof od}function kh(a){return a instanceof xd}function lh(a){return typeof a==="string"}function mh(a){return lh(a)||a===null||gh(a)}function nh(a){return typeof a==="boolean"}function oh(a){return nh(a)||gh(a)}function ph(a){return nh(a)||a===null||gh(a)}function qh(a){return typeof a==="number"}function gh(a){return a===void 0};function rh(a){return""+a}
function sh(a,b){var c=[];return c};function th(a,b){var c=new sd(a,function(){for(var d=Array.prototype.slice.call(arguments,0),e=0;e<d.length;e++)d[e]=this.evaluate(d[e]);try{return b.apply(this,d)}catch(g){throw Pa(g);}});c.Ua();return c}
function uh(a,b){var c=new Ua,d;for(d in b)if(b.hasOwnProperty(d)){var e=b[d];jb(e)?c.set(d,th(a+"_"+d,e)):kd(e)?c.set(d,uh(a+"_"+d,e)):(lb(e)||kb(e)||typeof e==="boolean")&&c.set(d,e)}c.Ua();return c};function vh(a,b){if(!lh(a))throw H(this.getName(),["string"],arguments);if(!mh(b))throw H(this.getName(),["string","undefined"],arguments);var c={},d=new Ua;return d=uh("AssertApiSubject",
c)};function wh(a,b){if(!mh(b))throw H(this.getName(),["string","undefined"],arguments);if(a instanceof zd)throw Error("Argument actual cannot have type Promise. Assertions on asynchronous code aren't supported.");var c={},d=new Ua;return d=uh("AssertThatSubject",c)};function xh(a){return function(){for(var b=za.apply(0,arguments),c=[],d=this.K,e=0;e<b.length;++e)c.push(Ad(b[e],d));return Bd(a.apply(null,c))}}function yh(){for(var a=Math,b=zh,c={},d=0;d<b.length;d++){var e=b[d];a.hasOwnProperty(e)&&(c[e]=xh(a[e].bind(a)))}return c};function Ah(a){return a!=null&&Eb(a,"__cvt_")};function Bh(a){var b;return b};function Ch(a){var b;return b};function Dh(a){try{return encodeURI(a)}catch(b){}};function Eh(a){try{return encodeURIComponent(String(a))}catch(b){}};function Jh(a){if(!mh(a))throw H(this.getName(),["string|undefined"],arguments);};function Kh(a,b){if(!qh(a)||!qh(b))throw H(this.getName(),["number","number"],arguments);return ob(a,b)};function Lh(){return(new Date).getTime()};function Mh(a){if(a===null)return"null";if(a instanceof od)return"array";if(a instanceof sd)return"function";if(a instanceof xd){var b=a.getValue();if((b==null?void 0:b.constructor)===void 0||b.constructor.name===void 0){var c=String(b);return c.substring(8,c.length-1)}return String(b.constructor.name)}return typeof a};function Nh(a){function b(c){return function(d){try{return c(d)}catch(e){(og||pg.Im)&&a.call(this,e.message)}}}return{parse:b(function(c){return Bd(JSON.parse(c))}),stringify:b(function(c){return JSON.stringify(Ad(c))}),publicName:"JSON"}};function Oh(a){return ub(Ad(a,this.K))};function Ph(a){return Number(Ad(a,this.K))};function Qh(a){return a===null?"null":a===void 0?"undefined":a.toString()};function Rh(a,b,c){var d=null,e=!1;return e?d:null};var zh="floor ceil round max min abs pow sqrt".split(" ");function Sh(){var a={};return{qp:function(b){return a.hasOwnProperty(b)?a[b]:void 0},Em:function(b,c){a[b]=c},reset:function(){a={}}}}function Th(a,b){return function(){return sd.prototype.invoke.apply(a,[b].concat(va(za.apply(0,arguments))))}}
function Uh(a,b){if(!lh(a))throw H(this.getName(),["string","any"],arguments);}
function Vh(a,b){if(!lh(a)||!eh(b))throw H(this.getName(),["string","PixieMap"],arguments);};var Wh={};var Xh=function(a){var b=new Ua;if(a instanceof od)for(var c=a.wa(),d=0;d<c.length;d++){var e=c[d];a.has(e)&&b.set(e,a.get(e))}else if(a instanceof sd)for(var f=a.wa(),g=0;g<f.length;g++){var h=f[g];b.set(h,a.get(h))}else for(var m=0;m<a.length;m++)b.set(m,a[m]);return b};
Wh.keys=function(a){dh(this.getName(),arguments);if(a instanceof od||a instanceof sd||typeof a==="string")a=Xh(a);if(a instanceof Ua||a instanceof zd)return new od(a.wa());return new od};
Wh.values=function(a){dh(this.getName(),arguments);if(a instanceof od||a instanceof sd||typeof a==="string")a=Xh(a);if(a instanceof Ua||a instanceof zd)return new od(a.Zb());return new od};
Wh.entries=function(a){dh(this.getName(),arguments);if(a instanceof od||a instanceof sd||typeof a==="string")a=Xh(a);if(a instanceof Ua||a instanceof zd)return new od(a.Ib().map(function(b){return new od(b)}));return new od};
Wh.freeze=function(a){(a instanceof Ua||a instanceof zd||a instanceof od||a instanceof sd)&&a.Ua();return a};Wh.delete=function(a,b){if(a instanceof Ua&&!a.tb())return a.remove(b),!0;return!1};function J(a,b){var c=za.apply(2,arguments),d=a.K.sb();if(!d)throw Error("Missing program state.");if(d.mq){try{d.Ql.apply(null,[b].concat(va(c)))}catch(e){throw db("TAGGING",21),e;}return}d.Ql.apply(null,[b].concat(va(c)))};var Yh=function(){this.H={};this.C={};this.N=!0;};Yh.prototype.get=function(a,b){var c=this.contains(a)?this.H[a]:void 0;return c};Yh.prototype.contains=function(a){return this.H.hasOwnProperty(a)};
Yh.prototype.add=function(a,b,c){if(this.contains(a))throw Error("Attempting to add a function which already exists: "+a+".");if(this.C.hasOwnProperty(a))throw Error("Attempting to add an API with an existing private API name: "+a+".");this.H[a]=c?void 0:jb(b)?th(a,b):uh(a,b)};function Zh(a,b){var c=void 0;return c};function $h(){var a={};return a};var K={m:{La:"ad_personalization",U:"ad_storage",V:"ad_user_data",ja:"analytics_storage",fc:"region",fa:"consent_updated",qg:"wait_for_update",Zm:"app_remove",bn:"app_store_refund",dn:"app_store_subscription_cancel",fn:"app_store_subscription_convert",gn:"app_store_subscription_renew",hn:"consent_update",Zj:"add_payment_info",bk:"add_shipping_info",Pd:"add_to_cart",Qd:"remove_from_cart",dk:"view_cart",Xc:"begin_checkout",Rd:"select_item",jc:"view_item_list",Gc:"select_promotion",kc:"view_promotion",
kb:"purchase",Sd:"refund",xb:"view_item",ek:"add_to_wishlist",jn:"exception",kn:"first_open",ln:"first_visit",qa:"gtag.config",Cb:"gtag.get",mn:"in_app_purchase",Yc:"page_view",nn:"screen_view",on:"session_start",pn:"source_update",qn:"timing_complete",rn:"track_social",Td:"user_engagement",sn:"user_id_update",Ke:"gclid_link_decoration_source",Le:"gclid_storage_source",mc:"gclgb",lb:"gclid",fk:"gclid_len",Ud:"gclgs",Vd:"gcllp",Wd:"gclst",za:"ads_data_redaction",Me:"gad_source",Ne:"gad_source_src",
Zc:"gclid_url",gk:"gclsrc",Oe:"gbraid",Xd:"wbraid",Ea:"allow_ad_personalization_signals",xg:"allow_custom_scripts",Pe:"allow_direct_google_requests",yg:"allow_display_features",zg:"allow_enhanced_conversions",Ob:"allow_google_signals",mb:"allow_interest_groups",tn:"app_id",un:"app_installer_id",vn:"app_name",wn:"app_version",Pb:"auid",xn:"auto_detection_enabled",bd:"aw_remarketing",Oh:"aw_remarketing_only",Ag:"discount",Bg:"aw_feed_country",Cg:"aw_feed_language",sa:"items",Dg:"aw_merchant_id",hk:"aw_basket_type",
Qe:"campaign_content",Re:"campaign_id",Se:"campaign_medium",Te:"campaign_name",Ue:"campaign",Ve:"campaign_source",We:"campaign_term",Qb:"client_id",ik:"rnd",Ph:"consent_update_type",yn:"content_group",zn:"content_type",Rb:"conversion_cookie_prefix",Xe:"conversion_id",Oa:"conversion_linker",Qh:"conversion_linker_disabled",dd:"conversion_api",Eg:"cookie_deprecation",nb:"cookie_domain",ob:"cookie_expires",yb:"cookie_flags",ed:"cookie_name",Sb:"cookie_path",cb:"cookie_prefix",Hc:"cookie_update",Yd:"country",
Va:"currency",Rh:"customer_buyer_stage",Ye:"customer_lifetime_value",Sh:"customer_loyalty",Th:"customer_ltv_bucket",Ze:"custom_map",Uh:"gcldc",fd:"dclid",jk:"debug_mode",oa:"developer_id",An:"disable_merchant_reported_purchases",gd:"dc_custom_params",Bn:"dc_natural_search",kk:"dynamic_event_settings",lk:"affiliation",Fg:"checkout_option",Vh:"checkout_step",mk:"coupon",af:"item_list_name",Wh:"list_name",Cn:"promotions",bf:"shipping",Xh:"tax",Gg:"engagement_time_msec",Hg:"enhanced_client_id",Yh:"enhanced_conversions",
nk:"enhanced_conversions_automatic_settings",Ig:"estimated_delivery_date",Zh:"euid_logged_in_state",cf:"event_callback",Dn:"event_category",Tb:"event_developer_id_string",En:"event_label",hd:"event",Jg:"event_settings",Kg:"event_timeout",Gn:"description",Hn:"fatal",In:"experiments",ai:"firebase_id",Zd:"first_party_collection",Lg:"_x_20",oc:"_x_19",pk:"fledge_drop_reason",qk:"fledge",rk:"flight_error_code",sk:"flight_error_message",tk:"fl_activity_category",uk:"fl_activity_group",bi:"fl_advertiser_id",
vk:"fl_ar_dedupe",df:"match_id",wk:"fl_random_number",xk:"tran",yk:"u",Mg:"gac_gclid",ae:"gac_wbraid",zk:"gac_wbraid_multiple_conversions",Ak:"ga_restrict_domain",di:"ga_temp_client_id",Jn:"ga_temp_ecid",jd:"gdpr_applies",Bk:"geo_granularity",Ic:"value_callback",qc:"value_key",rc:"google_analysis_params",be:"_google_ng",ce:"google_signals",Ck:"google_tld",ef:"gpp_sid",ff:"gpp_string",Ng:"groups",Dk:"gsa_experiment_id",hf:"gtag_event_feature_usage",Ek:"gtm_up",Jc:"iframe_state",jf:"ignore_referrer",
ei:"internal_traffic_results",Fk:"_is_fpm",Kc:"is_legacy_converted",Lc:"is_legacy_loaded",Og:"is_passthrough",kd:"_lps",zb:"language",Pg:"legacy_developer_id_string",Pa:"linker",de:"accept_incoming",sc:"decorate_forms",ma:"domains",Mc:"url_position",ee:"merchant_feed_label",fe:"merchant_feed_language",he:"merchant_id",Gk:"method",Kn:"name",Hk:"navigation_type",kf:"new_customer",Qg:"non_interaction",Ln:"optimize_id",Ik:"page_hostname",lf:"page_path",Wa:"page_referrer",Db:"page_title",Jk:"passengers",
Kk:"phone_conversion_callback",Mn:"phone_conversion_country_code",Lk:"phone_conversion_css_class",Nn:"phone_conversion_ids",Mk:"phone_conversion_number",Nk:"phone_conversion_options",On:"_platinum_request_status",Pn:"_protected_audience_enabled",nf:"quantity",Rg:"redact_device_info",fi:"referral_exclusion_definition",Pq:"_request_start_time",Vb:"restricted_data_processing",Qn:"retoken",Rn:"sample_rate",gi:"screen_name",Nc:"screen_resolution",Ok:"_script_source",Sn:"search_term",pb:"send_page_view",
ld:"send_to",md:"server_container_url",pf:"session_duration",Sg:"session_engaged",hi:"session_engaged_time",uc:"session_id",Tg:"session_number",qf:"_shared_user_id",rf:"delivery_postal_code",Qq:"_tag_firing_delay",Rq:"_tag_firing_time",Sq:"temporary_client_id",ii:"_timezone",ji:"topmost_url",Tn:"tracking_id",ki:"traffic_type",Xa:"transaction_id",vc:"transport_url",Pk:"trip_type",od:"update",Eb:"url_passthrough",Qk:"uptgs",tf:"_user_agent_architecture",uf:"_user_agent_bitness",vf:"_user_agent_full_version_list",
wf:"_user_agent_mobile",xf:"_user_agent_model",yf:"_user_agent_platform",zf:"_user_agent_platform_version",Af:"_user_agent_wow64",eb:"user_data",li:"user_data_auto_latency",mi:"user_data_auto_meta",ni:"user_data_auto_multi",oi:"user_data_auto_selectors",ri:"user_data_auto_status",wc:"user_data_mode",Ug:"user_data_settings",Qa:"user_id",Wb:"user_properties",Rk:"_user_region",Bf:"us_privacy_string",Fa:"value",Sk:"wbraid_multiple_conversions",rd:"_fpm_parameters",xi:"_host_name",bl:"_in_page_command",
fl:"_ip_override",ml:"_is_passthrough_cid",xc:"non_personalized_ads",Ii:"_sst_parameters",nc:"conversion_label",Aa:"page_location",Ub:"global_developer_id_string",nd:"tc_privacy_string"}};var ai={},bi=(ai[K.m.fa]="gcu",ai[K.m.mc]="gclgb",ai[K.m.lb]="gclaw",ai[K.m.fk]="gclid_len",ai[K.m.Ud]="gclgs",ai[K.m.Vd]="gcllp",ai[K.m.Wd]="gclst",ai[K.m.Pb]="auid",ai[K.m.Ag]="dscnt",ai[K.m.Bg]="fcntr",ai[K.m.Cg]="flng",ai[K.m.Dg]="mid",ai[K.m.hk]="bttype",ai[K.m.Qb]="gacid",ai[K.m.nc]="label",ai[K.m.dd]="capi",ai[K.m.Eg]="pscdl",ai[K.m.Va]="currency_code",ai[K.m.Rh]="clobs",ai[K.m.Ye]="vdltv",ai[K.m.Sh]="clolo",ai[K.m.Th]="clolb",ai[K.m.jk]="_dbg",ai[K.m.Ig]="oedeld",ai[K.m.Tb]="edid",ai[K.m.pk]=
"fdr",ai[K.m.qk]="fledge",ai[K.m.Mg]="gac",ai[K.m.ae]="gacgb",ai[K.m.zk]="gacmcov",ai[K.m.jd]="gdpr",ai[K.m.Ub]="gdid",ai[K.m.be]="_ng",ai[K.m.ef]="gpp_sid",ai[K.m.ff]="gpp",ai[K.m.Dk]="gsaexp",ai[K.m.hf]="_tu",ai[K.m.Jc]="frm",ai[K.m.Og]="gtm_up",ai[K.m.kd]="lps",ai[K.m.Pg]="did",ai[K.m.ee]="fcntr",ai[K.m.fe]="flng",ai[K.m.he]="mid",ai[K.m.kf]=void 0,ai[K.m.Db]="tiba",ai[K.m.Vb]="rdp",ai[K.m.uc]="ecsid",ai[K.m.qf]="ga_uid",ai[K.m.rf]="delopc",ai[K.m.nd]="gdpr_consent",ai[K.m.Xa]="oid",ai[K.m.Qk]=
"uptgs",ai[K.m.tf]="uaa",ai[K.m.uf]="uab",ai[K.m.vf]="uafvl",ai[K.m.wf]="uamb",ai[K.m.xf]="uam",ai[K.m.yf]="uap",ai[K.m.zf]="uapv",ai[K.m.Af]="uaw",ai[K.m.li]="ec_lat",ai[K.m.mi]="ec_meta",ai[K.m.ni]="ec_m",ai[K.m.oi]="ec_sel",ai[K.m.ri]="ec_s",ai[K.m.wc]="ec_mode",ai[K.m.Qa]="userId",ai[K.m.Bf]="us_privacy",ai[K.m.Fa]="value",ai[K.m.Sk]="mcov",ai[K.m.xi]="hn",ai[K.m.bl]="gtm_ee",ai[K.m.xc]="npa",ai[K.m.Xe]=null,ai[K.m.Nc]=null,ai[K.m.zb]=null,ai[K.m.sa]=null,ai[K.m.Aa]=null,ai[K.m.Wa]=null,ai[K.m.ji]=
null,ai[K.m.rd]=null,ai[K.m.Ke]=null,ai[K.m.Le]=null,ai[K.m.rc]=null,ai);function ci(a,b){if(a){var c=a.split("x");c.length===2&&(di(b,"u_w",c[0]),di(b,"u_h",c[1]))}}
function ei(a){var b=fi;b=b===void 0?gi:b;var c;var d=b;if(a&&a.length){for(var e=[],f=0;f<a.length;++f){var g=a[f];g&&e.push({item_id:d(g),quantity:g.quantity,value:g.price,start_date:g.start_date,end_date:g.end_date})}c=e}else c=[];var h;var m=c;if(m){for(var n=[],p=0;p<m.length;p++){var q=m[p],r=[];q&&(r.push(hi(q.value)),r.push(hi(q.quantity)),r.push(hi(q.item_id)),r.push(hi(q.start_date)),r.push(hi(q.end_date)),n.push("("+r.join("*")+")"))}h=n.length>0?n.join(""):""}else h="";return h}
function gi(a){return ii(a.item_id,a.id,a.item_name)}function ii(){for(var a=l(za.apply(0,arguments)),b=a.next();!b.done;b=a.next()){var c=b.value;if(c!==null&&c!==void 0)return c}}function ji(a){if(a&&a.length){for(var b=[],c=0;c<a.length;++c){var d=a[c];d&&d.estimated_delivery_date?b.push(""+d.estimated_delivery_date):b.push("")}return b.join(",")}}function di(a,b,c){c===void 0||c===null||c===""&&!Bg[b]||(a[b]=c)}function hi(a){return typeof a!=="number"&&typeof a!=="string"?"":a.toString()};var ki={},li=function(){for(var a=!1,b=!1,c=0;a===b;)if(a=ob(0,1)===0,b=ob(0,1)===0,c++,c>30)return;return a},ni={sq:mi};function oi(a,b){var c=ki[b],d=c.studyId,e=c.experimentId,f=c.probability;if(!(a.studies||{})[d]){var g=a.studies||{};g[d]=!0;a.studies=g;pi(b)?qi(a,e):f<=0||f>1||ni.sq(a,b)}}
function mi(a,b){var c=ki[b],d=c.controlId2;if(!(ob(0,9999)<c.probability*(c.controlId2&&c.probability<=.25?4:2)*1E4))return a;ri(a,{experimentId:c.experimentId,controlId:c.controlId,controlId2:c.controlId2&&c.probability<=.25?d:void 0,experimentCallback:function(){}});return a}function pi(a){return ki[a].active||ki[a].probability>.5}function qi(a,b){var c=a.exp||{};c[b]=!0;a.exp=c}
function ri(a,b){var c=b.experimentId,d=b.controlId,e=b.controlId2,f=b.experimentCallback;if((a.exp||{})[c])f();else if(!((a.exp||{})[d]||e&&(a.exp||{})[e])){var g=li()?0:1;e&&(g|=(li()?0:1)<<1);g===0?(qi(a,c),f()):g===1?qi(a,d):g===2&&qi(a,e)}};var L={J:{Lj:"call_conversion",W:"conversion",Un:"floodlight",Df:"ga_conversion",Ei:"landing_page",Ga:"page_view",na:"remarketing",Ta:"user_data_lead",Ja:"user_data_web"}};function ui(a){return vi?z.querySelectorAll(a):null}
function wi(a,b){if(!vi)return null;if(Element.prototype.closest)try{return a.closest(b)}catch(e){return null}var c=Element.prototype.matches||Element.prototype.webkitMatchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector||Element.prototype.oMatchesSelector,d=a;if(!z.documentElement.contains(d))return null;do{try{if(c.call(d,b))return d}catch(e){break}d=d.parentElement||d.parentNode}while(d!==null&&d.nodeType===1);return null}var xi=!1;
if(z.querySelectorAll)try{var yi=z.querySelectorAll(":root");yi&&yi.length==1&&yi[0]==z.documentElement&&(xi=!0)}catch(a){}var vi=xi;function zi(a){switch(a){case 0:break;case 9:return"e4";case 6:return"e5";case 14:return"e6";default:return"e7"}};function Ai(){this.blockSize=-1};function Bi(a,b){this.blockSize=-1;this.blockSize=64;this.N=Aa.Uint8Array?new Uint8Array(this.blockSize):Array(this.blockSize);this.P=this.H=0;this.C=[];this.ba=a;this.R=b;this.ka=Aa.Int32Array?new Int32Array(64):Array(64);Ci===void 0&&(Aa.Int32Array?Ci=new Int32Array(Di):Ci=Di);this.reset()}Ba(Bi,Ai);for(var Ei=[],Fi=0;Fi<63;Fi++)Ei[Fi]=0;var Gi=[].concat(128,Ei);
Bi.prototype.reset=function(){this.P=this.H=0;var a;if(Aa.Int32Array)a=new Int32Array(this.R);else{var b=this.R,c=b.length;if(c>0){for(var d=Array(c),e=0;e<c;e++)d[e]=b[e];a=d}else a=[]}this.C=a};
var Hi=function(a){for(var b=a.N,c=a.ka,d=0,e=0;e<b.length;)c[d++]=b[e]<<24|b[e+1]<<16|b[e+2]<<8|b[e+3],e=d*4;for(var f=16;f<64;f++){var g=c[f-15]|0,h=c[f-2]|0;c[f]=((c[f-16]|0)+((g>>>7|g<<25)^(g>>>18|g<<14)^g>>>3)|0)+((c[f-7]|0)+((h>>>17|h<<15)^(h>>>19|h<<13)^h>>>10)|0)|0}for(var m=a.C[0]|0,n=a.C[1]|0,p=a.C[2]|0,q=a.C[3]|0,r=a.C[4]|0,t=a.C[5]|0,u=a.C[6]|0,v=a.C[7]|0,w=0;w<64;w++){var y=((m>>>2|m<<30)^(m>>>13|m<<19)^(m>>>22|m<<10))+(m&n^m&p^n&p)|0,A=(v+((r>>>6|r<<26)^(r>>>11|r<<21)^(r>>>25|r<<7))|
0)+(((r&t^~r&u)+(Ci[w]|0)|0)+(c[w]|0)|0)|0;v=u;u=t;t=r;r=q+A|0;q=p;p=n;n=m;m=A+y|0}a.C[0]=a.C[0]+m|0;a.C[1]=a.C[1]+n|0;a.C[2]=a.C[2]+p|0;a.C[3]=a.C[3]+q|0;a.C[4]=a.C[4]+r|0;a.C[5]=a.C[5]+t|0;a.C[6]=a.C[6]+u|0;a.C[7]=a.C[7]+v|0};
Bi.prototype.update=function(a,b){b===void 0&&(b=a.length);var c=0,d=this.H;if(typeof a==="string")for(;c<b;)this.N[d++]=a.charCodeAt(c++),d==this.blockSize&&(Hi(this),d=0);else{var e,f=typeof a;e=f!="object"?f:a?Array.isArray(a)?"array":f:"null";if(e=="array"||e=="object"&&typeof a.length=="number")for(;c<b;){var g=a[c++];if(!("number"==typeof g&&0<=g&&255>=g&&g==(g|0)))throw Error("message must be a byte array");this.N[d++]=g;d==this.blockSize&&(Hi(this),d=0)}else throw Error("message must be string or array");
}this.H=d;this.P+=b};Bi.prototype.digest=function(){var a=[],b=this.P*8;this.H<56?this.update(Gi,56-this.H):this.update(Gi,this.blockSize-(this.H-56));for(var c=63;c>=56;c--)this.N[c]=b&255,b/=256;Hi(this);for(var d=0,e=0;e<this.ba;e++)for(var f=24;f>=0;f-=8)a[d++]=this.C[e]>>f&255;return a};
var Di=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,
4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],Ci;function Ii(){Bi.call(this,8,Ji)}Ba(Ii,Bi);var Ji=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225];var Ki=/^[0-9A-Fa-f]{64}$/;function Li(a){try{return(new TextEncoder).encode(a)}catch(e){for(var b=[],c=0;c<a.length;c++){var d=a.charCodeAt(c);d<128?b.push(d):d<2048?b.push(192|d>>6,128|d&63):d<55296||d>=57344?b.push(224|d>>12,128|d>>6&63,128|d&63):(d=65536+((d&1023)<<10|a.charCodeAt(++c)&1023),b.push(240|d>>18,128|d>>12&63,128|d>>6&63,128|d&63))}return new Uint8Array(b)}}
function Mi(a){var b=x;if(a===""||a==="e0")return Promise.resolve(a);var c;if((c=b.crypto)==null?0:c.subtle){if(Ki.test(a))return Promise.resolve(a);try{var d=Li(a);return b.crypto.subtle.digest("SHA-256",d).then(function(e){return Ni(e,b)}).catch(function(){return"e2"})}catch(e){return Promise.resolve("e2")}}else return Promise.resolve("e1")}
function Ni(a,b){var c=Array.from(new Uint8Array(a)).map(function(d){return String.fromCharCode(d)}).join("");return b.btoa(c).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")};var Oi=[],Pi;function Qi(a){Pi?Pi(a):Oi.push(a)}function Ri(a,b){if(!F(190))return b;var c,d=!1;d=d===void 0?!1:d;var e,f;c=((e=data)==null?0:(f=e.blob)==null?0:f.hasOwnProperty(a))?!!data.blob[a]:d;return c!==b?(Qi(a),b):c}function Si(a,b){if(!F(190))return b;var c=Ti(a,"");return c!==b?(Qi(a),b):c}function Ti(a,b){b=b===void 0?"":b;var c,d;return((c=data)==null?0:(d=c.blob)==null?0:d.hasOwnProperty(a))?String(data.blob[a]):b}
function Ui(a,b){if(!F(190))return b;var c,d,e;c=((d=data)==null?0:(e=d.blob)==null?0:e.hasOwnProperty(a))?Number(data.blob[a]):0;return c===b||isNaN(c)&&isNaN(b)?c:(Qi(a),b)}function Vi(){Pi=Wi;for(var a=l(Oi),b=a.next();!b.done;b=a.next())Pi(b.value);Oi.length=0};var Xi={Wm:'512',Xm:'0',Ym:'1000',Yn:'US-CO~US-CT~US-MT~US-NE~US-NH~US-TX~US-MN~US-NJ~US-MD',Zn:'US-CO~US-CT~US-MT~US-NE~US-NH~US-TX~US-MN~US-NJ~US-MD',vo:Si(44,'101509157~103116026~103200004~103233427~103351869~103351871~104684208~104684211~104909302~104909304~104935091~104935093')},Yi={Yo:Number(Xi.Wm)||0,Zo:Number(Xi.Xm)||0,bp:Number(Xi.Ym)||0,vp:Xi.Yn.split("~"),wp:Xi.Zn.split("~"),Jq:Xi.vo};Object.assign({},Yi);function M(a){db("GTM",a)};var Jj={},Kj=(Jj[K.m.mb]=1,Jj[K.m.md]=2,Jj[K.m.vc]=2,Jj[K.m.za]=3,Jj[K.m.Ye]=4,Jj[K.m.xg]=5,Jj[K.m.Hc]=6,Jj[K.m.cb]=6,Jj[K.m.nb]=6,Jj[K.m.ed]=6,Jj[K.m.Sb]=6,Jj[K.m.yb]=6,Jj[K.m.ob]=7,Jj[K.m.Vb]=9,Jj[K.m.yg]=10,Jj[K.m.Ob]=11,Jj),Lj={},Mj=(Lj.unknown=13,Lj.standard=14,Lj.unique=15,Lj.per_session=16,Lj.transactions=17,Lj.items_sold=18,Lj);var fb=[];function Nj(a,b){b=b===void 0?!1:b;for(var c=Object.keys(a),d=l(Object.keys(Kj)),e=d.next();!e.done;e=d.next()){var f=e.value;if(c.includes(f)){var g=Kj[f],h=b;h=h===void 0?!1:h;db("GTAG_EVENT_FEATURE_CHANNEL",g);h&&(fb[g]=!0)}}};var Oj=function(){this.C=new Set;this.H=new Set},Qj=function(a){var b=Pj.R;a=a===void 0?[]:a;var c=[].concat(va(b.C)).concat([].concat(va(b.H))).concat(a);c.sort(function(d,e){return d-e});return c},Rj=function(){var a=[].concat(va(Pj.R.C));a.sort(function(b,c){return b-c});return a},Sj=function(){var a=Pj.R,b=Yi.Jq;a.C=new Set;if(b!=="")for(var c=l(b.split("~")),d=c.next();!d.done;d=c.next()){var e=Number(d.value);isNaN(e)||a.C.add(e)}};var Tj={},Uj=Si(14,"5790"),Vj=Ui(15,Number("2")),Wj=Si(19,"dataLayer");Si(20,"");Si(16,"ChAI8Mm9wwYQ/obQruSG6Z55EicAAjDAIIksHIo76OQbSetOxphOKmDgQ15NAY+pyB1j9FcEvFBOZCIaAqYS");var Xj={__cl:1,__ecl:1,__ehl:1,__evl:1,__fal:1,__fil:1,__fsl:1,__hl:1,__jel:1,__lcl:1,__sdl:1,__tl:1,__ytl:1},Yj={__paused:1,__tg:1},Zj;for(Zj in Xj)Xj.hasOwnProperty(Zj)&&(Yj[Zj]=1);var ak=Ri(11,vb("")),bk=!1;
function ck(){var a=!1;a=!0;return a}var dk=F(218)?Ri(45,ck()):ck(),ek,fk=!1;ek=fk;Tj.vg=Si(3,"www.googletagmanager.com");var gk=""+Tj.vg+(dk?"/gtag/js":"/gtm.js"),hk=null,ik=null,jk={},kk={};Tj.Qm=Ri(2,vb(""));var lk="";
Tj.Ji=lk;var Pj=new function(){this.R=new Oj;this.C=this.N=!1;this.H=0;this.Ba=this.Sa=this.qb=this.P="";this.ba=this.ka=!1};function mk(){var a;a=a===void 0?[]:a;return Qj(a).join("~")}function nk(){var a=Pj.P.length;return Pj.P[a-1]==="/"?Pj.P.substring(0,a-1):Pj.P}function ok(){return Pj.C?F(84)?Pj.H===0:Pj.H!==1:!1}function pk(a){for(var b={},c=l(a.split("|")),d=c.next();!d.done;d=c.next())b[d.value]=!0;return b};var qk=new qb,rk={},sk={},vk={name:Wj,set:function(a,b){ld(Gb(a,b),rk);tk()},get:function(a){return uk(a,2)},reset:function(){qk=new qb;rk={};tk()}};function uk(a,b){return b!=2?qk.get(a):wk(a)}function wk(a,b){var c=a.split(".");b=b||[];for(var d=rk,e=0;e<c.length;e++){if(d===null)return!1;if(d===void 0)break;d=d[c[e]];if(b.indexOf(d)!==-1)return}return d}function xk(a,b){sk.hasOwnProperty(a)||(qk.set(a,b),ld(Gb(a,b),rk),tk())}
function yk(){for(var a=["gtm.allowlist","gtm.blocklist","gtm.whitelist","gtm.blacklist","tagTypeBlacklist"],b=0;b<a.length;b++){var c=a[b],d=uk(c,1);if(Array.isArray(d)||kd(d))d=ld(d,null);sk[c]=d}}function tk(a){rb(sk,function(b,c){qk.set(b,c);ld(Gb(b),rk);ld(Gb(b,c),rk);a&&delete sk[b]})}function zk(a,b){var c,d=(b===void 0?2:b)!==1?wk(a):qk.get(a);id(d)==="array"||id(d)==="object"?c=ld(d,null):c=d;return c};var Jk=/:[0-9]+$/,Kk=/^\d+\.fls\.doubleclick\.net$/;function Lk(a,b,c,d){var e=Mk(a,!!d,b),f,g;return c?(g=e[b])!=null?g:[]:(f=e[b])==null?void 0:f[0]}function Mk(a,b,c){for(var d={},e=l(a.split("&")),f=e.next();!f.done;f=e.next()){var g=l(f.value.split("=")),h=g.next().value,m=ua(g),n=decodeURIComponent(h.replace(/\+/g," "));if(c===void 0||n===c){var p=m.join("=");d[n]||(d[n]=[]);d[n].push(b?p:decodeURIComponent(p.replace(/\+/g," ")))}}return d}
function Nk(a){try{return decodeURIComponent(a)}catch(b){}}function Ok(a,b,c,d,e){b&&(b=String(b).toLowerCase());if(b==="protocol"||b==="port")a.protocol=Pk(a.protocol)||Pk(x.location.protocol);b==="port"?a.port=String(Number(a.hostname?a.port:x.location.port)||(a.protocol==="http"?80:a.protocol==="https"?443:"")):b==="host"&&(a.hostname=(a.hostname||x.location.hostname).replace(Jk,"").toLowerCase());return Qk(a,b,c,d,e)}
function Qk(a,b,c,d,e){var f,g=Pk(a.protocol);b&&(b=String(b).toLowerCase());switch(b){case "url_no_fragment":f=Rk(a);break;case "protocol":f=g;break;case "host":f=a.hostname.replace(Jk,"").toLowerCase();if(c){var h=/^www\d*\./.exec(f);h&&h[0]&&(f=f.substring(h[0].length))}break;case "port":f=String(Number(a.port)||(g==="http"?80:g==="https"?443:""));break;case "path":a.pathname||a.hostname||db("TAGGING",1);f=a.pathname.substring(0,1)==="/"?a.pathname:"/"+a.pathname;var m=f.split("/");(d||[]).indexOf(m[m.length-
1])>=0&&(m[m.length-1]="");f=m.join("/");break;case "query":f=a.search.replace("?","");e&&(f=Lk(f,e,!1));break;case "extension":var n=a.pathname.split(".");f=n.length>1?n[n.length-1]:"";f=f.split("/")[0];break;case "fragment":f=a.hash.replace("#","");break;default:f=a&&a.href}return f}function Pk(a){return a?a.replace(":","").toLowerCase():""}function Rk(a){var b="";if(a&&a.href){var c=a.href.indexOf("#");b=c<0?a.href:a.href.substring(0,c)}return b}var Sk={},Tk=0;
function Uk(a){var b=Sk[a];if(!b){var c=z.createElement("a");a&&(c.href=a);var d=c.pathname;d[0]!=="/"&&(a||db("TAGGING",1),d="/"+d);var e=c.hostname.replace(Jk,"");b={href:c.href,protocol:c.protocol,host:c.host,hostname:e,pathname:d,search:c.search,hash:c.hash,port:c.port};Tk<5&&(Sk[a]=b,Tk++)}return b}function Vk(a,b,c){var d=Uk(a);return Mb(b,d,c)}
function Wk(a){var b=Uk(x.location.href),c=Ok(b,"host",!1);if(c&&c.match(Kk)){var d=Ok(b,"path");if(d){var e=d.split(a+"=");if(e.length>1)return e[1].split(";")[0].split("?")[0]}}};var Xk={"https://www.google.com":"/g","https://www.googleadservices.com":"/as","https://pagead2.googlesyndication.com":"/gs"},Yk=["/as/d/ccm/conversion","/g/d/ccm/conversion","/gs/ccm/conversion","/d/ccm/form-data"];function Zk(a,b){if(a){var c=""+a;c.indexOf("http://")!==0&&c.indexOf("https://")!==0&&(c="https://"+c);c[c.length-1]==="/"&&(c=c.substring(0,c.length-1));return Uk(""+c+b).href}}function $k(a,b){if(ok()||Pj.N)return Zk(a,b)}
function al(){return!!Tj.Ji&&Tj.Ji.split("@@").join("")!=="SGTM_TOKEN"}function bl(a){for(var b=l([K.m.md,K.m.vc]),c=b.next();!c.done;c=b.next()){var d=O(a,c.value);if(d)return d}}function cl(a,b,c){c=c===void 0?"":c;if(!ok())return a;var d=b?Xk[a]||"":"";d==="/gs"&&(c="");return""+nk()+d+c}function dl(a){if(!ok())return a;for(var b=l(Yk),c=b.next();!c.done;c=b.next())if(Eb(a,""+nk()+c.value))return a+"&_uip="+encodeURIComponent("::");return a};function el(a){var b=String(a[ff.Ra]||"").replace(/_/g,"");return Eb(b,"cvt")?"cvt":b}var fl=x.location.search.indexOf("?gtm_latency=")>=0||x.location.search.indexOf("&gtm_latency=")>=0;var gl={nq:Ui(27,Number("0.005000")),Wo:Ui(42,Number("0.010000"))},hl=Math.random(),il=fl||hl<Number(gl.nq),jl=fl||hl>=1-Number(gl.Wo);var kl=function(a,b){var c=function(){};c.prototype=a.prototype;var d=new c;a.apply(d,Array.prototype.slice.call(arguments,1));return d},ll=function(a){var b=a;return function(){if(b){var c=b;b=null;c()}}};var ml,nl;a:{for(var ol=["CLOSURE_FLAGS"],pl=Aa,ql=0;ql<ol.length;ql++)if(pl=pl[ol[ql]],pl==null){nl=null;break a}nl=pl}var rl=nl&&nl[610401301];ml=rl!=null?rl:!1;function sl(){var a=Aa.navigator;if(a){var b=a.userAgent;if(b)return b}return""}var tl,ul=Aa.navigator;tl=ul?ul.userAgentData||null:null;function vl(a){if(!ml||!tl)return!1;for(var b=0;b<tl.brands.length;b++){var c=tl.brands[b].brand;if(c&&c.indexOf(a)!=-1)return!0}return!1}function wl(a){return sl().indexOf(a)!=-1};function xl(){return ml?!!tl&&tl.brands.length>0:!1}function yl(){return xl()?!1:wl("Opera")}function zl(){return wl("Firefox")||wl("FxiOS")}function Al(){return xl()?vl("Chromium"):(wl("Chrome")||wl("CriOS"))&&!(xl()?0:wl("Edge"))||wl("Silk")};var Bl=function(a){Bl[" "](a);return a};Bl[" "]=function(){};var Cl=function(a){return decodeURIComponent(a.replace(/\+/g," "))};function Dl(){return ml?!!tl&&!!tl.platform:!1}function El(){return wl("iPhone")&&!wl("iPod")&&!wl("iPad")}function Fl(){El()||wl("iPad")||wl("iPod")};yl();xl()||wl("Trident")||wl("MSIE");wl("Edge");!wl("Gecko")||sl().toLowerCase().indexOf("webkit")!=-1&&!wl("Edge")||wl("Trident")||wl("MSIE")||wl("Edge");sl().toLowerCase().indexOf("webkit")!=-1&&!wl("Edge")&&wl("Mobile");Dl()||wl("Macintosh");Dl()||wl("Windows");(Dl()?tl.platform==="Linux":wl("Linux"))||Dl()||wl("CrOS");Dl()||wl("Android");El();wl("iPad");wl("iPod");Fl();sl().toLowerCase().indexOf("kaios");var Gl=function(a){try{var b;if(b=!!a&&a.location.href!=null)a:{try{Bl(a.foo);b=!0;break a}catch(c){}b=!1}return b}catch(c){return!1}},Hl=function(a,b){if(a)for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)},Il=function(a,b){for(var c=a,d=0;d<50;++d){var e;try{e=!(!c.frames||!c.frames[b])}catch(h){e=!1}if(e)return c;var f;a:{try{var g=c.parent;if(g&&g!=c){f=g;break a}}catch(h){}f=null}if(!(c=f))break}return null},Jl=function(a){var b=x;if(b.top==b)return 0;if(a===void 0?0:a){var c=
b.location.ancestorOrigins;if(c)return c[c.length-1]==b.location.origin?1:2}return Gl(b.top)?1:2},Kl=function(a){a=a===void 0?document:a;return a.createElement("img")},Ll=function(){for(var a=x,b=a;a&&a!=a.parent;)a=a.parent,Gl(a)&&(b=a);return b};function Ml(a){var b;b=b===void 0?document:b;var c;return!((c=b.featurePolicy)==null||!c.allowedFeatures().includes(a))};function Nl(){return Ml("join-ad-interest-group")&&jb(tc.joinAdInterestGroup)}
function Ol(a,b,c){var d=Ja[3]===void 0?1:Ja[3],e='iframe[data-tagging-id="'+b+'"]',f=[];try{if(d===1){var g=z.querySelector(e);g&&(f=[g])}else f=Array.from(z.querySelectorAll(e))}catch(r){}var h;a:{try{h=z.querySelectorAll('iframe[allow="join-ad-interest-group"][data-tagging-id*="-"]');break a}catch(r){}h=void 0}var m=h,n=((m==null?void 0:m.length)||0)>=(Ja[2]===void 0?50:Ja[2]),p;if(p=f.length>=1){var q=Number(f[f.length-1].dataset.loadTime);q!==void 0&&zb()-q<(Ja[1]===void 0?6E4:Ja[1])?(db("TAGGING",
9),p=!0):p=!1}if(p)return!1;if(d===1)if(f.length>=1)Pl(f[0]);else{if(n)return db("TAGGING",10),!1}else f.length>=d?Pl(f[0]):n&&Pl(m[0]);Hc(a,c,{allow:"join-ad-interest-group"},{taggingId:b,loadTime:zb()});return!0}function Pl(a){try{a.parentNode.removeChild(a)}catch(b){}};function Ql(a,b,c){var d,e=a.GooglebQhCsO;e||(e={},a.GooglebQhCsO=e);d=e;if(d[b])return!1;d[b]=[];d[b][0]=c;return!0};var Rl=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e<128?b[c++]=e:(e<2048?b[c++]=e>>6|192:((e&64512)==55296&&d+1<a.length&&(a.charCodeAt(d+1)&64512)==56320?(e=65536+((e&1023)<<10)+(a.charCodeAt(++d)&1023),b[c++]=e>>18|240,b[c++]=e>>12&63|128):b[c++]=e>>12|224,b[c++]=e>>6&63|128),b[c++]=e&63|128)}return b};zl();El()||wl("iPod");wl("iPad");!wl("Android")||Al()||zl()||yl()||wl("Silk");Al();!wl("Safari")||Al()||(xl()?0:wl("Coast"))||yl()||(xl()?0:wl("Edge"))||(xl()?vl("Microsoft Edge"):wl("Edg/"))||(xl()?vl("Opera"):wl("OPR"))||zl()||wl("Silk")||wl("Android")||Fl();var Sl={},Tl=null,Ul=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e>255&&(b[c++]=e&255,e>>=8);b[c++]=e}var f=4;f===void 0&&(f=0);if(!Tl){Tl={};for(var g="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),h=["+/=","+/","-_=","-_.","-_"],m=0;m<5;m++){var n=g.concat(h[m].split(""));Sl[m]=n;for(var p=0;p<n.length;p++){var q=n[p];Tl[q]===void 0&&(Tl[q]=p)}}}for(var r=Sl[f],t=Array(Math.floor(b.length/3)),u=r[64]||"",v=0,w=0;v<b.length-2;v+=3){var y=b[v],
A=b[v+1],C=b[v+2],E=r[y>>2],G=r[(y&3)<<4|A>>4],I=r[(A&15)<<2|C>>6],N=r[C&63];t[w++]=""+E+G+I+N}var T=0,ca=u;switch(b.length-v){case 2:T=b[v+1],ca=r[(T&15)<<2]||u;case 1:var P=b[v];t[w]=""+r[P>>2]+r[(P&3)<<4|T>>4]+ca+u}return t.join("")};var Vl=function(a,b,c,d){for(var e=b,f=c.length;(e=a.indexOf(c,e))>=0&&e<d;){var g=a.charCodeAt(e-1);if(g==38||g==63){var h=a.charCodeAt(e+f);if(!h||h==61||h==38||h==35)return e}e+=f+1}return-1},Wl=/#|$/,Xl=function(a,b){var c=a.search(Wl),d=Vl(a,0,b,c);if(d<0)return null;var e=a.indexOf("&",d);if(e<0||e>c)e=c;d+=b.length+1;return Cl(a.slice(d,e!==-1?e:0))},Yl=/[?&]($|#)/,Zl=function(a,b,c){for(var d,e=a.search(Wl),f=0,g,h=[];(g=Vl(a,f,b,e))>=0;)h.push(a.substring(f,g)),f=Math.min(a.indexOf("&",g)+
1||e,e);h.push(a.slice(f));d=h.join("").replace(Yl,"$1");var m,n=c!=null?"="+encodeURIComponent(String(c)):"";var p=b+n;if(p){var q,r=d.indexOf("#");r<0&&(r=d.length);var t=d.indexOf("?"),u;t<0||t>r?(t=r,u=""):u=d.substring(t+1,r);q=[d.slice(0,t),u,d.slice(r)];var v=q[1];q[1]=p?v?v+"&"+p:p:v;m=q[0]+(q[1]?"?"+q[1]:"")+q[2]}else m=d;return m};function $l(a,b,c,d,e,f,g){var h=Xl(c,"fmt");if(d){var m=Xl(c,"random"),n=Xl(c,"label")||"";if(!m)return!1;var p=Ul(Cl(n)+":"+Cl(m));if(!Ql(a,p,d))return!1}h&&Number(h)!==4&&(c=Zl(c,"rfmt",h));var q=Zl(c,"fmt",4),r=b.getElementsByTagName("script")[0].parentElement;g==null||g.H();Fc(q,function(){g==null||g.C();a.google_noFurtherRedirects&&d&&(a.google_noFurtherRedirects=null,d())},function(){g==null||g.C();e==null||e()},f,r||void 0);return!0};var am={},bm=(am[1]={},am[2]={},am[3]={},am[4]={},am);function cm(a,b,c){var d=dm(b,c);if(d){var e=bm[b][d];e||(e=bm[b][d]=[]);e.push(Object.assign({},a))}}function em(a,b){var c=dm(a,b);if(c){var d=bm[a][c];d&&(bm[a][c]=d.filter(function(e){return!e.Am}))}}function fm(a){switch(a){case "script-src":case "script-src-elem":return 1;case "frame-src":return 4;case "connect-src":return 2;case "img-src":return 3}}
function dm(a,b){var c=b;if(b[0]==="/"){var d;c=((d=x.location)==null?void 0:d.origin)+b}try{var e=new URL(c);return a===4?e.origin:e.origin+e.pathname}catch(f){}}function gm(a){var b=za.apply(1,arguments);jl&&(cm(a,2,b[0]),cm(a,3,b[0]));Qc.apply(null,va(b))}function hm(a){var b=za.apply(1,arguments);jl&&cm(a,2,b[0]);return Rc.apply(null,va(b))}function im(a){var b=za.apply(1,arguments);jl&&cm(a,3,b[0]);Ic.apply(null,va(b))}
function jm(a){var b=za.apply(1,arguments),c=b[0];jl&&(cm(a,2,c),cm(a,3,c));return Uc.apply(null,va(b))}function km(a){var b=za.apply(1,arguments);jl&&cm(a,1,b[0]);Fc.apply(null,va(b))}function lm(a){var b=za.apply(1,arguments);b[0]&&jl&&cm(a,4,b[0]);Hc.apply(null,va(b))}function mm(a){var b=za.apply(1,arguments);jl&&cm(a,1,b[2]);return $l.apply(null,va(b))}function nm(a){var b=za.apply(1,arguments);jl&&cm(a,4,b[0]);Ol.apply(null,va(b))};var om=/gtag[.\/]js/,pm=/gtm[.\/]js/,qm=!1;function rm(a){if(qm)return"1";var b,c=(b=a.scriptElement)==null?void 0:b.src;if(c){if(om.test(c))return"3";if(pm.test(c))return"2"}return"0"};function sm(a,b,c){var d=tm(),e=um().container[a];e&&e.state!==3||(um().container[a]={state:1,context:b,parent:d},vm({ctid:a,isDestination:!1},c))}function vm(a,b){var c=um();c.pending||(c.pending=[]);nb(c.pending,function(d){return d.target.ctid===a.ctid&&d.target.isDestination===a.isDestination})||c.pending.push({target:a,onLoad:b})}function wm(){var a=x.google_tags_first_party;Array.isArray(a)||(a=[]);for(var b={},c=l(a),d=c.next();!d.done;d=c.next())b[d.value]=!0;return Object.freeze(b)}
var xm=function(){this.container={};this.destination={};this.canonical={};this.pending=[];this.injectedFirstPartyContainers={};this.injectedFirstPartyContainers=wm()};function um(){var a=xc("google_tag_data",{}),b=a.tidr;b&&typeof b==="object"||(b=new xm,a.tidr=b);var c=b;c.container||(c.container={});c.destination||(c.destination={});c.canonical||(c.canonical={});c.pending||(c.pending=[]);c.injectedFirstPartyContainers||(c.injectedFirstPartyContainers=wm());return c};var ym={},jg={ctid:Si(5,"UA-180993812-1"),canonicalContainerId:Si(6,""),sm:Si(10,"UA-180993812-1"),tm:Si(9,"UA-180993812-1")};ym.pe=Ri(7,vb(""));function zm(){return ym.pe&&Am().some(function(a){return a===jg.ctid})}function Bm(){return jg.canonicalContainerId||"_"+jg.ctid}function Cm(){return jg.sm?jg.sm.split("|"):[jg.ctid]}
function Am(){return jg.tm?jg.tm.split("|").filter(function(a){return a.indexOf("GTM-")!==0}):[]}function Dm(){var a=Em(tm()),b=a&&a.parent;if(b)return Em(b)}function Fm(){var a=Em(tm());if(a){for(;a.parent;){var b=Em(a.parent);if(!b)break;a=b}return a}}function Em(a){var b=um();return a.isDestination?b.destination[a.ctid]:b.container[a.ctid]}
function Gm(){var a=um();if(a.pending){for(var b,c=[],d=!1,e=Cm(),f=Am(),g={},h=0;h<a.pending.length;g={jg:void 0},h++)g.jg=a.pending[h],nb(g.jg.target.isDestination?f:e,function(m){return function(n){return n===m.jg.target.ctid}}(g))?d||(b=g.jg.onLoad,d=!0):c.push(g.jg);a.pending=c;if(b)try{b(Bm())}catch(m){}}}
function Hm(){for(var a=jg.ctid,b=Cm(),c=Am(),d=function(n,p){var q={canonicalContainerId:jg.canonicalContainerId,scriptContainerId:a,state:2,containers:b.slice(),destinations:c.slice()};vc&&(q.scriptElement=vc);wc&&(q.scriptSource=wc);if(Dm()===void 0){var r;a:{if((q.scriptContainerId||"").indexOf("GTM-")>=0){var t;b:{var u,v=(u=q.scriptElement)==null?void 0:u.src;if(v){for(var w=Pj.C,y=Uk(v),A=w?y.pathname:""+y.hostname+y.pathname,C=z.scripts,E="",G=0;G<C.length;++G){var I=C[G];if(!(I.innerHTML.length===
0||!w&&I.innerHTML.indexOf(q.scriptContainerId||"SHOULD_NOT_BE_SET")<0||I.innerHTML.indexOf(A)<0)){if(I.innerHTML.indexOf("(function(w,d,s,l,i)")>=0){t=String(G);break b}E=String(G)}}if(E){t=E;break b}}t=void 0}var N=t;if(N){qm=!0;r=N;break a}}var T=[].slice.call(z.scripts);r=q.scriptElement?String(T.indexOf(q.scriptElement)):"-1"}q.htmlLoadOrder=r;q.loadScriptType=rm(q)}var ca=p?e.destination:e.container,P=ca[n];P?(p&&P.state===0&&M(93),Object.assign(P,q)):ca[n]=q},e=um(),f=l(b),g=f.next();!g.done;g=
f.next())d(g.value,!1);for(var h=l(c),m=h.next();!m.done;m=h.next())d(m.value,!0);e.canonical[Bm()]={};Gm()}function Im(){var a=Bm();return!!um().canonical[a]}function Jm(a){return!!um().container[a]}function Km(a){var b=um().destination[a];return!!b&&!!b.state}function tm(){return{ctid:jg.ctid,isDestination:ym.pe}}function Lm(){var a=um().container,b;for(b in a)if(a.hasOwnProperty(b)&&a[b].state===1)return!0;return!1}
function Mm(){var a={};rb(um().destination,function(b,c){c.state===0&&(a[b]=c)});return a}function Nm(a){return!!(a&&a.parent&&a.context&&a.context.source===1&&a.parent.ctid.indexOf("GTM-")!==0)}function Om(){for(var a=um(),b=l(Cm()),c=b.next();!c.done;c=b.next())if(a.injectedFirstPartyContainers[c.value])return!0;return!1};var Pm={Ia:{je:0,oe:1,Fi:2}};Pm.Ia[Pm.Ia.je]="FULL_TRANSMISSION";Pm.Ia[Pm.Ia.oe]="LIMITED_TRANSMISSION";Pm.Ia[Pm.Ia.Fi]="NO_TRANSMISSION";var Qm={X:{Fb:0,Da:1,Fc:2,Oc:3}};Qm.X[Qm.X.Fb]="NO_QUEUE";Qm.X[Qm.X.Da]="ADS";Qm.X[Qm.X.Fc]="ANALYTICS";Qm.X[Qm.X.Oc]="MONITORING";function Rm(){var a=xc("google_tag_data",{});return a.ics=a.ics||new Sm}var Sm=function(){this.entries={};this.waitPeriodTimedOut=this.wasSetLate=this.accessedAny=this.accessedDefault=this.usedImplicit=this.usedUpdate=this.usedDefault=this.usedDeclare=this.active=!1;this.C=[]};
Sm.prototype.default=function(a,b,c,d,e,f,g){this.usedDefault||this.usedDeclare||!this.accessedDefault&&!this.accessedAny||(this.wasSetLate=!0);this.usedDefault=this.active=!0;db("TAGGING",19);b==null?db("TAGGING",18):Tm(this,a,b==="granted",c,d,e,f,g)};Sm.prototype.waitForUpdate=function(a,b,c){for(var d=0;d<a.length;d++)Tm(this,a[d],void 0,void 0,"","",b,c)};
var Tm=function(a,b,c,d,e,f,g,h){var m=a.entries,n=m[b]||{},p=n.region,q=d&&kb(d)?d.toUpperCase():void 0;e=e.toUpperCase();f=f.toUpperCase();if(e===""||q===f||(q===e?p!==f:!q&&!p)){var r=!!(g&&g>0&&n.update===void 0),t={region:q,declare_region:n.declare_region,implicit:n.implicit,default:c!==void 0?c:n.default,declare:n.declare,update:n.update,quiet:r};if(e!==""||n.default!==!1)m[b]=t;r&&x.setTimeout(function(){m[b]===t&&t.quiet&&(db("TAGGING",2),a.waitPeriodTimedOut=!0,a.clearTimeout(b,void 0,h),
a.notifyListeners())},g)}};k=Sm.prototype;k.clearTimeout=function(a,b,c){var d=[a],e=c.delegatedConsentTypes,f;for(f in e)e.hasOwnProperty(f)&&e[f]===a&&d.push(f);var g=this.entries[a]||{},h=this.getConsentState(a,c);if(g.quiet){g.quiet=!1;for(var m=l(d),n=m.next();!n.done;n=m.next())Um(this,n.value)}else if(b!==void 0&&h!==b)for(var p=l(d),q=p.next();!q.done;q=p.next())Um(this,q.value)};
k.update=function(a,b,c){this.usedDefault||this.usedDeclare||this.usedUpdate||!this.accessedAny||(this.wasSetLate=!0);this.usedUpdate=this.active=!0;if(b!=null){var d=this.getConsentState(a,c),e=this.entries;(e[a]=e[a]||{}).update=b==="granted";this.clearTimeout(a,d,c)}};
k.declare=function(a,b,c,d,e){this.usedDeclare=this.active=!0;var f=this.entries,g=f[a]||{},h=g.declare_region,m=c&&kb(c)?c.toUpperCase():void 0;d=d.toUpperCase();e=e.toUpperCase();if(d===""||m===e||(m===d?h!==e:!m&&!h)){var n={region:g.region,declare_region:m,declare:b==="granted",implicit:g.implicit,default:g.default,update:g.update,quiet:g.quiet};if(d!==""||g.declare!==!1)f[a]=n}};
k.implicit=function(a,b){this.usedImplicit=!0;var c=this.entries,d=c[a]=c[a]||{};d.implicit!==!1&&(d.implicit=b==="granted")};
k.getConsentState=function(a,b){var c=this.entries,d=c[a]||{},e=d.update;if(e!==void 0)return e?1:2;if(b.usedContainerScopedDefaults){var f=b.containerScopedDefaults[a];if(f===3)return 1;if(f===2)return 2}else if(e=d.default,e!==void 0)return e?1:2;if(b==null?0:b.delegatedConsentTypes.hasOwnProperty(a)){var g=b.delegatedConsentTypes[a],h=c[g]||{};e=h.update;if(e!==void 0)return e?1:2;if(b.usedContainerScopedDefaults){var m=b.containerScopedDefaults[g];if(m===3)return 1;if(m===2)return 2}else if(e=
h.default,e!==void 0)return e?1:2}e=d.declare;if(e!==void 0)return e?1:2;e=d.implicit;return e!==void 0?e?3:4:0};k.addListener=function(a,b){this.C.push({consentTypes:a,zd:b})};var Um=function(a,b){for(var c=0;c<a.C.length;++c){var d=a.C[c];Array.isArray(d.consentTypes)&&d.consentTypes.indexOf(b)!==-1&&(d.vm=!0)}};Sm.prototype.notifyListeners=function(a,b){for(var c=0;c<this.C.length;++c){var d=this.C[c];if(d.vm){d.vm=!1;try{d.zd({consentEventId:a,consentPriorityId:b})}catch(e){}}}};var Vm=!1,Wm=!1,Xm={},Ym={delegatedConsentTypes:{},corePlatformServices:{},usedCorePlatformServices:!1,selectedAllCorePlatformServices:!1,containerScopedDefaults:(Xm.ad_storage=1,Xm.analytics_storage=1,Xm.ad_user_data=1,Xm.ad_personalization=1,Xm),usedContainerScopedDefaults:!1};function Zm(a){var b=Rm();b.accessedAny=!0;return(kb(a)?[a]:a).every(function(c){switch(b.getConsentState(c,Ym)){case 1:case 3:return!0;case 2:case 4:return!1;default:return!0}})}
function $m(a){var b=Rm();b.accessedAny=!0;return b.getConsentState(a,Ym)}function an(a){var b=Rm();b.accessedAny=!0;return!(b.entries[a]||{}).quiet}function bn(){if(!Ka(7))return!1;var a=Rm();a.accessedAny=!0;if(a.active)return!0;if(!Ym.usedContainerScopedDefaults)return!1;for(var b=l(Object.keys(Ym.containerScopedDefaults)),c=b.next();!c.done;c=b.next())if(Ym.containerScopedDefaults[c.value]!==1)return!0;return!1}function cn(a,b){Rm().addListener(a,b)}
function dn(a,b){Rm().notifyListeners(a,b)}function en(a,b){function c(){for(var e=0;e<b.length;e++)if(!an(b[e]))return!0;return!1}if(c()){var d=!1;cn(b,function(e){d||c()||(d=!0,a(e))})}else a({})}
function fn(a,b){function c(){for(var h=[],m=0;m<e.length;m++){var n=e[m];Zm(n)&&!f[n]&&h.push(n)}return h}function d(h){for(var m=0;m<h.length;m++)f[h[m]]=!0}var e=kb(b)?[b]:b,f={},g=c();g.length!==e.length&&(d(g),cn(e,function(h){function m(q){q.length!==0&&(d(q),h.consentTypes=q,a(h))}var n=c();if(n.length!==0){var p=Object.keys(f).length;n.length+p>=e.length?m(n):x.setTimeout(function(){m(c())},500)}}))};var gn={},hn=(gn[Qm.X.Fb]=Pm.Ia.je,gn[Qm.X.Da]=Pm.Ia.je,gn[Qm.X.Fc]=Pm.Ia.je,gn[Qm.X.Oc]=Pm.Ia.je,gn),jn=function(a,b){this.C=a;this.consentTypes=b};jn.prototype.isConsentGranted=function(){switch(this.C){case 0:return this.consentTypes.every(function(a){return Zm(a)});case 1:return this.consentTypes.some(function(a){return Zm(a)});default:lc(this.C,"consentsRequired had an unknown type")}};
var kn={},ln=(kn[Qm.X.Fb]=new jn(0,[]),kn[Qm.X.Da]=new jn(0,["ad_storage"]),kn[Qm.X.Fc]=new jn(0,["analytics_storage"]),kn[Qm.X.Oc]=new jn(1,["ad_storage","analytics_storage"]),kn);var nn=function(a){var b=this;this.type=a;this.C=[];cn(ln[a].consentTypes,function(){mn(b)||b.flush()})};nn.prototype.flush=function(){for(var a=l(this.C),b=a.next();!b.done;b=a.next()){var c=b.value;c()}this.C=[]};var mn=function(a){return hn[a.type]===Pm.Ia.Fi&&!ln[a.type].isConsentGranted()},on=function(a,b){mn(a)?a.C.push(b):b()},pn=new Map;function qn(a){pn.has(a)||pn.set(a,new nn(a));return pn.get(a)};var rn={Z:{Nm:"aw_user_data_cache",Kh:"cookie_deprecation_label",wg:"diagnostics_page_id",Vn:"fl_user_data_cache",Xn:"ga4_user_data_cache",Ef:"ip_geo_data_cache",Ai:"ip_geo_fetch_in_progress",ql:"nb_data",sl:"page_experiment_ids",Nf:"pt_data",tl:"pt_listener_set",Bl:"service_worker_endpoint",Dl:"shared_user_id",El:"shared_user_id_requested",jh:"shared_user_id_source"}};var sn=function(a){return Ze(function(b){for(var c in a)if(b===a[c]&&!/^[0-9]+$/.test(c))return!0;return!1})}(rn.Z);
function tn(a,b){b=b===void 0?!1:b;if(sn(a)){var c,d,e=(d=(c=xc("google_tag_data",{})).xcd)!=null?d:c.xcd={};if(e[a])return e[a];if(b){var f=void 0,g=1,h={},m={set:function(n){f=n;m.notify()},get:function(){return f},subscribe:function(n){h[String(g)]=n;return g++},unsubscribe:function(n){var p=String(n);return h.hasOwnProperty(p)?(delete h[p],!0):!1},notify:function(){for(var n=l(Object.keys(h)),p=n.next();!p.done;p=n.next()){var q=p.value;try{h[q](a,f)}catch(r){}}}};return e[a]=m}}}
function un(a,b){var c=tn(a,!0);c&&c.set(b)}function vn(a){var b;return(b=tn(a))==null?void 0:b.get()}function wn(a){var b={},c=tn(a);if(!c){c=tn(a,!0);if(!c)return;c.set(b)}return c.get()}function xn(a,b){if(typeof b==="function"){var c;return(c=tn(a,!0))==null?void 0:c.subscribe(b)}}function yn(a,b){var c=tn(a);return c?c.unsubscribe(b):!1};var zn="https://"+Si(21,"www.googletagmanager.com"),An="/td?id="+jg.ctid,Bn={},Cn=(Bn.tdp=1,Bn.exp=1,Bn.pid=1,Bn.dl=1,Bn.seq=1,Bn.t=1,Bn.v=1,Bn),Dn=["mcc"],En={},Fn={},Gn=!1,Hn=void 0;function In(a,b,c){Fn[a]=b;(c===void 0||c)&&Jn(a)}function Jn(a,b){En[a]!==void 0&&(b===void 0||!b)||Eb(jg.ctid,"GTM-")&&a==="mcc"||(En[a]=!0)}
function Kn(a){a=a===void 0?!1:a;var b=Object.keys(En).filter(function(c){return En[c]===!0&&Fn[c]!==void 0&&(a||!Dn.includes(c))}).map(function(c){var d=Fn[c];typeof d==="function"&&(d=d());return d?"&"+c+"="+d:""}).join("");return""+cl(zn)+An+(""+b+"&z=0")}function Ln(){Object.keys(En).forEach(function(a){Cn[a]||(En[a]=!1)})}
function Mn(a){a=a===void 0?!1:a;if(Pj.ba&&jl&&jg.ctid){var b=qn(Qm.X.Oc);if(mn(b))Gn||(Gn=!0,on(b,Mn));else{var c=Kn(a),d={destinationId:jg.ctid,endpoint:61};a?jm(d,c,void 0,{Ch:!0},void 0,function(){im(d,c+"&img=1")}):im(d,c);Ln();Gn=!1}}}var Nn={};
function On(a){var b=String(a);Nn.hasOwnProperty(b)||(Nn[b]=!0,In("csp",Object.keys(Nn).join("~")),Jn("csp",!0),Hn===void 0&&F(171)&&(Hn=x.setTimeout(function(){var c=En.csp;En.csp=!0;En.seq=!1;var d=Kn(!1);En.csp=c;En.seq=!0;Fc(d+"&script=1");Hn=void 0},500)))}function Pn(){Object.keys(En).filter(function(a){return En[a]&&!Cn[a]}).length>0&&Mn(!0)}var Qn;
function Rn(){if(vn(rn.Z.wg)===void 0){var a=function(){un(rn.Z.wg,ob());Qn=0};a();x.setInterval(a,864E5)}else xn(rn.Z.wg,function(){Qn=0});Qn=0}function Sn(){Rn();In("v","3");In("t","t");In("pid",function(){return String(vn(rn.Z.wg))});In("seq",function(){return String(++Qn)});In("exp",mk());Kc(x,"pagehide",Pn)};var Tn=["ad_storage","analytics_storage","ad_user_data","ad_personalization"],Un=[K.m.md,K.m.vc,K.m.Zd,K.m.Qb,K.m.uc,K.m.Qa,K.m.Pa,K.m.cb,K.m.nb,K.m.Sb],Vn=!1,Wn=!1,Xn={},Yn={};function Zn(){!Wn&&Vn&&(Tn.some(function(a){return Ym.containerScopedDefaults[a]!==1})||$n("mbc"));Wn=!0}function $n(a){jl&&(In(a,"1"),Mn())}function ao(a,b){if(!Xn[b]&&(Xn[b]=!0,Yn[b]))for(var c=l(Un),d=c.next();!d.done;d=c.next())if(O(a,d.value)){$n("erc");break}};function bo(a){db("HEALTH",a)};var co={pp:Si(22,"eyIwIjoiVVMiLCIxIjoiVVMtTUkiLCIyIjpmYWxzZSwiMyI6IiIsIjQiOiIiLCI1Ijp0cnVlLCI2IjpmYWxzZSwiNyI6ImFkX3N0b3JhZ2V8YW5hbHl0aWNzX3N0b3JhZ2V8YWRfdXNlcl9kYXRhfGFkX3BlcnNvbmFsaXphdGlvbiJ9")},eo={},fo=!1;function go(){function a(){c!==void 0&&yn(rn.Z.Ef,c);try{var e=vn(rn.Z.Ef);eo=JSON.parse(e)}catch(f){M(123),bo(2),eo={}}fo=!0;b()}var b=ho,c=void 0,d=vn(rn.Z.Ef);d?a(d):(c=xn(rn.Z.Ef,a),io())}
function io(){function a(c){un(rn.Z.Ef,c||"{}");un(rn.Z.Ai,!1)}if(!vn(rn.Z.Ai)){un(rn.Z.Ai,!0);var b="";try{x.fetch(b,{method:"GET",cache:"no-store",mode:"cors",credentials:"omit"}).then(function(c){c.ok?c.text().then(function(d){a(d)},function(){a()}):a()},function(){a()})}catch(c){a()}}}
function jo(){var a=co.pp;try{return JSON.parse(bb(a))}catch(b){return M(123),bo(2),{}}}function ko(){return eo["0"]||""}function lo(){return eo["1"]||""}function mo(){var a=!1;return a}function no(){return eo["6"]!==!1}function oo(){var a="";return a}
function po(){var a=!1;a=!!eo["5"];return a}function qo(){var a="";return a};var ro={},so=Object.freeze((ro[K.m.Ea]=1,ro[K.m.yg]=1,ro[K.m.zg]=1,ro[K.m.Ob]=1,ro[K.m.sa]=1,ro[K.m.nb]=1,ro[K.m.ob]=1,ro[K.m.yb]=1,ro[K.m.ed]=1,ro[K.m.Sb]=1,ro[K.m.cb]=1,ro[K.m.Hc]=1,ro[K.m.Ze]=1,ro[K.m.oa]=1,ro[K.m.kk]=1,ro[K.m.cf]=1,ro[K.m.Jg]=1,ro[K.m.Kg]=1,ro[K.m.Zd]=1,ro[K.m.Ak]=1,ro[K.m.rc]=1,ro[K.m.ce]=1,ro[K.m.Ck]=1,ro[K.m.Ng]=1,ro[K.m.ei]=1,ro[K.m.Kc]=1,ro[K.m.Lc]=1,ro[K.m.Pa]=1,ro[K.m.fi]=1,ro[K.m.Vb]=1,ro[K.m.pb]=1,ro[K.m.ld]=1,ro[K.m.md]=1,ro[K.m.pf]=1,ro[K.m.hi]=1,ro[K.m.rf]=1,ro[K.m.vc]=
1,ro[K.m.od]=1,ro[K.m.Ug]=1,ro[K.m.Wb]=1,ro[K.m.rd]=1,ro[K.m.Ii]=1,ro));Object.freeze([K.m.Aa,K.m.Wa,K.m.Db,K.m.zb,K.m.gi,K.m.Qa,K.m.ai,K.m.yn]);
var to={},uo=Object.freeze((to[K.m.Zm]=1,to[K.m.bn]=1,to[K.m.dn]=1,to[K.m.fn]=1,to[K.m.gn]=1,to[K.m.kn]=1,to[K.m.ln]=1,to[K.m.mn]=1,to[K.m.on]=1,to[K.m.Td]=1,to)),vo={},wo=Object.freeze((vo[K.m.Zj]=1,vo[K.m.bk]=1,vo[K.m.Pd]=1,vo[K.m.Qd]=1,vo[K.m.dk]=1,vo[K.m.Xc]=1,vo[K.m.Rd]=1,vo[K.m.jc]=1,vo[K.m.Gc]=1,vo[K.m.kc]=1,vo[K.m.kb]=1,vo[K.m.Sd]=1,vo[K.m.xb]=1,vo[K.m.ek]=1,vo)),xo=Object.freeze([K.m.Ea,K.m.Pe,K.m.Ob,K.m.Hc,K.m.Zd,K.m.jf,K.m.pb,K.m.od]),yo=Object.freeze([].concat(va(xo))),zo=Object.freeze([K.m.ob,
K.m.Kg,K.m.pf,K.m.hi,K.m.Gg]),Ao=Object.freeze([].concat(va(zo))),Bo={},Co=(Bo[K.m.U]="1",Bo[K.m.ja]="2",Bo[K.m.V]="3",Bo[K.m.La]="4",Bo),Do={},Eo=Object.freeze((Do.search="s",Do.youtube="y",Do.playstore="p",Do.shopping="h",Do.ads="a",Do.maps="m",Do));function Fo(a){return typeof a!=="object"||a===null?{}:a}function Go(a){return a===void 0||a===null?"":typeof a==="object"?a.toString():String(a)}function Ho(a){if(a!==void 0&&a!==null)return Go(a)}function Io(a){return typeof a==="number"?a:Ho(a)};function Jo(a){return a&&a.indexOf("pending:")===0?Ko(a.substr(8)):!1}function Ko(a){if(a==null||a.length===0)return!1;var b=Number(a),c=zb();return b<c+3E5&&b>c-9E5};var Lo=!1,Mo=!1,No=!1,Oo=0,Po=!1,Qo=[];function Ro(a){if(Oo===0)Po&&Qo&&(Qo.length>=100&&Qo.shift(),Qo.push(a));else if(So()){var b=Si(41,'google.tagmanager.ta.prodqueue'),c=xc(b,[]);c.length>=50&&c.shift();c.push(a)}}function To(){Uo();Lc(z,"TAProdDebugSignal",To)}function Uo(){if(!Mo){Mo=!0;Vo();var a=Qo;Qo=void 0;a==null||a.forEach(function(b){Ro(b)})}}
function Vo(){var a=z.documentElement.getAttribute("data-tag-assistant-prod-present");Ko(a)?Oo=1:!Jo(a)||Lo||No?Oo=2:(No=!0,Kc(z,"TAProdDebugSignal",To,!1),x.setTimeout(function(){Uo();Lo=!0},200))}function So(){if(!Po)return!1;switch(Oo){case 1:case 0:return!0;case 2:return!1;default:return!1}};var Wo=!1;function Xo(a,b){var c=Cm(),d=Am();if(So()){var e=Yo("INIT");e.containerLoadSource=a!=null?a:0;b&&(e.parentTargetReference=b);e.aliases=c;e.destinations=d;Ro(e)}}
function Zo(a){var b,c,d,e;b=a.targetId;c=a.request;d=a.Ya;e=a.isBatched;var f;if(f=So()){var g;a:switch(c.endpoint){case 19:case 47:case 44:g=!0;break a;default:g=!1}f=!g}if(f){var h=Yo("GTAG_HIT",{eventId:d.eventId,priorityId:d.priorityId});h.target=b;h.url=c.url;c.postBody&&(h.postBody=c.postBody);h.parameterEncoding=c.parameterEncoding;h.endpoint=c.endpoint;e!==void 0&&(h.isBatched=e);Ro(h)}}function $o(a){So()&&Zo(a())}
function Yo(a,b){b=b===void 0?{}:b;b.groupId=ap;var c,d=b,e={publicId:bp};d.eventId!=null&&(e.eventId=d.eventId);d.priorityId!=null&&(e.priorityId=d.priorityId);d.eventName&&(e.eventName=d.eventName);d.groupId&&(e.groupId=d.groupId);d.tagName&&(e.tagName=d.tagName);c={containerProduct:"GTM",key:e,version:'1',messageType:a};c.containerProduct=Wo?"OGT":"GTM";c.key.targetRef=cp;return c}var bp="",cp={ctid:"",isDestination:!1},ap;
function dp(a){var b=jg.ctid,c=zm();Oo=0;Po=!0;Vo();ap=a;bp=b;Wo=dk;cp={ctid:b,isDestination:c}};var ep=[K.m.U,K.m.ja,K.m.V,K.m.La],fp,gp;function hp(a){var b=a[K.m.fc];b||(b=[""]);for(var c={Zf:0};c.Zf<b.length;c={Zf:c.Zf},++c.Zf)rb(a,function(d){return function(e,f){if(e!==K.m.fc){var g=Go(f),h=b[d.Zf],m=ko(),n=lo();Wm=!0;Vm&&db("TAGGING",20);Rm().declare(e,g,h,m,n)}}}(c))}
function ip(a){Zn();!gp&&fp&&$n("crc");gp=!0;var b=a[K.m.qg];b&&M(41);var c=a[K.m.fc];c?M(40):c=[""];for(var d={cg:0};d.cg<c.length;d={cg:d.cg},++d.cg)rb(a,function(e){return function(f,g){if(f!==K.m.fc&&f!==K.m.qg){var h=Ho(g),m=c[e.cg],n=Number(b),p=ko(),q=lo();n=n===void 0?0:n;Vm=!0;Wm&&db("TAGGING",20);Rm().default(f,h,m,p,q,n,Ym)}}}(d))}
function jp(a){Ym.usedContainerScopedDefaults=!0;var b=a[K.m.fc];if(b){var c=Array.isArray(b)?b:[b];if(!c.includes(lo())&&!c.includes(ko()))return}rb(a,function(d,e){switch(d){case "ad_storage":case "analytics_storage":case "ad_user_data":case "ad_personalization":break;default:return}Ym.usedContainerScopedDefaults=!0;Ym.containerScopedDefaults[d]=e==="granted"?3:2})}
function kp(a,b){Zn();fp=!0;rb(a,function(c,d){var e=Go(d);Vm=!0;Wm&&db("TAGGING",20);Rm().update(c,e,Ym)});dn(b.eventId,b.priorityId)}function lp(a){a.hasOwnProperty("all")&&(Ym.selectedAllCorePlatformServices=!0,rb(Eo,function(b){Ym.corePlatformServices[b]=a.all==="granted";Ym.usedCorePlatformServices=!0}));rb(a,function(b,c){b!=="all"&&(Ym.corePlatformServices[b]=c==="granted",Ym.usedCorePlatformServices=!0)})}function Q(a){Array.isArray(a)||(a=[a]);return a.every(function(b){return Zm(b)})}
function mp(a,b){cn(a,b)}function np(a,b){fn(a,b)}function op(a,b){en(a,b)}function pp(){var a=[K.m.U,K.m.La,K.m.V];Rm().waitForUpdate(a,500,Ym)}function qp(a){for(var b=l(a),c=b.next();!c.done;c=b.next()){var d=c.value;Rm().clearTimeout(d,void 0,Ym)}dn()}function rp(){if(!ek)for(var a=no()?pk(Pj.Sa):pk(Pj.qb),b=0;b<ep.length;b++){var c=ep[b],d=c,e=a[c]?"granted":"denied";Rm().implicit(d,e)}};var sp=!1,tp=[];function up(){if(!sp){sp=!0;for(var a=tp.length-1;a>=0;a--)tp[a]();tp=[]}};var vp=x.google_tag_manager=x.google_tag_manager||{};function wp(a,b){return vp[a]=vp[a]||b()}function xp(){var a=jg.ctid,b=yp;vp[a]=vp[a]||b}function zp(){var a=vp.sequence||1;vp.sequence=a+1;return a};function Ap(){if(vp.pscdl!==void 0)vn(rn.Z.Kh)===void 0&&un(rn.Z.Kh,vp.pscdl);else{var a=function(c){vp.pscdl=c;un(rn.Z.Kh,c)},b=function(){a("error")};try{tc.cookieDeprecationLabel?(a("pending"),tc.cookieDeprecationLabel.getValue().then(a).catch(b)):a("noapi")}catch(c){b(c)}}};var Bp=0;function Cp(a){jl&&a===void 0&&Bp===0&&(In("mcc","1"),Bp=1)};var Dp={Cf:{Rm:"cd",Sm:"ce",Tm:"cf",Um:"cpf",Vm:"cu"}};var Ep=/^(?:AW|DC|G|GF|GT|HA|MC|UA)$/,Fp=/\s/;
function Gp(a,b){if(kb(a)){a=xb(a);var c=a.indexOf("-");if(!(c<0)){var d=a.substring(0,c);if(Ep.test(d)){var e=a.substring(c+1),f;if(b){var g=function(n){var p=n.indexOf("/");return p<0?[n]:[n.substring(0,p),n.substring(p+1)]};f=g(e);if(d==="DC"&&f.length===2){var h=g(f[1]);h.length===2&&(f[1]=h[0],f.push(h[1]))}}else{f=e.split("/");for(var m=0;m<f.length;m++)if(!f[m]||Fp.test(f[m])&&(d!=="AW"||m!==1))return}return{id:a,prefix:d,destinationId:d+"-"+f[0],ids:f}}}}}
function Hp(a,b){for(var c={},d=0;d<a.length;++d){var e=Gp(a[d],b);e&&(c[e.id]=e)}var f=[],g;for(g in c)if(c.hasOwnProperty(g)){var h=c[g];h.prefix==="AW"&&h.ids[Ip[1]]&&f.push(h.destinationId)}for(var m=0;m<f.length;++m)delete c[f[m]];for(var n=[],p=l(Object.keys(c)),q=p.next();!q.done;q=p.next())n.push(c[q.value]);return n}var Jp={},Ip=(Jp[0]=0,Jp[1]=1,Jp[2]=2,Jp[3]=0,Jp[4]=1,Jp[5]=0,Jp[6]=0,Jp[7]=0,Jp);var Kp=Number('')||500,Lp={},Mp={},Np={initialized:11,complete:12,interactive:13},Op={},Pp=Object.freeze((Op[K.m.pb]=!0,Op)),Qp=void 0;function Rp(a,b){if(b.length&&jl){var c;(c=Lp)[a]!=null||(c[a]=[]);Mp[a]!=null||(Mp[a]=[]);var d=b.filter(function(e){return!Mp[a].includes(e)});Lp[a].push.apply(Lp[a],va(d));Mp[a].push.apply(Mp[a],va(d));!Qp&&d.length>0&&(Jn("tdc",!0),Qp=x.setTimeout(function(){Mn();Lp={};Qp=void 0},Kp))}}
function Sp(a,b){var c={},d;for(d in b)b.hasOwnProperty(d)&&(c[d]=!0);for(var e in a)a.hasOwnProperty(e)&&(c[e]=!0);return c}
function Tp(a,b,c,d){c=c===void 0?{}:c;d=d===void 0?"":d;if(a===b)return[];var e=function(r,t){var u;id(t)==="object"?u=t[r]:id(t)==="array"&&(u=t[r]);return u===void 0?Pp[r]:u},f=Sp(a,b),g;for(g in f)if(f.hasOwnProperty(g)){var h=(d?d+".":"")+g,m=e(g,a),n=e(g,b),p=id(m)==="object"||id(m)==="array",q=id(n)==="object"||id(n)==="array";if(p&&q)Tp(m,n,c,h);else if(p||q||m!==n)c[h]=!0}return Object.keys(c)}
function Up(){In("tdc",function(){Qp&&(x.clearTimeout(Qp),Qp=void 0);var a=[],b;for(b in Lp)Lp.hasOwnProperty(b)&&a.push(b+"*"+Lp[b].join("."));return a.length?a.join("!"):void 0},!1)};var Vp=function(a,b,c,d,e,f,g,h,m,n,p){this.eventId=a;this.priorityId=b;this.C=c;this.R=d;this.N=e;this.P=f;this.H=g;this.eventMetadata=h;this.onSuccess=m;this.onFailure=n;this.isGtmEvent=p},Wp=function(a,b){var c=[];switch(b){case 3:c.push(a.C);c.push(a.R);c.push(a.N);c.push(a.P);c.push(a.H);break;case 2:c.push(a.C);break;case 1:c.push(a.R);c.push(a.N);c.push(a.P);c.push(a.H);break;case 4:c.push(a.C),c.push(a.R),c.push(a.N),c.push(a.P)}return c},O=function(a,b,c,d){for(var e=l(Wp(a,d===void 0?3:
d)),f=e.next();!f.done;f=e.next()){var g=f.value;if(g[b]!==void 0)return g[b]}return c},Xp=function(a){for(var b={},c=Wp(a,4),d=l(c),e=d.next();!e.done;e=d.next())for(var f=Object.keys(e.value),g=l(f),h=g.next();!h.done;h=g.next())b[h.value]=1;return Object.keys(b)};
Vp.prototype.getMergedValues=function(a,b,c){function d(n){kd(n)&&rb(n,function(p,q){f=!0;e[p]=q})}b=b===void 0?3:b;var e={},f=!1;c&&d(c);var g=Wp(this,b);g.reverse();for(var h=l(g),m=h.next();!m.done;m=h.next())d(m.value[a]);return f?e:void 0};
var Yp=function(a){for(var b=[K.m.Ue,K.m.Qe,K.m.Re,K.m.Se,K.m.Te,K.m.Ve,K.m.We],c=Wp(a,3),d=l(c),e=d.next();!e.done;e=d.next()){for(var f=e.value,g={},h=!1,m=l(b),n=m.next();!n.done;n=m.next()){var p=n.value;f[p]!==void 0&&(g[p]=f[p],h=!0)}var q=h?g:void 0;if(q)return q}return{}},Zp=function(a,b){this.eventId=a;this.priorityId=b;this.H={};this.R={};this.C={};this.N={};this.ba={};this.P={};this.eventMetadata={};this.isGtmEvent=!1;this.onSuccess=function(){};this.onFailure=function(){}},$p=function(a,
b){a.H=b;return a},aq=function(a,b){a.R=b;return a},bq=function(a,b){a.C=b;return a},cq=function(a,b){a.N=b;return a},dq=function(a,b){a.ba=b;return a},eq=function(a,b){a.P=b;return a},fq=function(a,b){a.eventMetadata=b||{};return a},gq=function(a,b){a.onSuccess=b;return a},hq=function(a,b){a.onFailure=b;return a},iq=function(a,b){a.isGtmEvent=b;return a},jq=function(a){return new Vp(a.eventId,a.priorityId,a.H,a.R,a.C,a.N,a.P,a.eventMetadata,a.onSuccess,a.onFailure,a.isGtmEvent)};var R={A:{Ij:"accept_by_default",pg:"add_tag_timing",Gh:"allow_ad_personalization",Kj:"batch_on_navigation",Mj:"client_id_source",Ge:"consent_event_id",He:"consent_priority_id",Lq:"consent_state",fa:"consent_updated",Wc:"conversion_linker_enabled",ya:"cookie_options",sg:"create_dc_join",tg:"create_fpm_geo_join",ug:"create_fpm_signals_join",Od:"create_google_join",Je:"em_event",Oq:"endpoint_for_debug",Yj:"enhanced_client_id_source",Nh:"enhanced_match_result",ie:"euid_mode_enabled",fb:"event_start_timestamp_ms",
Wk:"event_usage",Wg:"extra_tag_experiment_ids",Vq:"add_parameter",ui:"attribution_reporting_experiment",wi:"counting_method",Xg:"send_as_iframe",Wq:"parameter_order",Yg:"parsed_target",Wn:"ga4_collection_subdomain",Zk:"gbraid_cookie_marked",ia:"hit_type",sd:"hit_type_override",bo:"is_config_command",Ff:"is_consent_update",Gf:"is_conversion",il:"is_ecommerce",ud:"is_external_event",Bi:"is_fallback_aw_conversion_ping_allowed",Hf:"is_first_visit",jl:"is_first_visit_conversion",Zg:"is_fl_fallback_conversion_flow_allowed",
ke:"is_fpm_encryption",ah:"is_fpm_split",me:"is_gcp_conversion",kl:"is_google_signals_allowed",vd:"is_merchant_center",bh:"is_new_to_site",eh:"is_server_side_destination",ne:"is_session_start",nl:"is_session_start_conversion",Zq:"is_sgtm_ga_ads_conversion_study_control_group",ar:"is_sgtm_prehit",ol:"is_sgtm_service_worker",Ci:"is_split_conversion",co:"is_syn",If:"join_id",Di:"join_elapsed",Jf:"join_timer_sec",qe:"tunnel_updated",ir:"prehit_for_retry",kr:"promises",lr:"record_aw_latency",yc:"redact_ads_data",
se:"redact_click_ids",oo:"remarketing_only",zl:"send_ccm_parallel_ping",ih:"send_fledge_experiment",nr:"send_ccm_parallel_test_ping",Of:"send_to_destinations",Hi:"send_to_targets",Al:"send_user_data_hit",hb:"source_canonical_id",Ha:"speculative",Fl:"speculative_in_message",Gl:"suppress_script_load",Hl:"syn_or_mod",Ll:"transient_ecsid",Pf:"transmission_type",ib:"user_data",ur:"user_data_from_automatic",vr:"user_data_from_automatic_getter",ue:"user_data_from_code",mh:"user_data_from_manual",Nl:"user_data_mode",
Qf:"user_id_updated"}};var kq={Mm:Number("5"),Mr:Number("")},lq=[],mq=!1;function nq(a){lq.push(a)}var oq="?id="+jg.ctid,pq=void 0,qq={},rq=void 0,sq=new function(){var a=5;kq.Mm>0&&(a=kq.Mm);this.H=a;this.C=0;this.N=[]},tq=1E3;
function uq(a,b){var c=pq;if(c===void 0)if(b)c=zp();else return"";for(var d=[cl("https://www.googletagmanager.com"),"/a",oq],e=l(lq),f=e.next();!f.done;f=e.next())for(var g=f.value,h=g({eventId:c,Nd:!!a}),m=l(h),n=m.next();!n.done;n=m.next()){var p=l(n.value),q=p.next().value,r=p.next().value;d.push("&"+q+"="+r)}d.push("&z=0");return d.join("")}
function vq(){if(Pj.ba&&(rq&&(x.clearTimeout(rq),rq=void 0),pq!==void 0&&wq)){var a=qn(Qm.X.Oc);if(mn(a))mq||(mq=!0,on(a,vq));else{var b;if(!(b=qq[pq])){var c=sq;b=c.C<c.H?!1:zb()-c.N[c.C%c.H]<1E3}if(b||tq--<=0)M(1),qq[pq]=!0;else{var d=sq,e=d.C++%d.H;d.N[e]=zb();var f=uq(!0);im({destinationId:jg.ctid,endpoint:56,eventId:pq},f);mq=wq=!1}}}}function xq(){if(il&&Pj.ba){var a=uq(!0,!0);im({destinationId:jg.ctid,endpoint:56,eventId:pq},a)}}var wq=!1;
function yq(a){qq[a]||(a!==pq&&(vq(),pq=a),wq=!0,rq||(rq=x.setTimeout(vq,500)),uq().length>=2022&&vq())}var zq=ob();function Aq(){zq=ob()}function Bq(){return[["v","3"],["t","t"],["pid",String(zq)]]};var Cq={};function Dq(a,b,c){il&&a!==void 0&&(Cq[a]=Cq[a]||[],Cq[a].push(c+b),yq(a))}function Eq(a){var b=a.eventId,c=a.Nd,d=[],e=Cq[b]||[];e.length&&d.push(["epr",e.join(".")]);c&&delete Cq[b];return d};function Fq(a,b,c,d){var e=Gp(a,!0);e&&Gq.register(e,b,c,d)}function Hq(a,b,c,d){var e=Gp(c,d.isGtmEvent);e&&(bk&&(d.deferrable=!0),Gq.push("event",[b,a],e,d))}function Iq(a,b,c,d){var e=Gp(c,d.isGtmEvent);e&&Gq.push("get",[a,b],e,d)}function Jq(a){var b=Gp(a,!0),c;b?c=Kq(Gq,b).C:c={};return c}function Lq(a,b){var c=Gp(a,!0);c&&Mq(Gq,c,b)}
var Nq=function(){this.R={};this.C={};this.H={};this.ba=null;this.P={};this.N=!1;this.status=1},Oq=function(a,b,c,d){this.H=zb();this.C=b;this.args=c;this.messageContext=d;this.type=a},Pq=function(){this.destinations={};this.C={};this.commands=[]},Kq=function(a,b){return a.destinations[b.destinationId]=a.destinations[b.destinationId]||new Nq},Qq=function(a,b,c,d){if(d.C){var e=Kq(a,d.C),f=e.ba;if(f){var g=ld(c,null),h=ld(e.R[d.C.id],null),m=ld(e.P,null),n=ld(e.C,null),p=ld(a.C,null),q={};if(il)try{q=
ld(rk,null)}catch(w){M(72)}var r=d.C.prefix,t=function(w){Dq(d.messageContext.eventId,r,w)},u=jq(iq(hq(gq(fq(dq(cq(eq(bq(aq($p(new Zp(d.messageContext.eventId,d.messageContext.priorityId),g),h),m),n),p),q),d.messageContext.eventMetadata),function(){if(t){var w=t;t=void 0;w("2");if(d.messageContext.onSuccess)d.messageContext.onSuccess()}}),function(){if(t){var w=t;t=void 0;w("3");if(d.messageContext.onFailure)d.messageContext.onFailure()}}),!!d.messageContext.isGtmEvent)),v=function(){try{Dq(d.messageContext.eventId,
r,"1");var w=d.type,y=d.C.id;if(jl&&w==="config"){var A,C=(A=Gp(y))==null?void 0:A.ids;if(!(C&&C.length>1)){var E,G=xc("google_tag_data",{});G.td||(G.td={});E=G.td;var I=ld(u.P);ld(u.C,I);var N=[],T;for(T in E)E.hasOwnProperty(T)&&Tp(E[T],I).length&&N.push(T);N.length&&(Rp(y,N),db("TAGGING",Np[z.readyState]||14));E[y]=I}}f(d.C.id,b,d.H,u)}catch(ca){Dq(d.messageContext.eventId,r,"4")}};b==="gtag.get"?v():on(e.ka,v)}}};
Pq.prototype.register=function(a,b,c,d){var e=Kq(this,a);e.status!==3&&(e.ba=b,e.status=3,e.ka=qn(c),Mq(this,a,d||{}),this.flush())};
Pq.prototype.push=function(a,b,c,d){c!==void 0&&(Kq(this,c).status===1&&(Kq(this,c).status=2,this.push("require",[{}],c,{})),Kq(this,c).N&&(d.deferrable=!1),d.eventMetadata||(d.eventMetadata={}),d.eventMetadata[R.A.Of]||(d.eventMetadata[R.A.Of]=[c.destinationId]),d.eventMetadata[R.A.Hi]||(d.eventMetadata[R.A.Hi]=[c.id]));this.commands.push(new Oq(a,c,b,d));d.deferrable||this.flush()};
Pq.prototype.flush=function(a){for(var b=this,c=[],d=!1,e={};this.commands.length;e={Qc:void 0,rh:void 0}){var f=this.commands[0],g=f.C;if(f.messageContext.deferrable)!g||Kq(this,g).N?(f.messageContext.deferrable=!1,this.commands.push(f)):c.push(f),this.commands.shift();else{switch(f.type){case "require":if(Kq(this,g).status!==3&&!a){this.commands.push.apply(this.commands,c);return}break;case "set":var h=f.args[0];rb(h,function(t,u){ld(Gb(t,u),b.C)});Nj(h,!0);break;case "config":var m=Kq(this,g);
e.Qc={};rb(f.args[0],function(t){return function(u,v){ld(Gb(u,v),t.Qc)}}(e));var n=!!e.Qc[K.m.od];delete e.Qc[K.m.od];var p=g.destinationId===g.id;Nj(e.Qc,!0);n||(p?m.P={}:m.R[g.id]={});m.N&&n||Qq(this,K.m.qa,e.Qc,f);m.N=!0;p?ld(e.Qc,m.P):(ld(e.Qc,m.R[g.id]),M(70));d=!0;break;case "event":e.rh={};rb(f.args[0],function(t){return function(u,v){ld(Gb(u,v),t.rh)}}(e));Nj(e.rh);Qq(this,f.args[1],e.rh,f);break;case "get":var q={},r=(q[K.m.qc]=f.args[0],q[K.m.Ic]=f.args[1],q);Qq(this,K.m.Cb,r,f)}this.commands.shift();
Rq(this,f)}}this.commands.push.apply(this.commands,c);d&&this.flush()};var Rq=function(a,b){if(b.type!=="require")if(b.C)for(var c=Kq(a,b.C).H[b.type]||[],d=0;d<c.length;d++)c[d]();else for(var e in a.destinations)if(a.destinations.hasOwnProperty(e)){var f=a.destinations[e];if(f&&f.H)for(var g=f.H[b.type]||[],h=0;h<g.length;h++)g[h]()}},Mq=function(a,b,c){var d=ld(c,null);ld(Kq(a,b).C,d);Kq(a,b).C=d},Gq=new Pq;function Sq(a,b,c){return typeof a.addEventListener==="function"?(a.addEventListener(b,c,!1),!0):!1}function Tq(a,b,c){typeof a.removeEventListener==="function"&&a.removeEventListener(b,c,!1)};function Uq(a,b,c,d){d=d===void 0?!1:d;a.google_image_requests||(a.google_image_requests=[]);var e=Kl(a.document);if(c){var f=function(){if(c){var g=a.google_image_requests,h=qc(g,e);h>=0&&Array.prototype.splice.call(g,h,1)}Tq(e,"load",f);Tq(e,"error",f)};Sq(e,"load",f);Sq(e,"error",f)}d&&(e.attributionSrc="");e.src=b;a.google_image_requests.push(e)}
function Vq(a){var b;b=b===void 0?!1:b;var c="https://pagead2.googlesyndication.com/pagead/gen_204?id=tcfe";Hl(a,function(d,e){if(d||d===0)c+="&"+e+"="+encodeURIComponent(String(d))});Wq(c,b)}
function Wq(a,b){var c=window,d;b=b===void 0?!1:b;d=d===void 0?!1:d;if(c.fetch){var e={keepalive:!0,credentials:"include",redirect:"follow",method:"get",mode:"no-cors"};d&&(e.mode="cors","setAttributionReporting"in XMLHttpRequest.prototype?e.attributionReporting={eventSourceEligible:"true",triggerEligible:"false"}:e.headers={"Attribution-Reporting-Eligible":"event-source"});c.fetch(a,e)}else Uq(c,a,b===void 0?!1:b,d===void 0?!1:d)};var Xq=function(){this.ba=this.ba;this.P=this.P};Xq.prototype.ba=!1;Xq.prototype.dispose=function(){this.ba||(this.ba=!0,this.N())};Xq.prototype[Symbol.dispose]=function(){this.dispose()};Xq.prototype.addOnDisposeCallback=function(a,b){this.ba?b!==void 0?a.call(b):a():(this.P||(this.P=[]),b&&(a=a.bind(b)),this.P.push(a))};Xq.prototype.N=function(){if(this.P)for(;this.P.length;)this.P.shift()()};function Yq(a){a.addtlConsent!==void 0&&typeof a.addtlConsent!=="string"&&(a.addtlConsent=void 0);a.gdprApplies!==void 0&&typeof a.gdprApplies!=="boolean"&&(a.gdprApplies=void 0);return a.tcString!==void 0&&typeof a.tcString!=="string"||a.listenerId!==void 0&&typeof a.listenerId!=="number"?2:a.cmpStatus&&a.cmpStatus!=="error"?0:3}
var Zq=function(a,b){b=b===void 0?{}:b;Xq.call(this);this.C=null;this.ka={};this.qb=0;this.R=null;this.H=a;var c;this.Sa=(c=b.timeoutMs)!=null?c:500;var d;this.Ba=(d=b.Br)!=null?d:!1};sa(Zq,Xq);Zq.prototype.N=function(){this.ka={};this.R&&(Tq(this.H,"message",this.R),delete this.R);delete this.ka;delete this.H;delete this.C;Xq.prototype.N.call(this)};var ar=function(a){return typeof a.H.__tcfapi==="function"||$q(a)!=null};
Zq.prototype.addEventListener=function(a){var b=this,c={internalBlockOnErrors:this.Ba},d=ll(function(){return a(c)}),e=0;this.Sa!==-1&&(e=setTimeout(function(){c.tcString="tcunavailable";c.internalErrorState=1;d()},this.Sa));var f=function(g,h){clearTimeout(e);g?(c=g,c.internalErrorState=Yq(c),c.internalBlockOnErrors=b.Ba,h&&c.internalErrorState===0||(c.tcString="tcunavailable",h||(c.internalErrorState=3))):(c.tcString="tcunavailable",c.internalErrorState=3);a(c)};try{br(this,"addEventListener",f)}catch(g){c.tcString=
"tcunavailable",c.internalErrorState=3,e&&(clearTimeout(e),e=0),d()}};Zq.prototype.removeEventListener=function(a){a&&a.listenerId&&br(this,"removeEventListener",null,a.listenerId)};
var dr=function(a,b,c){var d;d=d===void 0?"755":d;var e;a:{if(a.publisher&&a.publisher.restrictions){var f=a.publisher.restrictions[b];if(f!==void 0){e=f[d===void 0?"755":d];break a}}e=void 0}var g=e;if(g===0)return!1;var h=c;c===2?(h=0,g===2&&(h=1)):c===3&&(h=1,g===1&&(h=0));var m;if(h===0)if(a.purpose&&a.vendor){var n=cr(a.vendor.consents,d===void 0?"755":d);m=n&&b==="1"&&a.purposeOneTreatment&&a.publisherCC==="CH"?!0:n&&cr(a.purpose.consents,b)}else m=!0;else m=h===1?a.purpose&&a.vendor?cr(a.purpose.legitimateInterests,
b)&&cr(a.vendor.legitimateInterests,d===void 0?"755":d):!0:!0;return m},cr=function(a,b){return!(!a||!a[b])},br=function(a,b,c,d){c||(c=function(){});var e=a.H;if(typeof e.__tcfapi==="function"){var f=e.__tcfapi;f(b,2,c,d)}else if($q(a)){er(a);var g=++a.qb;a.ka[g]=c;if(a.C){var h={};a.C.postMessage((h.__tcfapiCall={command:b,version:2,callId:g,parameter:d},h),"*")}}else c({},!1)},$q=function(a){if(a.C)return a.C;a.C=Il(a.H,"__tcfapiLocator");return a.C},er=function(a){if(!a.R){var b=function(c){try{var d;
d=(typeof c.data==="string"?JSON.parse(c.data):c.data).__tcfapiReturn;a.ka[d.callId](d.returnValue,d.success)}catch(e){}};a.R=b;Sq(a.H,"message",b)}},fr=function(a){if(a.gdprApplies===!1)return!0;a.internalErrorState===void 0&&(a.internalErrorState=Yq(a));return a.cmpStatus==="error"||a.internalErrorState!==0?a.internalBlockOnErrors?(Vq({e:String(a.internalErrorState)}),!1):!0:a.cmpStatus!=="loaded"||a.eventStatus!=="tcloaded"&&a.eventStatus!=="useractioncomplete"?!1:!0};var gr={1:0,3:0,4:0,7:3,9:3,10:3};function hr(){return wp("tcf",function(){return{}})}var ir=function(){return new Zq(x,{timeoutMs:-1})};
function jr(){var a=hr(),b=ir();ar(b)&&!kr()&&!lr()&&M(124);if(!a.active&&ar(b)){kr()&&(a.active=!0,a.purposes={},a.cmpId=0,a.tcfPolicyVersion=0,Rm().active=!0,a.tcString="tcunavailable");pp();try{b.addEventListener(function(c){if(c.internalErrorState!==0)mr(a),qp([K.m.U,K.m.La,K.m.V]),Rm().active=!0;else if(a.gdprApplies=c.gdprApplies,a.cmpId=c.cmpId,a.enableAdvertiserConsentMode=c.enableAdvertiserConsentMode,lr()&&(a.active=!0),!nr(c)||kr()||lr()){a.tcfPolicyVersion=c.tcfPolicyVersion;var d;if(c.gdprApplies===
!1){var e={},f;for(f in gr)gr.hasOwnProperty(f)&&(e[f]=!0);d=e;b.removeEventListener(c)}else if(nr(c)){var g={},h;for(h in gr)if(gr.hasOwnProperty(h))if(h==="1"){var m,n=c,p={op:!0};p=p===void 0?{}:p;m=fr(n)?n.gdprApplies===!1?!0:n.tcString==="tcunavailable"?!p.idpcApplies:(p.idpcApplies||n.gdprApplies!==void 0||p.op)&&(p.idpcApplies||typeof n.tcString==="string"&&n.tcString.length)?dr(n,"1",0):!0:!1;g["1"]=m}else g[h]=dr(c,h,gr[h]);d=g}if(d){a.tcString=c.tcString||"tcempty";a.purposes=d;var q={},
r=(q[K.m.U]=a.purposes["1"]?"granted":"denied",q);a.gdprApplies!==!0?(qp([K.m.U,K.m.La,K.m.V]),Rm().active=!0):(r[K.m.La]=a.purposes["3"]&&a.purposes["4"]?"granted":"denied",typeof a.tcfPolicyVersion==="number"&&a.tcfPolicyVersion>=4?r[K.m.V]=a.purposes["1"]&&a.purposes["7"]?"granted":"denied":qp([K.m.V]),kp(r,{eventId:0},{gdprApplies:a?a.gdprApplies:void 0,tcString:or()||""}))}}else qp([K.m.U,K.m.La,K.m.V])})}catch(c){mr(a),qp([K.m.U,K.m.La,K.m.V]),Rm().active=!0}}}
function mr(a){a.type="e";a.tcString="tcunavailable"}function nr(a){return a.eventStatus==="tcloaded"||a.eventStatus==="useractioncomplete"||a.eventStatus==="cmpuishown"}function kr(){return x.gtag_enable_tcf_support===!0}function lr(){return hr().enableAdvertiserConsentMode===!0}function or(){var a=hr();if(a.active)return a.tcString}function pr(){var a=hr();if(a.active&&a.gdprApplies!==void 0)return a.gdprApplies?"1":"0"}
function qr(a){if(!gr.hasOwnProperty(String(a)))return!0;var b=hr();return b.active&&b.purposes?!!b.purposes[String(a)]:!0};var rr=[K.m.U,K.m.ja,K.m.V,K.m.La],sr={},tr=(sr[K.m.U]=1,sr[K.m.ja]=2,sr);function ur(a){if(a===void 0)return 0;switch(O(a,K.m.Ea)){case void 0:return 1;case !1:return 3;default:return 2}}function vr(){return(F(183)?Yi.vp:Yi.wp).indexOf(lo())!==-1&&tc.globalPrivacyControl===!0}function wr(a){if(vr())return!1;var b=ur(a);if(b===3)return!1;switch($m(K.m.La)){case 1:case 3:return!0;case 2:return!1;case 4:return b===2;case 0:return!0;default:return!1}}
function xr(){return bn()||!Zm(K.m.U)||!Zm(K.m.ja)}function yr(){var a={},b;for(b in tr)tr.hasOwnProperty(b)&&(a[tr[b]]=$m(b));return"G1"+bf(a[1]||0)+bf(a[2]||0)}var zr={},Ar=(zr[K.m.U]=0,zr[K.m.ja]=1,zr[K.m.V]=2,zr[K.m.La]=3,zr);function Br(a){switch(a){case void 0:return 1;case !0:return 3;case !1:return 2;default:return 0}}
function Cr(a){for(var b="1",c=0;c<rr.length;c++){var d=b,e,f=rr[c],g=Ym.delegatedConsentTypes[f];e=g===void 0?0:Ar.hasOwnProperty(g)?12|Ar[g]:8;var h=Rm();h.accessedAny=!0;var m=h.entries[f]||{};e=e<<2|Br(m.implicit);b=d+(""+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[e]+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[Br(m.declare)<<4|Br(m.default)<<2|Br(m.update)])}var n=b,p=(vr()?1:0)<<3,q=(bn()?1:0)<<2,r=ur(a);b=n+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[p|
q|r];return b+=""+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[Ym.containerScopedDefaults.ad_storage<<4|Ym.containerScopedDefaults.analytics_storage<<2|Ym.containerScopedDefaults.ad_user_data]+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[(Ym.usedContainerScopedDefaults?1:0)<<2|Ym.containerScopedDefaults.ad_personalization]}
function Dr(){if(!Zm(K.m.V))return"-";for(var a=Object.keys(Eo),b={},c=l(a),d=c.next();!d.done;d=c.next()){var e=d.value;b[e]=Ym.corePlatformServices[e]!==!1}for(var f="",g=l(a),h=g.next();!h.done;h=g.next()){var m=h.value;b[m]&&(f+=Eo[m])}(Ym.usedCorePlatformServices?Ym.selectedAllCorePlatformServices:1)&&(f+="o");return f||"-"}function Er(){return no()||(kr()||lr())&&pr()==="1"?"1":"0"}function Fr(){return(no()?!0:!(!kr()&&!lr())&&pr()==="1")||!Zm(K.m.V)}
function Gr(){var a="0",b="0",c;var d=hr();c=d.active?d.cmpId:void 0;typeof c==="number"&&c>=0&&c<=4095&&(a="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[c>>6&63],b="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[c&63]);var e="0",f;var g=hr();f=g.active?g.tcfPolicyVersion:void 0;typeof f==="number"&&f>=0&&f<=63&&(e="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[f]);var h=0;no()&&(h|=1);pr()==="1"&&(h|=2);kr()&&(h|=4);var m;var n=hr();m=n.enableAdvertiserConsentMode!==
void 0?n.enableAdvertiserConsentMode?"1":"0":void 0;m==="1"&&(h|=8);Rm().waitPeriodTimedOut&&(h|=16);return"1"+a+b+e+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[h]}function Hr(){return lo()==="US-CO"};var fg;function Ir(){var a=!1;return a}function Jr(){F(212)&&dk&&gg("all",function(a,b,c){var d=c.options;switch(b){case "detect_link_click_events":case "detect_form_submit_events":return(d==null?void 0:d.waitForTags)!==!0;case "detect_youtube_activity_events":return(d==null?void 0:d.fixMissingApi)!==!0;default:return!0}})};var Kr;function Lr(){if(wc===null)return 0;var a=$c();if(!a)return 0;var b=a.getEntriesByName(wc,"resource")[0];if(!b)return 0;switch(b.deliveryType){case "":return 1;case "cache":return 2;case "navigational-prefetch":return 3;default:return 0}}var Mr={UA:1,AW:2,DC:3,G:4,GF:5,GT:12,GTM:14,HA:6,MC:7};
function Nr(a){a=a===void 0?{}:a;var b=jg.ctid.split("-")[0].toUpperCase(),c={ctid:jg.ctid,yj:Vj,Cj:Uj,fm:ym.pe?2:1,Aq:a.Dm,ve:jg.canonicalContainerId};if(F(210)){var d;c.qq=(d=Fm())==null?void 0:d.canonicalContainerId}if(F(204)){var e;c.Ko=(e=Kr)!=null?e:Kr=Lr()}c.ve!==a.Ma&&(c.Ma=a.Ma);var f=Dm();c.qm=f?f.canonicalContainerId:void 0;dk?(c.Uc=Mr[b],c.Uc||(c.Uc=0)):c.Uc=ek?13:10;Pj.C?(c.Sc=0,c.Rl=2):Pj.N?c.Sc=1:Ir()?c.Sc=2:c.Sc=3;var g={6:!1};Pj.H===2?g[7]=!0:Pj.H===1&&(g[2]=!0);if(wc){var h=Ok(Uk(wc),
"host");h&&(g[8]=h.match(/^(www\.)?googletagmanager\.com$/)===null)}c.Tl=g;return ef(c,a.oh)}
function Or(){if(!F(192))return Nr();if(F(193))return ef({yj:Vj,Cj:Uj});var a=jg.ctid.split("-")[0].toUpperCase(),b={ctid:jg.ctid,yj:Vj,Cj:Uj,fm:ym.pe?2:1,ve:jg.canonicalContainerId},c=Dm();b.qm=c?c.canonicalContainerId:void 0;dk?(b.Uc=Mr[a],b.Uc||(b.Uc=0)):b.Uc=ek?13:10;Pj.C?(b.Sc=0,b.Rl=2):Pj.N?b.Sc=1:Ir()?b.Sc=2:b.Sc=3;var d={6:!1};Pj.H===2?d[7]=!0:Pj.H===1&&(d[2]=!0);if(wc){var e=Ok(Uk(wc),"host");e&&(d[8]=e.match(/^(www\.)?googletagmanager\.com$/)===null)}b.Tl=d;return ef(b)};function Pr(a,b,c,d){var e,f=Number(a.Bc!=null?a.Bc:void 0);f!==0&&(e=new Date((b||zb())+1E3*(f||7776E3)));return{path:a.path,domain:a.domain,flags:a.flags,encode:!!c,expires:e,Dc:d}};var Qr=["ad_storage","ad_user_data"];function Rr(a,b){if(!a)return db("TAGGING",32),10;if(b===null||b===void 0||b==="")return db("TAGGING",33),11;var c=Sr(!1);if(c.error!==0)return db("TAGGING",34),c.error;if(!c.value)return db("TAGGING",35),2;c.value[a]=b;var d=Tr(c);d!==0&&db("TAGGING",36);return d}
function Ur(a){if(!a)return db("TAGGING",27),{error:10};var b=Sr();if(b.error!==0)return db("TAGGING",29),b;if(!b.value)return db("TAGGING",30),{error:2};if(!(a in b.value))return db("TAGGING",31),{value:void 0,error:15};var c=b.value[a];return c===null||c===void 0||c===""?(db("TAGGING",28),{value:void 0,error:11}):{value:c,error:0}}
function Sr(a){a=a===void 0?!0:a;if(!Zm(Qr))return db("TAGGING",43),{error:3};try{if(!x.localStorage)return db("TAGGING",44),{error:1}}catch(f){return db("TAGGING",45),{error:14}}var b={schema:"gcl",version:1},c=void 0;try{c=x.localStorage.getItem("_gcl_ls")}catch(f){return db("TAGGING",46),{error:13}}try{if(c){var d=JSON.parse(c);if(d&&typeof d==="object")b=d;else return db("TAGGING",47),{error:12}}}catch(f){return db("TAGGING",48),{error:8}}if(b.schema!=="gcl")return db("TAGGING",49),{error:4};
if(b.version!==1)return db("TAGGING",50),{error:5};try{var e=Vr(b);a&&e&&Tr({value:b,error:0})}catch(f){return db("TAGGING",48),{error:8}}return{value:b,error:0}}
function Vr(a){if(!a||typeof a!=="object")return!1;if("expires"in a&&"value"in a){var b;typeof a.expires==="number"?b=a.expires:b=typeof a.expires==="string"?Number(a.expires):NaN;if(isNaN(b)||!(Date.now()<=b))return a.value=null,a.error=9,db("TAGGING",54),!0}else{for(var c=!1,d=l(Object.keys(a)),e=d.next();!e.done;e=d.next())c=Vr(a[e.value])||c;return c}return!1}
function Tr(a){if(a.error)return a.error;if(!a.value)return db("TAGGING",42),2;var b=a.value,c;try{c=JSON.stringify(b)}catch(d){return db("TAGGING",52),6}try{x.localStorage.setItem("_gcl_ls",c)}catch(d){return db("TAGGING",53),7}return 0};var Wr={oj:"value",Gb:"conversionCount"},Xr=[Wr,{dm:9,xm:10,oj:"timeouts",Gb:"timeouts"}];function Yr(){var a=Wr;if(!Zr(a))return{};var b=$r(Xr),c=b[a.Gb];if(c===void 0||c===-1)return b;var d={},e=Object.assign({},b,(d[a.Gb]=c+1,d));return as(e)?e:b}
function $r(a){var b;a:{var c=Ur("gcl_ctr");if(c.error===0&&c.value&&typeof c.value==="object"){var d=c.value;try{b="value"in d&&typeof d.value==="object"?d.value:void 0;break a}catch(p){}}b=void 0}for(var e=b,f={},g=l(a),h=g.next();!h.done;h=g.next()){var m=h.value;if(e&&Zr(m)){var n=e[m.oj];n===void 0||Number.isNaN(n)?f[m.Gb]=-1:f[m.Gb]=Number(n)}else f[m.Gb]=-1}return f}
function as(a,b){b=b||{};for(var c=zb(),d=Pr(b,c,!0),e={},f=l(Xr),g=f.next();!g.done;g=f.next()){var h=g.value,m=a[h.Gb];m!==void 0&&m!==-1&&(e[h.oj]=m)}e.creationTimeMs=c;return Rr("gcl_ctr",{value:e,expires:Number(d.expires)})===0?!0:!1}function Zr(a){return Zm(["ad_storage","ad_user_data"])?!a.xm||Ka(a.xm):!1}function bs(a){return Zm(["ad_storage","ad_user_data"])?!a.dm||Ka(a.dm):!1};function cs(a){var b=1,c,d,e;if(a)for(b=0,d=a.length-1;d>=0;d--)e=a.charCodeAt(d),b=(b<<6&268435455)+e+(e<<14),c=b&266338304,b=c!==0?b^c>>21:b;return b};var ds={O:{po:0,Jj:1,rg:2,Pj:3,Ih:4,Nj:5,Oj:6,Qj:7,Jh:8,Uk:9,Tk:10,si:11,Vk:12,Vg:13,Yk:14,Lf:15,no:16,te:17,Mi:18,Ni:19,Oi:20,Jl:21,Pi:22,Lh:23,Xj:24}};ds.O[ds.O.po]="RESERVED_ZERO";ds.O[ds.O.Jj]="ADS_CONVERSION_HIT";ds.O[ds.O.rg]="CONTAINER_EXECUTE_START";ds.O[ds.O.Pj]="CONTAINER_SETUP_END";ds.O[ds.O.Ih]="CONTAINER_SETUP_START";ds.O[ds.O.Nj]="CONTAINER_BLOCKING_END";ds.O[ds.O.Oj]="CONTAINER_EXECUTE_END";ds.O[ds.O.Qj]="CONTAINER_YIELD_END";ds.O[ds.O.Jh]="CONTAINER_YIELD_START";ds.O[ds.O.Uk]="EVENT_EXECUTE_END";
ds.O[ds.O.Tk]="EVENT_EVALUATION_END";ds.O[ds.O.si]="EVENT_EVALUATION_START";ds.O[ds.O.Vk]="EVENT_SETUP_END";ds.O[ds.O.Vg]="EVENT_SETUP_START";ds.O[ds.O.Yk]="GA4_CONVERSION_HIT";ds.O[ds.O.Lf]="PAGE_LOAD";ds.O[ds.O.no]="PAGEVIEW";ds.O[ds.O.te]="SNIPPET_LOAD";ds.O[ds.O.Mi]="TAG_CALLBACK_ERROR";ds.O[ds.O.Ni]="TAG_CALLBACK_FAILURE";ds.O[ds.O.Oi]="TAG_CALLBACK_SUCCESS";ds.O[ds.O.Jl]="TAG_EXECUTE_END";ds.O[ds.O.Pi]="TAG_EXECUTE_START";ds.O[ds.O.Lh]="CUSTOM_PERFORMANCE_START";ds.O[ds.O.Xj]="CUSTOM_PERFORMANCE_END";var es=[],fs={},gs={};var hs=["2"];function is(a){return a.origin!=="null"};function js(a,b,c){for(var d=[],e=b.split(";"),f=function(p){return Ka(11)?p.trim():p.replace(/^\s*|\s*$/g,"")},g=0;g<e.length;g++){var h=e[g].split("="),m=f(h[0]);if(m&&m===a){var n=f(h.slice(1).join("="));n&&c&&(n=decodeURIComponent(n));d.push(n)}}return d};var ks;function ls(a,b,c,d){return ms(d)?js(a,String(b||ns()),c):[]}function os(a,b,c,d,e){if(ms(e)){var f=ps(a,d,e);if(f.length===1)return f[0];if(f.length!==0){f=qs(f,function(g){return g.Xo},b);if(f.length===1)return f[0];f=qs(f,function(g){return g.Zp},c);return f[0]}}}function rs(a,b,c,d){var e=ns(),f=window;is(f)&&(f.document.cookie=a);var g=ns();return e!==g||c!==void 0&&ls(b,g,!1,d).indexOf(c)>=0}
function ss(a,b,c,d){function e(w,y,A){if(A==null)return delete h[y],w;h[y]=A;return w+"; "+y+"="+A}function f(w,y){if(y==null)return w;h[y]=!0;return w+"; "+y}if(!ms(c.Dc))return 2;var g;b==null?g=a+"=deleted; expires="+(new Date(0)).toUTCString():(c.encode&&(b=encodeURIComponent(b)),b=ts(b),g=a+"="+b);var h={};g=e(g,"path",c.path);var m;c.expires instanceof Date?m=c.expires.toUTCString():c.expires!=null&&(m=""+c.expires);g=e(g,"expires",m);g=e(g,"max-age",c.Vp);g=e(g,"samesite",c.rq);c.secure&&
(g=f(g,"secure"));var n=c.domain;if(n&&n.toLowerCase()==="auto"){for(var p=us(),q=void 0,r=!1,t=0;t<p.length;++t){var u=p[t]!=="none"?p[t]:void 0,v=e(g,"domain",u);v=f(v,c.flags);try{d&&d(a,h)}catch(w){q=w;continue}r=!0;if(!vs(u,c.path)&&rs(v,a,b,c.Dc))return Ka(19)&&(ks=u),0}if(q&&!r)throw q;return 1}n&&n.toLowerCase()!=="none"&&(g=e(g,"domain",n));g=f(g,c.flags);d&&d(a,h);return vs(n,c.path)?1:rs(g,a,b,c.Dc)?0:1}
function ws(a,b,c){c.path==null&&(c.path="/");c.domain||(c.domain="auto");if(es.includes("2")){var d;(d=$c())==null||d.mark("2-"+ds.O.Lh+"-"+(gs["2"]||0))}var e=ss(a,b,c);if(es.includes("2")){var f="2-"+ds.O.Xj+"-"+(gs["2"]||0),g={start:"2-"+ds.O.Lh+"-"+(gs["2"]||0),end:f},h;(h=$c())==null||h.mark(f);var m,n,p=(n=(m=$c())==null?void 0:m.measure(f,g))==null?void 0:n.duration;p!==void 0&&(gs["2"]=(gs["2"]||0)+1,fs["2"]=p+(fs["2"]||0))}return e}
function qs(a,b,c){for(var d=[],e=[],f,g=0;g<a.length;g++){var h=a[g],m=b(h);m===c?d.push(h):f===void 0||m<f?(e=[h],f=m):m===f&&e.push(h)}return d.length>0?d:e}function ps(a,b,c){for(var d=[],e=ls(a,void 0,void 0,c),f=0;f<e.length;f++){var g=e[f].split("."),h=g.shift();if(!b||!h||b.indexOf(h)!==-1){var m=g.shift();if(m){var n=m.split("-");d.push({Oo:e[f],Po:g.join("."),Xo:Number(n[0])||1,Zp:Number(n[1])||1})}}}return d}function ts(a){a&&a.length>1200&&(a=a.substring(0,1200));return a}
var xs=/^(www\.)?google(\.com?)?(\.[a-z]{2})?$/,ys=/(^|\.)doubleclick\.net$/i;function vs(a,b){return a!==void 0&&(ys.test(window.document.location.hostname)||b==="/"&&xs.test(a))}function zs(a){if(!a)return 1;var b=a;Ka(6)&&a==="none"&&(b=window.document.location.hostname);b=b.indexOf(".")===0?b.substring(1):b;return b.split(".").length}function As(a){if(!a||a==="/")return 1;a[0]!=="/"&&(a="/"+a);a[a.length-1]!=="/"&&(a+="/");return a.split("/").length-1}
function Bs(a,b){var c=""+zs(a),d=As(b);d>1&&(c+="-"+d);return c}
var ns=function(){return is(window)?window.document.cookie:""},ms=function(a){return a&&Ka(7)?(Array.isArray(a)?a:[a]).every(function(b){return an(b)&&Zm(b)}):!0},us=function(){var a=ks,b=[];a&&b.push(a);var c=window.document.location.hostname.split(".");if(c.length===4){var d=c[c.length-1];if(Number(d).toString()===d)return["none"]}for(var e=c.length-2;e>=0;e--){var f=c.slice(e).join(".");f!==a&&b.push(f)}var g=window.document.location.hostname;ys.test(g)||xs.test(g)||b.push("none");return b};function Cs(a){var b=Math.round(Math.random()*2147483647);return a?String(b^cs(a)&2147483647):String(b)}function Ds(a){return[Cs(a),Math.round(zb()/1E3)].join(".")}function Es(a,b,c,d,e){var f=zs(b),g;return(g=os(a,f,As(c),d,e))==null?void 0:g.Po};var Fs;function Gs(){function a(g){c(g.target||g.srcElement||{})}function b(g){d(g.target||g.srcElement||{})}var c=Hs,d=Is,e=Js();if(!e.init){Kc(z,"mousedown",a);Kc(z,"keyup",a);Kc(z,"submit",b);var f=HTMLFormElement.prototype.submit;HTMLFormElement.prototype.submit=function(){d(this);f.call(this)};e.init=!0}}function Ks(a,b,c,d,e){var f={callback:a,domains:b,fragment:c===2,placement:c,forms:d,sameHost:e};Js().decorators.push(f)}
function Ls(a,b,c){for(var d=Js().decorators,e={},f=0;f<d.length;++f){var g=d[f],h;if(h=!c||g.forms)a:{var m=g.domains,n=a,p=!!g.sameHost;if(m&&(p||n!==z.location.hostname))for(var q=0;q<m.length;q++)if(m[q]instanceof RegExp){if(m[q].test(n)){h=!0;break a}}else if(n.indexOf(m[q])>=0||p&&m[q].indexOf(n)>=0){h=!0;break a}h=!1}if(h){var r=g.placement;r===void 0&&(r=g.fragment?2:1);r===b&&Cb(e,g.callback())}}return e}
function Js(){var a=xc("google_tag_data",{}),b=a.gl;b&&b.decorators||(b={decorators:[]},a.gl=b);return b};var Ms=/(.*?)\*(.*?)\*(.*)/,Ns=/^https?:\/\/([^\/]*?)\.?cdn\.ampproject\.org\/?(.*)/,Os=/^(?:www\.|m\.|amp\.)+/,Ps=/([^?#]+)(\?[^#]*)?(#.*)?/;function Qs(a){var b=Ps.exec(a);if(b)return{uj:b[1],query:b[2],fragment:b[3]}}function Rs(a){return new RegExp("(.*?)(^|&)"+a+"=([^&]*)&?(.*)")}
function Ss(a,b){var c=[tc.userAgent,(new Date).getTimezoneOffset(),tc.userLanguage||tc.language,Math.floor(zb()/60/1E3)-(b===void 0?0:b),a].join("*"),d;if(!(d=Fs)){for(var e=Array(256),f=0;f<256;f++){for(var g=f,h=0;h<8;h++)g=g&1?g>>>1^3988292384:g>>>1;e[f]=g}d=e}Fs=d;for(var m=4294967295,n=0;n<c.length;n++)m=m>>>8^Fs[(m^c.charCodeAt(n))&255];return((m^-1)>>>0).toString(36)}
function Ts(a){return function(b){var c=Uk(x.location.href),d=c.search.replace("?",""),e=Lk(d,"_gl",!1,!0)||"";b.query=Us(e)||{};var f=Ok(c,"fragment"),g;var h=-1;if(Eb(f,"_gl="))h=4;else{var m=f.indexOf("&_gl=");m>0&&(h=m+3+2)}if(h<0)g=void 0;else{var n=f.indexOf("&",h);g=n<0?f.substring(h):f.substring(h,n)}b.fragment=Us(g||"")||{};a&&Vs(c,d,f)}}function Ws(a,b){var c=Rs(a).exec(b),d=b;if(c){var e=c[2],f=c[4];d=c[1];f&&(d=d+e+f)}return d}
function Vs(a,b,c){function d(g,h){var m=Ws("_gl",g);m.length&&(m=h+m);return m}if(sc&&sc.replaceState){var e=Rs("_gl");if(e.test(b)||e.test(c)){var f=Ok(a,"path");b=d(b,"?");c=d(c,"#");sc.replaceState({},"",""+f+b+c)}}}function Xs(a,b){var c=Ts(!!b),d=Js();d.data||(d.data={query:{},fragment:{}},c(d.data));var e={},f=d.data;f&&(Cb(e,f.query),a&&Cb(e,f.fragment));return e}
var Us=function(a){try{var b=Ys(a,3);if(b!==void 0){for(var c={},d=b?b.split("*"):[],e=0;e+1<d.length;e+=2){var f=d[e],g=bb(d[e+1]);c[f]=g}db("TAGGING",6);return c}}catch(h){db("TAGGING",8)}};function Ys(a,b){if(a){var c;a:{for(var d=a,e=0;e<3;++e){var f=Ms.exec(d);if(f){c=f;break a}d=Nk(d)||""}c=void 0}var g=c;if(g&&g[1]==="1"){var h=g[3],m;a:{for(var n=g[2],p=0;p<b;++p)if(n===Ss(h,p)){m=!0;break a}m=!1}if(m)return h;db("TAGGING",7)}}}
function Zs(a,b,c,d,e){function f(p){p=Ws(a,p);var q=p.charAt(p.length-1);p&&q!=="&"&&(p+="&");return p+n}d=d===void 0?!1:d;e=e===void 0?!1:e;var g=Qs(c);if(!g)return"";var h=g.query||"",m=g.fragment||"",n=a+"="+b;d?m.substring(1).length!==0&&e||(m="#"+f(m.substring(1))):h="?"+f(h.substring(1));return""+g.uj+h+m}
function $s(a,b){function c(n,p,q){var r;a:{for(var t in n)if(n.hasOwnProperty(t)){r=!0;break a}r=!1}if(r){var u,v=[],w;for(w in n)if(n.hasOwnProperty(w)){var y=n[w];y!==void 0&&y===y&&y!==null&&y.toString()!=="[object Object]"&&(v.push(w),v.push(ab(String(y))))}var A=v.join("*");u=["1",Ss(A),A].join("*");d?(Ka(3)||Ka(1)||!p)&&at("_gl",u,a,p,q):bt("_gl",u,a,p,q)}}var d=(a.tagName||"").toUpperCase()==="FORM",e=Ls(b,1,d),f=Ls(b,2,d),g=Ls(b,4,d),h=Ls(b,3,d);c(e,!1,!1);c(f,!0,!1);Ka(1)&&c(g,!0,!0);for(var m in h)h.hasOwnProperty(m)&&
ct(m,h[m],a)}function ct(a,b,c){c.tagName.toLowerCase()==="a"?bt(a,b,c):c.tagName.toLowerCase()==="form"&&at(a,b,c)}function bt(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;var f;if(f=c.href){var g;if(!(g=!Ka(4)||d)){var h=x.location.href,m=Qs(c.href),n=Qs(h);g=!(m&&n&&m.uj===n.uj&&m.query===n.query&&m.fragment)}f=g}if(f){var p=Zs(a,b,c.href,d,e);ic.test(p)&&(c.href=p)}}
function at(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;if(c){var f=c.getAttribute("action")||"";if(f){var g=(c.method||"").toLowerCase();if(g!=="get"||d){if(g==="get"||g==="post"){var h=Zs(a,b,f,d,e);ic.test(h)&&(c.action=h)}}else{for(var m=c.childNodes||[],n=!1,p=0;p<m.length;p++){var q=m[p];if(q.name===a){q.setAttribute("value",b);n=!0;break}}if(!n){var r=z.createElement("input");r.setAttribute("type","hidden");r.setAttribute("name",a);r.setAttribute("value",b);c.appendChild(r)}}}}}
function Hs(a){try{var b;a:{for(var c=a,d=100;c&&d>0;){if(c.href&&c.nodeName.match(/^a(?:rea)?$/i)){b=c;break a}c=c.parentNode;d--}b=null}var e=b;if(e){var f=e.protocol;f!=="http:"&&f!=="https:"||$s(e,e.hostname)}}catch(g){}}function Is(a){try{var b=a.getAttribute("action");if(b){var c=Ok(Uk(b),"host");$s(a,c)}}catch(d){}}function dt(a,b,c,d){Gs();var e=c==="fragment"?2:1;d=!!d;Ks(a,b,e,d,!1);e===2&&db("TAGGING",23);d&&db("TAGGING",24)}
function et(a,b){Gs();Ks(a,[Qk(x.location,"host",!0)],b,!0,!0)}function ft(){var a=z.location.hostname,b=Ns.exec(z.referrer);if(!b)return!1;var c=b[2],d=b[1],e="";if(c){var f=c.split("/"),g=f[1];e=g==="s"?Nk(f[2])||"":Nk(g)||""}else if(d){if(d.indexOf("xn--")===0)return!1;e=d.replace(/-/g,".").replace(/\.\./g,"-")}var h=a.replace(Os,""),m=e.replace(Os,""),n;if(!(n=h===m)){var p="."+m;n=h.length>=p.length&&h.substring(h.length-p.length,h.length)===p}return n}
function gt(a,b){return a===!1?!1:a||b||ft()};var ht=["1"],it={},jt={};function kt(a,b){b=b===void 0?!0:b;var c=lt(a.prefix);if(it[c])mt(a);else if(nt(c,a.path,a.domain)){var d=jt[lt(a.prefix)]||{id:void 0,Ah:void 0};b&&ot(a,d.id,d.Ah);mt(a)}else{var e=Wk("auiddc");if(e)db("TAGGING",17),it[c]=e;else if(b){var f=lt(a.prefix),g=Ds();pt(f,g,a);nt(c,a.path,a.domain);mt(a,!0)}}}
function mt(a,b){if((b===void 0?0:b)&&Zr(Wr)){var c=Sr(!1);c.error!==0?db("TAGGING",38):c.value?"gcl_ctr"in c.value?(delete c.value.gcl_ctr,Tr(c)!==0&&db("TAGGING",41)):db("TAGGING",40):db("TAGGING",39)}if(bs(Wr)&&$r([Wr])[Wr.Gb]===-1){for(var d={},e=(d[Wr.Gb]=0,d),f=l(Xr),g=f.next();!g.done;g=f.next()){var h=g.value;h!==Wr&&bs(h)&&(e[h.Gb]=0)}as(e,a)}}
function ot(a,b,c){var d=lt(a.prefix),e=it[d];if(e){var f=e.split(".");if(f.length===2){var g=Number(f[1])||0;if(g){var h=e;b&&(h=e+"."+b+"."+(c?c:Math.floor(zb()/1E3)));pt(d,h,a,g*1E3)}}}}function pt(a,b,c,d){var e;e=["1",Bs(c.domain,c.path),b].join(".");var f=Pr(c,d);f.Dc=qt();ws(a,e,f)}function nt(a,b,c){var d=Es(a,b,c,ht,qt());if(!d)return!1;rt(a,d);return!0}
function rt(a,b){var c=b.split(".");c.length===5?(it[a]=c.slice(0,2).join("."),jt[a]={id:c.slice(2,4).join("."),Ah:Number(c[4])||0}):c.length===3?jt[a]={id:c.slice(0,2).join("."),Ah:Number(c[2])||0}:it[a]=b}function lt(a){return(a||"_gcl")+"_au"}function st(a){function b(){Zm(c)&&a()}var c=qt();en(function(){b();Zm(c)||fn(b,c)},c)}
function tt(a){var b=Xs(!0),c=lt(a.prefix);st(function(){var d=b[c];if(d){rt(c,d);var e=Number(it[c].split(".")[1])*1E3;if(e){db("TAGGING",16);var f=Pr(a,e);f.Dc=qt();var g=["1",Bs(a.domain,a.path),d].join(".");ws(c,g,f)}}})}function ut(a,b,c,d,e){e=e||{};var f=function(){var g={},h=Es(a,e.path,e.domain,ht,qt());h&&(g[a]=h);return g};st(function(){dt(f,b,c,d)})}function qt(){return["ad_storage","ad_user_data"]};function vt(a){for(var b=[],c=z.cookie.split(";"),d=new RegExp("^\\s*"+(a||"_gac")+"_(UA-\\d+-\\d+)=\\s*(.+?)\\s*$"),e=0;e<c.length;e++){var f=c[e].match(d);f&&b.push({Fj:f[1],value:f[2],timestamp:Number(f[2].split(".")[1])||0})}b.sort(function(g,h){return h.timestamp-g.timestamp});return b}
function wt(a,b){var c=vt(a),d={};if(!c||!c.length)return d;for(var e=0;e<c.length;e++){var f=c[e].value.split(".");if(!(f[0]!=="1"||b&&f.length<3||!b&&f.length!==3)&&Number(f[1])){d[c[e].Fj]||(d[c[e].Fj]=[]);var g={version:f[0],timestamp:Number(f[1])*1E3,gclid:f[2]};b&&f.length>3&&(g.labels=f.slice(3));d[c[e].Fj].push(g)}}return d};var xt={},zt=(xt.k={da:/^[\w-]+$/},xt.b={da:/^[\w-]+$/,zj:!0},xt.i={da:/^[1-9]\d*$/},xt.h={da:/^\d+$/},xt.t={da:/^[1-9]\d*$/},xt.d={da:/^[A-Za-z0-9_-]+$/},xt.j={da:/^\d+$/},xt.u={da:/^[1-9]\d*$/},xt.l={da:/^[01]$/},xt.o={da:/^[1-9]\d*$/},xt.g={da:/^[01]$/},xt.s={da:/^.+$/},xt);var At={},Et=(At[5]={Fh:{2:Bt},nj:"2",ph:["k","i","b","u"]},At[4]={Fh:{2:Bt,GCL:Ct},nj:"2",ph:["k","i","b"]},At[2]={Fh:{GS2:Bt,GS1:Dt},nj:"GS2",ph:"sogtjlhd".split("")},At);function Ft(a,b,c){var d=Et[b];if(d){var e=a.split(".")[0];c==null||c(e);if(e){var f=d.Fh[e];if(f)return f(a,b)}}}
function Bt(a,b){var c=a.split(".");if(c.length===3){var d=c[2];if(d.indexOf("$")===-1&&d.indexOf("%24")!==-1)try{d=decodeURIComponent(d)}catch(t){}var e={},f=Et[b];if(f){for(var g=f.ph,h=l(d.split("$")),m=h.next();!m.done;m=h.next()){var n=m.value,p=n[0];if(g.indexOf(p)!==-1)try{var q=decodeURIComponent(n.substring(1)),r=zt[p];r&&(r.zj?(e[p]=e[p]||[],e[p].push(q)):e[p]=q)}catch(t){}}return e}}}function Gt(a,b,c){var d=Et[b];if(d)return[d.nj,c||"1",Ht(a,b)].join(".")}
function Ht(a,b){var c=Et[b];if(c){for(var d=[],e=l(c.ph),f=e.next();!f.done;f=e.next()){var g=f.value,h=zt[g];if(h){var m=a[g];if(m!==void 0)if(h.zj&&Array.isArray(m))for(var n=l(m),p=n.next();!p.done;p=n.next())d.push(encodeURIComponent(""+g+p.value));else d.push(encodeURIComponent(""+g+m))}}return d.join("$")}}function Ct(a){var b=a.split(".");b.shift();var c=b.shift(),d=b.shift(),e={};return e.k=d,e.i=c,e.b=b,e}
function Dt(a){var b=a.split(".").slice(2);if(!(b.length<5||b.length>7)){var c={};return c.s=b[0],c.o=b[1],c.g=b[2],c.t=b[3],c.j=b[4],c.l=b[5],c.h=b[6],c}};var It=new Map([[5,"ad_storage"],[4,["ad_storage","ad_user_data"]],[2,"analytics_storage"]]);function Jt(a,b,c){if(Et[b]){for(var d=[],e=ls(a,void 0,void 0,It.get(b)),f=l(e),g=f.next();!g.done;g=f.next()){var h=Ft(g.value,b,c);h&&d.push(Kt(h))}return d}}function Lt(a,b,c,d,e){d=d||{};var f=Bs(d.domain,d.path),g=Gt(b,c,f);if(!g)return 1;var h=Pr(d,e,void 0,It.get(c));return ws(a,g,h)}function Mt(a,b){var c=b.da;return typeof c==="function"?c(a):c.test(a)}
function Kt(a){for(var b=l(Object.keys(a)),c=b.next(),d={};!c.done;d={Tf:void 0},c=b.next()){var e=c.value,f=a[e];d.Tf=zt[e];d.Tf?d.Tf.zj?a[e]=Array.isArray(f)?f.filter(function(g){return function(h){return Mt(h,g.Tf)}}(d)):void 0:typeof f==="string"&&Mt(f,d.Tf)||(a[e]=void 0):a[e]=void 0}return a};var Nt=function(){this.value=0};Nt.prototype.set=function(a){return this.value|=1<<a};var Ot=function(a,b){b<=0||(a.value|=1<<b-1)};Nt.prototype.get=function(){return this.value};Nt.prototype.clear=function(a){this.value&=~(1<<a)};Nt.prototype.clearAll=function(){this.value=0};Nt.prototype.equals=function(a){return this.value===a.value};function Pt(a){if(a)try{return new Uint8Array(atob(a.replace(/-/g,"+").replace(/_/g,"/")).split("").map(function(b){return b.charCodeAt(0)}))}catch(b){}}function Qt(a,b){var c=0,d=0,e,f=b;do{if(f>=a.length)return;e=a[f++];c|=(e&127)<<d;d+=7}while(e&128);return[c,f]};function Rt(){var a=String,b=x.location.hostname,c=x.location.pathname,d=b=Nb(b);d.split(".").length>2&&(d=d.replace(/^(www[0-9]*|web|ftp|wap|home|m|w|amp|mobile)\./,""));b=d;c=Nb(c);var e=c.split(";")[0];e=e.replace(/\/(ar|slp|web|index)?\/?$/,"");return a(cs((""+b+e).toLowerCase()))};var St={},Tt=(St.gclid=!0,St.dclid=!0,St.gbraid=!0,St.wbraid=!0,St),Ut=/^\w+$/,Vt=/^[\w-]+$/,Wt={},Xt=(Wt.aw="_aw",Wt.dc="_dc",Wt.gf="_gf",Wt.gp="_gp",Wt.gs="_gs",Wt.ha="_ha",Wt.ag="_ag",Wt.gb="_gb",Wt),Yt=/^(?:www\.)?google(?:\.com?)?(?:\.[a-z]{2}t?)?$/,Zt=/^www\.googleadservices\.com$/;function $t(){return["ad_storage","ad_user_data"]}function au(a){return!Ka(7)||Zm(a)}function bu(a,b){function c(){var d=au(b);d&&a();return d}en(function(){c()||fn(c,b)},b)}
function cu(a){return du(a).map(function(b){return b.gclid})}function eu(a){return fu(a).filter(function(b){return b.gclid}).map(function(b){return b.gclid})}function fu(a){var b=gu(a.prefix),c=hu("gb",b),d=hu("ag",b);if(!d||!c)return[];var e=function(h){return function(m){m.type=h;return m}},f=du(c).map(e("gb")),g=iu(d).map(e("ag"));return f.concat(g).sort(function(h,m){return m.timestamp-h.timestamp})}
function ju(a,b,c,d,e,f){var g=nb(a,function(h){return h.gclid===c});g?(g.timestamp<d&&(g.timestamp=d,g.Fd=f),g.labels=ku(g.labels||[],e||[])):a.push({version:b,gclid:c,timestamp:d,labels:e,Fd:f})}function iu(a){for(var b=Jt(a,5)||[],c=[],d=l(b),e=d.next();!e.done;e=d.next()){var f=e.value,g=f,h=lu(f);h&&ju(c,"2",g.k,h,g.b||[],f.u)}return c.sort(function(m,n){return n.timestamp-m.timestamp})}
function du(a){for(var b=[],c=ls(a,z.cookie,void 0,$t()),d=l(c),e=d.next();!e.done;e=d.next()){var f=mu(e.value);if(f!=null){var g=f;ju(b,g.version,g.gclid,g.timestamp,g.labels)}}b.sort(function(h,m){return m.timestamp-h.timestamp});return nu(b)}function ou(a,b){for(var c=[],d=l(a),e=d.next();!e.done;e=d.next()){var f=e.value;c.includes(f)||c.push(f)}for(var g=l(b),h=g.next();!h.done;h=g.next()){var m=h.value;c.includes(m)||c.push(m)}return c}
function pu(a,b,c){c=c===void 0?!1:c;for(var d,e,f=l(a),g=f.next();!g.done;g=f.next()){var h=g.value;if(h.gclid===b.gclid){d=h;break}h.Ka&&b.Ka&&h.Ka.equals(b.Ka)&&(e=h)}if(d){var m,n,p=(m=d.Ka)!=null?m:new Nt,q=(n=b.Ka)!=null?n:new Nt;p.value|=q.value;d.Ka=p;d.timestamp<b.timestamp&&(d.timestamp=b.timestamp,d.Fd=b.Fd);d.labels=ou(d.labels||[],b.labels||[]);d.Bb=ou(d.Bb||[],b.Bb||[])}else c&&e?Object.assign(e,b):a.push(b)}
function qu(a){if(!a)return new Nt;var b=new Nt;if(a===1)return Ot(b,2),Ot(b,3),b;Ot(b,a);return b}
function ru(){var a=Ur("gclid");if(!a||a.error||!a.value||typeof a.value!=="object")return null;var b=a.value;try{if(!("value"in b&&b.value)||typeof b.value!=="object")return null;var c=b.value,d=c.value;if(!d||!d.match(Vt))return null;var e=c.linkDecorationSource,f=c.linkDecorationSources,g=new Nt;typeof e==="number"?g=qu(e):typeof f==="number"&&(g.value=f);return{version:"",gclid:d,timestamp:Number(c.creationTimeMs)||0,labels:[],Ka:g,Bb:[2]}}catch(h){return null}}
function su(){var a=Ur("gcl_aw");if(a.error!==0)return null;try{return a.value.reduce(function(b,c){if(!c.value||typeof c.value!=="object")return b;var d=c.value,e=d.value;if(!e||!e.match(Vt))return b;var f=new Nt,g=d.linkDecorationSources;typeof g==="number"&&(f.value=g);b.push({version:"",gclid:e,timestamp:Number(d.creationTimeMs)||0,expires:Number(c.expires)||0,labels:[],Ka:f,Bb:[2]});return b},[])}catch(b){return null}}
function tu(a){for(var b=[],c=ls(a,z.cookie,void 0,$t()),d=l(c),e=d.next();!e.done;e=d.next()){var f=mu(e.value);f!=null&&(f.Fd=void 0,f.Ka=new Nt,f.Bb=[1],pu(b,f))}var g=ru();g&&(g.Fd=void 0,g.Bb=g.Bb||[2],pu(b,g));if(Ka(13)){var h=su();if(h)for(var m=l(h),n=m.next();!n.done;n=m.next()){var p=n.value;p.Fd=void 0;p.Bb=p.Bb||[2];pu(b,p)}}b.sort(function(q,r){return r.timestamp-q.timestamp});return nu(b)}
function ku(a,b){if(!a.length)return b;if(!b.length)return a;var c={};return a.concat(b).filter(function(d){return c.hasOwnProperty(d)?!1:c[d]=!0})}function gu(a){return a&&typeof a==="string"&&a.match(Ut)?a:"_gcl"}function uu(a,b){if(a){var c={value:a,Ka:new Nt};Ot(c.Ka,b);return c}}
function vu(a,b,c){var d=Uk(a),e=Ok(d,"query",!1,void 0,"gclsrc"),f=uu(Ok(d,"query",!1,void 0,"gclid"),c?4:2);if(b&&(!f||!e)){var g=d.hash.replace("#","");f||(f=uu(Lk(g,"gclid",!1),3));e||(e=Lk(g,"gclsrc",!1))}return!f||e!==void 0&&e!=="aw"&&e!=="aw.ds"?[]:[f]}
function wu(a,b){var c=Uk(a),d=Ok(c,"query",!1,void 0,"gclid"),e=Ok(c,"query",!1,void 0,"gclsrc"),f=Ok(c,"query",!1,void 0,"wbraid");f=Lb(f);var g=Ok(c,"query",!1,void 0,"gbraid"),h=Ok(c,"query",!1,void 0,"gad_source"),m=Ok(c,"query",!1,void 0,"dclid");if(b&&!(d&&e&&f&&g)){var n=c.hash.replace("#","");d=d||Lk(n,"gclid",!1);e=e||Lk(n,"gclsrc",!1);f=f||Lk(n,"wbraid",!1);g=g||Lk(n,"gbraid",!1);h=h||Lk(n,"gad_source",!1)}return xu(d,e,m,f,g,h)}function yu(){return wu(x.location.href,!0)}
function xu(a,b,c,d,e,f){var g={},h=function(m,n){g[n]||(g[n]=[]);g[n].push(m)};g.gclid=a;g.gclsrc=b;g.dclid=c;if(a!==void 0&&a.match(Vt))switch(b){case void 0:h(a,"aw");break;case "aw.ds":h(a,"aw");h(a,"dc");break;case "ds":h(a,"dc");break;case "3p.ds":h(a,"dc");break;case "gf":h(a,"gf");break;case "ha":h(a,"ha")}c&&h(c,"dc");d!==void 0&&Vt.test(d)&&(g.wbraid=d,h(d,"gb"));e!==void 0&&Vt.test(e)&&(g.gbraid=e,h(e,"ag"));f!==void 0&&Vt.test(f)&&(g.gad_source=f,h(f,"gs"));return g}
function zu(a){for(var b=yu(),c=!0,d=l(Object.keys(b)),e=d.next();!e.done;e=d.next())if(b[e.value]!==void 0){c=!1;break}c&&(b=wu(x.document.referrer,!1),b.gad_source=void 0);Au(b,!1,a)}
function Bu(a){zu(a);var b=vu(x.location.href,!0,!1);b.length||(b=vu(x.document.referrer,!1,!0));a=a||{};Cu(a);if(b.length){var c=b[0],d=zb(),e=Pr(a,d,!0),f=$t(),g=function(){au(f)&&e.expires!==void 0&&Rr("gclid",{value:{value:c.value,creationTimeMs:d,linkDecorationSources:c.Ka.get()},expires:Number(e.expires)})};en(function(){g();au(f)||fn(g,f)},f)}}
function Cu(a){var b;if(b=Ka(14)){var c=Du();b=Yt.test(c)||Zt.test(c)||Eu()}if(b){var d;a:{for(var e=Uk(x.location.href),f=Mk(Ok(e,"query")),g=l(Object.keys(f)),h=g.next();!h.done;h=g.next()){var m=h.value;if(!Tt[m]){var n=f[m][0]||"",p;if(!n||n.length<50||n.length>200)p=!1;else{var q=Pt(n),r;if(q)c:{var t=q;if(t&&t.length!==0){var u=0;try{for(;u<t.length;){var v=Qt(t,u);if(v===void 0)break;var w=l(v),y=w.next().value,A=w.next().value,C=y,E=A,G=C&7;if(C>>3===16382){if(G!==0)break;var I=Qt(t,E);if(I===
void 0)break;r=l(I).next().value===1;break c}var N;d:{var T=void 0,ca=t,P=E;switch(G){case 0:N=(T=Qt(ca,P))==null?void 0:T[1];break d;case 1:N=P+8;break d;case 2:var ha=Qt(ca,P);if(ha===void 0)break;var da=l(ha),ka=da.next().value;N=da.next().value+ka;break d;case 5:N=P+4;break d}N=void 0}if(N===void 0||N>t.length)break;u=N}}catch(W){}}r=!1}else r=!1;p=r}if(p){d=n;break a}}}d=void 0}var X=d;X&&Fu(X,7,a)}}
function Fu(a,b,c){c=c||{};var d=zb(),e=Pr(c,d,!0),f=$t(),g=function(){if(au(f)&&e.expires!==void 0){var h=su()||[];pu(h,{version:"",gclid:a,timestamp:d,expires:Number(e.expires),Ka:qu(b)},!0);Rr("gcl_aw",h.map(function(m){return{value:{value:m.gclid,creationTimeMs:m.timestamp,linkDecorationSources:m.Ka?m.Ka.get():0},expires:Number(m.expires)}}))}};en(function(){au(f)?g():fn(g,f)},f)}
function Au(a,b,c,d,e){c=c||{};e=e||[];var f=gu(c.prefix),g=d||zb(),h=Math.round(g/1E3),m=$t(),n=!1,p=!1,q=function(){if(au(m)){var r=Pr(c,g,!0);r.Dc=m;for(var t=function(T,ca){var P=hu(T,f);P&&(ws(P,ca,r),T!=="gb"&&(n=!0))},u=function(T){var ca=["GCL",h,T];e.length>0&&ca.push(e.join("."));return ca.join(".")},v=l(["aw","dc","gf","ha","gp"]),w=v.next();!w.done;w=v.next()){var y=w.value;a[y]&&t(y,u(a[y][0]))}if(!n&&a.gb){var A=a.gb[0],C=hu("gb",f);!b&&du(C).some(function(T){return T.gclid===A&&T.labels&&
T.labels.length>0})||t("gb",u(A))}}if(!p&&a.gbraid&&au("ad_storage")&&(p=!0,!n)){var E=a.gbraid,G=hu("ag",f);if(b||!iu(G).some(function(T){return T.gclid===E&&T.labels&&T.labels.length>0})){var I={},N=(I.k=E,I.i=""+h,I.b=e,I);Lt(G,N,5,c,g)}}Gu(a,f,g,c)};en(function(){q();au(m)||fn(q,m)},m)}
function Gu(a,b,c,d){if(a.gad_source!==void 0&&au("ad_storage")){var e=Zc();if(e!=="r"&&e!=="h"){var f=a.gad_source,g=hu("gs",b);if(g){var h=Math.floor((zb()-(Yc()||0))/1E3),m,n=Rt(),p={};m=(p.k=f,p.i=""+h,p.u=n,p);Lt(g,m,5,d,c)}}}}
function Hu(a,b){var c=Xs(!0);bu(function(){for(var d=gu(b.prefix),e=0;e<a.length;++e){var f=a[e];if(Xt[f]!==void 0){var g=hu(f,d),h=c[g];if(h){var m=Math.min(Iu(h),zb()),n;b:{for(var p=m,q=ls(g,z.cookie,void 0,$t()),r=0;r<q.length;++r)if(Iu(q[r])>p){n=!0;break b}n=!1}if(!n){var t=Pr(b,m,!0);t.Dc=$t();ws(g,h,t)}}}}Au(xu(c.gclid,c.gclsrc),!1,b)},$t())}
function Ju(a){var b=["ag"],c=Xs(!0),d=gu(a.prefix);bu(function(){for(var e=0;e<b.length;++e){var f=hu(b[e],d);if(f){var g=c[f];if(g){var h=Ft(g,5);if(h){var m=lu(h);m||(m=zb());var n;a:{for(var p=m,q=Jt(f,5),r=0;r<q.length;++r)if(lu(q[r])>p){n=!0;break a}n=!1}if(n)break;h.i=""+Math.round(m/1E3);Lt(f,h,5,a,m)}}}}},["ad_storage"])}function hu(a,b){var c=Xt[a];if(c!==void 0)return b+c}function Iu(a){return Ku(a.split(".")).length!==0?(Number(a.split(".")[1])||0)*1E3:0}
function lu(a){return a?(Number(a.i)||0)*1E3:0}function mu(a){var b=Ku(a.split("."));return b.length===0?null:{version:b[0],gclid:b[2],timestamp:(Number(b[1])||0)*1E3,labels:b.slice(3)}}function Ku(a){return a.length<3||a[0]!=="GCL"&&a[0]!=="1"||!/^\d+$/.test(a[1])||!Vt.test(a[2])?[]:a}
function Lu(a,b,c,d,e){if(Array.isArray(b)&&is(x)){var f=gu(e),g=function(){for(var h={},m=0;m<a.length;++m){var n=hu(a[m],f);if(n){var p=ls(n,z.cookie,void 0,$t());p.length&&(h[n]=p.sort()[p.length-1])}}return h};bu(function(){dt(g,b,c,d)},$t())}}
function Mu(a,b,c,d){if(Array.isArray(a)&&is(x)){var e=["ag"],f=gu(d),g=function(){for(var h={},m=0;m<e.length;++m){var n=hu(e[m],f);if(!n)return{};var p=Jt(n,5);if(p.length){var q=p.sort(function(r,t){return lu(t)-lu(r)})[0];h[n]=Gt(q,5)}}return h};bu(function(){dt(g,a,b,c)},["ad_storage"])}}function nu(a){return a.filter(function(b){return Vt.test(b.gclid)})}
function Nu(a,b){if(is(x)){for(var c=gu(b.prefix),d={},e=0;e<a.length;e++)Xt[a[e]]&&(d[a[e]]=Xt[a[e]]);bu(function(){rb(d,function(f,g){var h=ls(c+g,z.cookie,void 0,$t());h.sort(function(t,u){return Iu(u)-Iu(t)});if(h.length){var m=h[0],n=Iu(m),p=Ku(m.split(".")).length!==0?m.split(".").slice(3):[],q={},r;r=Ku(m.split(".")).length!==0?m.split(".")[2]:void 0;q[f]=[r];Au(q,!0,b,n,p)}})},$t())}}
function Ou(a){var b=["ag"],c=["gbraid"];bu(function(){for(var d=gu(a.prefix),e=0;e<b.length;++e){var f=hu(b[e],d);if(!f)break;var g=Jt(f,5);if(g.length){var h=g.sort(function(q,r){return lu(r)-lu(q)})[0],m=lu(h),n=h.b,p={};p[c[e]]=h.k;Au(p,!0,a,m,n)}}},["ad_storage"])}function Pu(a,b){for(var c=0;c<b.length;++c)if(a[b[c]])return!0;return!1}
function Qu(a){function b(h,m,n){n&&(h[m]=n)}if(bn()){var c=yu(),d;a.includes("gad_source")&&(d=c.gad_source!==void 0?c.gad_source:Xs(!1)._gs);if(Pu(c,a)||d){var e={};b(e,"gclid",c.gclid);b(e,"dclid",c.dclid);b(e,"gclsrc",c.gclsrc);b(e,"wbraid",c.wbraid);b(e,"gbraid",c.gbraid);et(function(){return e},3);var f={},g=(f._up="1",f);b(g,"_gs",d);et(function(){return g},1)}}}function Eu(){var a=Uk(x.location.href);return Ok(a,"query",!1,void 0,"gad_source")}
function Ru(a){if(!Ka(1))return null;var b=Xs(!0).gad_source;if(b!=null)return x.location.hash="",b;if(Ka(2)){b=Eu();if(b!=null)return b;var c=yu();if(Pu(c,a))return"0"}return null}function Su(a){var b=Ru(a);b!=null&&et(function(){var c={};return c.gad_source=b,c},4)}function Tu(a,b,c){var d=[];if(b.length===0)return d;for(var e={},f=0;f<b.length;f++){var g=b[f],h=g.type?g.type:"gcl";(g.labels||[]).indexOf(c)===-1?(a.push(0),e[h]||d.push(g)):a.push(1);e[h]=!0}return d}
function Uu(a,b,c,d){var e=[];c=c||{};if(!au($t()))return e;var f=du(a),g=Tu(e,f,b);if(g.length&&!d)for(var h=l(g),m=h.next();!m.done;m=h.next()){var n=m.value,p=n.timestamp,q=[n.version,Math.round(p/1E3),n.gclid].concat(n.labels||[],[b]).join("."),r=Pr(c,p,!0);r.Dc=$t();ws(a,q,r)}return e}
function Vu(a,b){var c=[];b=b||{};var d=fu(b),e=Tu(c,d,a);if(e.length)for(var f=l(e),g=f.next();!g.done;g=f.next()){var h=g.value,m=gu(b.prefix),n=hu(h.type,m);if(!n)break;var p=h,q=p.version,r=p.gclid,t=p.labels,u=p.timestamp,v=Math.round(u/1E3);if(h.type==="ag"){var w={},y=(w.k=r,w.i=""+v,w.b=(t||[]).concat([a]),w);Lt(n,y,5,b,u)}else if(h.type==="gb"){var A=[q,v,r].concat(t||[],[a]).join("."),C=Pr(b,u,!0);C.Dc=$t();ws(n,A,C)}}return c}
function Wu(a,b){var c=gu(b),d=hu(a,c);if(!d)return 0;var e;e=a==="ag"?iu(d):du(d);for(var f=0,g=0;g<e.length;g++)f=Math.max(f,e[g].timestamp);return f}function Xu(a){for(var b=0,c=l(Object.keys(a)),d=c.next();!d.done;d=c.next())for(var e=a[d.value],f=0;f<e.length;f++)b=Math.max(b,Number(e[f].timestamp));return b}function Yu(a){var b=Math.max(Wu("aw",a),Xu(au($t())?wt():{})),c=Math.max(Wu("gb",a),Xu(au($t())?wt("_gac_gb",!0):{}));c=Math.max(c,Wu("ag",a));return c>b}
function Du(){return z.referrer?Ok(Uk(z.referrer),"host"):""};function mv(){return wp("dedupe_gclid",function(){return Ds()})};var nv=/^(www\.)?google(\.com?)?(\.[a-z]{2}t?)?$/,ov=/^www.googleadservices.com$/;function pv(a){a||(a=qv());return a.Iq?!1:a.Dp||a.Ep||a.Hp||a.Fp||a.Xf||a.np||a.Gp||a.tp?!0:!1}function qv(){var a={},b=Xs(!0);a.Iq=!!b._up;var c=yu();a.Dp=c.aw!==void 0;a.Ep=c.dc!==void 0;a.Hp=c.wbraid!==void 0;a.Fp=c.gbraid!==void 0;a.Gp=c.gclsrc==="aw.ds";a.Xf=av().Xf;var d=z.referrer?Ok(Uk(z.referrer),"host"):"";a.tp=nv.test(d);a.np=ov.test(d);return a};function rv(a){var b=window,c=b.webkit;delete b.webkit;a(b.webkit);b.webkit=c}function sv(a){var b={action:"gcl_setup"};if("CWVWebViewMessage"in a.messageHandlers)return a.messageHandlers.CWVWebViewMessage.postMessage({command:"awb",payload:b}),!0;var c=a.messageHandlers.awb;return c?(c.postMessage(b),!0):!1};function tv(){return["ad_storage","ad_user_data"]}function uv(a){if(F(38)&&!vn(rn.Z.ql)&&"webkit"in window&&window.webkit.messageHandlers){var b=function(){try{rv(function(c){c&&("CWVWebViewMessage"in c.messageHandlers||"awb"in c.messageHandlers)&&(un(rn.Z.ql,function(d){d.gclid&&Fu(d.gclid,5,a)}),sv(c)||M(178))})}catch(c){M(177)}};en(function(){au(tv())?b():fn(b,tv())},tv())}};var vv=["https://www.google.com","https://www.youtube.com","https://m.youtube.com"];function wv(a){a.data.action==="gcl_transfer"&&a.data.gadSource?un(rn.Z.Nf,{gadSource:a.data.gadSource}):M(173)}
function xv(a,b){if(F(a)){if(vn(rn.Z.Nf))return M(176),rn.Z.Nf;if(vn(rn.Z.tl))return M(170),rn.Z.Nf;var c=Ll();if(!c)M(171);else if(c.opener){var d=function(g){if(vv.includes(g.origin)){a===119?wv(g):a===200&&(wv(g),g.data.gclid&&Fu(String(g.data.gclid),6,b));var h;(h=g.stopImmediatePropagation)==null||h.call(g);Tq(c,"message",d)}else M(172)};if(Sq(c,"message",d)){un(rn.Z.tl,!0);for(var e=l(vv),f=e.next();!f.done;f=e.next())c.opener.postMessage({action:"gcl_setup"},f.value);M(174);return rn.Z.Nf}M(175)}}}
;var yv=function(){this.C=this.gppString=void 0};yv.prototype.reset=function(){this.C=this.gppString=void 0};var zv=new yv;var Av=RegExp("^UA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*(?:%3BUA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*)*$"),Bv=/^~?[\w-]+(?:\.~?[\w-]+)*$/,Cv=/^\d+\.fls\.doubleclick\.net$/,Dv=/;gac=([^;?]+)/,Ev=/;gacgb=([^;?]+)/;
function Fv(a,b){if(Cv.test(z.location.host)){var c=z.location.href.match(b);return c&&c.length===2&&c[1].match(Av)?Nk(c[1])||"":""}for(var d=[],e=l(Object.keys(a)),f=e.next();!f.done;f=e.next()){for(var g=f.value,h=[],m=a[g],n=0;n<m.length;n++)h.push(m[n].gclid);d.push(g+":"+h.join(","))}return d.length>0?d.join(";"):""}
function Gv(a,b,c){for(var d=au($t())?wt("_gac_gb",!0):{},e=[],f=!1,g=l(Object.keys(d)),h=g.next();!h.done;h=g.next()){var m=h.value,n=Uu("_gac_gb_"+m,a,b,c);f=f||n.length!==0&&n.some(function(p){return p===1});e.push(m+":"+n.join(","))}return{mp:f?e.join(";"):"",lp:Fv(d,Ev)}}function Hv(a){var b=z.location.href.match(new RegExp(";"+a+"=([^;?]+)"));return b&&b.length===2&&b[1].match(Bv)?b[1]:void 0}
function Iv(a){var b={},c,d,e;Cv.test(z.location.host)&&(c=Hv("gclgs"),d=Hv("gclst"),e=Hv("gcllp"));if(c&&d&&e)b.sh=c,b.uh=d,b.th=e;else{var f=zb(),g=iu((a||"_gcl")+"_gs"),h=g.map(function(p){return p.gclid}),m=g.map(function(p){return f-p.timestamp}),n=g.map(function(p){return p.Fd});h.length>0&&m.length>0&&n.length>0&&(b.sh=h.join("."),b.uh=m.join("."),b.th=n.join("."))}return b}
function Jv(a,b,c,d){d=d===void 0?!1:d;if(Cv.test(z.location.host)){var e=Hv(c);if(e){if(d){var f=new Nt;Ot(f,2);Ot(f,3);return e.split(".").map(function(h){return{gclid:h,Ka:f,Bb:[1]}})}return e.split(".").map(function(h){return{gclid:h}})}}else{if(b==="gclid"){var g=(a||"_gcl")+"_aw";return d?tu(g):du(g)}if(b==="wbraid")return du((a||"_gcl")+"_gb");if(b==="braids")return fu({prefix:a})}return[]}function Kv(a){return Cv.test(z.location.host)?!(Hv("gclaw")||Hv("gac")):Yu(a)}
function Lv(a,b,c){var d;d=c?Vu(a,b):Uu((b&&b.prefix||"_gcl")+"_gb",a,b);return d.length===0||d.every(function(e){return e===0})?"":d.join(".")};function Mv(){var a=x.__uspapi;if(jb(a)){var b="";try{a("getUSPData",1,function(c,d){if(d&&c){var e=c.uspString;e&&RegExp("^[\\da-zA-Z-]{1,20}$").test(e)&&(b=e)}})}catch(c){}return b}};function Zv(a){var b=O(a.D,K.m.Lc),c=O(a.D,K.m.Kc);b&&!c?(a.eventName!==K.m.qa&&a.eventName!==K.m.Td&&M(131),a.isAborted=!0):!b&&c&&(M(132),a.isAborted=!0)}function $v(a){var b=Q(K.m.U)?vp.pscdl:"denied";b!=null&&V(a,K.m.Eg,b)}function aw(a){var b=Jl(!0);V(a,K.m.Jc,b)}function bw(a){Hr()&&V(a,K.m.be,1)}
function Qv(){var a=z.title;if(a===void 0||a==="")return"";a=encodeURIComponent(a);for(var b=256;b>0&&Nk(a.substring(0,b))===void 0;)b--;return Nk(a.substring(0,b))||""}function cw(a){dw(a,Dp.Cf.Sm,O(a.D,K.m.ob))}function dw(a,b,c){Pv(a,K.m.rd)||V(a,K.m.rd,{});Pv(a,K.m.rd)[b]=c}function ew(a){U(a,R.A.Pf,Qm.X.Da)}function fw(a){var b=gb("GTAG_EVENT_FEATURE_CHANNEL");b&&(V(a,K.m.hf,b),eb())}function gw(a){var b=a.D.getMergedValues(K.m.rc);b&&a.mergeHitDataForKey(K.m.rc,b)}
function hw(a,b){b=b===void 0?!1:b;var c=S(a,R.A.Of);if(c)if(c.indexOf(a.target.destinationId)<0){if(U(a,R.A.Ij,!1),b||!iw(a,"custom_event_accept_rules",!1))a.isAborted=!0}else U(a,R.A.Ij,!0)}function jw(a){jl&&(Vn=!0,a.eventName===K.m.qa?ao(a.D,a.target.id):(S(a,R.A.Je)||(Yn[a.target.id]=!0),Cp(S(a,R.A.hb))))};function tw(a,b,c,d){var e=Gc(),f;if(e===1)a:{var g=gk;g=g.toLowerCase();for(var h="https://"+g,m="http://"+g,n=1,p=z.getElementsByTagName("script"),q=0;q<p.length&&q<100;q++){var r=p[q].src;if(r){r=r.toLowerCase();if(r.indexOf(m)===0){f=3;break a}n===1&&r.indexOf(h)===0&&(n=2)}}f=n}else f=e;return(f===2||d||"http:"!==x.location.protocol?a:b)+c};
var yw=function(a,b){if(a)if(Ir()){}else if(kb(a)&&(a=Gp(a)),a){var c=void 0,d=!1,e=O(b,K.m.Nn);if(e&&Array.isArray(e)){c=[];for(var f=0;f<e.length;f++){var g=Gp(e[f]);g&&(c.push(g),(a.id===g.id||a.id===a.destinationId&&a.destinationId===g.destinationId)&&(d=!0))}}if(!c||d){var h=O(b,K.m.Mk),m;if(h){m=Array.isArray(h)?h:[h];var n=O(b,K.m.Kk),p=O(b,K.m.Lk),q=O(b,K.m.Nk),r=Ho(O(b,K.m.Mn)),t=n||p,u=1;a.prefix!=="UA"||c||
(u=5);for(var v=0;v<m.length;v++)if(v<u)if(c)uw(c,m[v],r,b,{Cc:t,options:q});else if(a.prefix==="AW"&&a.ids[Ip[1]])F(155)?uw([a],m[v],r||"US",b,{Cc:t,options:q}):vw(a.ids[Ip[0]],a.ids[Ip[1]],m[v],b,{Cc:t,options:q});else if(a.prefix==="UA")if(F(155))uw([a],m[v],r||"US",b,{Cc:t});else{var w=a.destinationId,y=m[v],A={Cc:t};M(23);if(y){A=A||{};var C=ww(xw,A,w),E={};A.Cc!==void 0?E.receiver=A.Cc:E.replace=y;E.ga_wpid=w;E.destination=y;C(2,yb(),E)}}}}}},uw=function(a,b,c,d,e){M(21);if(b&&c){e=e||{};for(var f=
{countryNameCode:c,destinationNumber:b,retrievalTime:yb()},g=0;g<a.length;g++){var h=a[g];zw[h.id]||(h&&h.prefix==="AW"&&!f.adData&&h.ids.length>=2?(f.adData={ak:h.ids[Ip[0]],cl:h.ids[Ip[1]]},Aw(f.adData,d),zw[h.id]=!0):h&&h.prefix==="UA"&&!f.gaData&&(f.gaData={gaWpid:h.destinationId},zw[h.id]=!0))}(f.gaData||f.adData)&&ww(Bw,e,void 0,d)(e.Cc,f,e.options)}},vw=function(a,b,c,d,e){M(22);if(c){e=e||{};var f=ww(Cw,e,a,d),g={ak:a,cl:b};e.Cc===void 0&&(g.autoreplace=c);Aw(g,d);f(2,e.Cc,g,c,0,yb(),e.options)}},
Aw=function(a,b){a.dma=Er();Fr()&&(a.dmaCps=Dr());wr(b)?a.npa="0":a.npa="1"},ww=function(a,b,c,d){var e=x;if(e[a.functionName])return b.tj&&B(b.tj),e[a.functionName];var f=Dw();e[a.functionName]=f;if(a.additionalQueues)for(var g=0;g<a.additionalQueues.length;g++)e[a.additionalQueues[g]]=e[a.additionalQueues[g]]||Dw();a.idKey&&e[a.idKey]===void 0&&(e[a.idKey]=c);km({destinationId:jg.ctid,endpoint:0,eventId:d==null?void 0:d.eventId,priorityId:d==null?void 0:d.priorityId},tw("https://","http://",a.scriptUrl),
b.tj,b.Xp);return f},Dw=function(){function a(){a.q=a.q||[];a.q.push(arguments)}return a},Cw={functionName:"_googWcmImpl",idKey:"_googWcmAk",scriptUrl:"www.gstatic.com/wcm/loader.js"},xw={functionName:"_gaPhoneImpl",idKey:"ga_wpid",scriptUrl:"www.gstatic.com/gaphone/loader.js"},Ew={Om:"9",ro:"5"},Bw={functionName:"_googCallTrackingImpl",additionalQueues:[xw.functionName,Cw.functionName],scriptUrl:"www.gstatic.com/call-tracking/call-tracking_"+
(Ew.Om||Ew.ro)+".js"},zw={};function Fw(a){return{getDestinationId:function(){return a.target.destinationId},getEventName:function(){return a.eventName},setEventName:function(b){a.eventName=b},getHitData:function(b){return Pv(a,b)},setHitData:function(b,c){V(a,b,c)},setHitDataIfNotDefined:function(b,c){Pv(a,b)===void 0&&V(a,b,c)},copyToHitData:function(b,c){a.copyToHitData(b,c)},getMetadata:function(b){return S(a,b)},setMetadata:function(b,c){U(a,b,c)},isAborted:function(){return a.isAborted},abort:function(){a.isAborted=!0},
getFromEventContext:function(b){return O(a.D,b)},Ab:function(){return a},getHitKeys:function(){return Object.keys(a.C)},getMergedValues:function(b){return a.D.getMergedValues(b,3)},mergeHitDataForKey:function(b,c){return kd(c)?a.mergeHitDataForKey(b,c):!1}}};var Hw=function(a){var b=Gw[a.target.destinationId];if(!a.isAborted&&b)for(var c=Fw(a),d=0;d<b.length;++d){try{b[d](c)}catch(e){a.isAborted=!0}if(a.isAborted)break}},Iw=function(a,b){var c=Gw[a];c||(c=Gw[a]=[]);c.push(b)},Gw={};function Kw(a,b){return arguments.length===1?Lw("set",a):Lw("set",a,b)}function Mw(a,b){return arguments.length===1?Lw("config",a):Lw("config",a,b)}function Nw(a,b,c){c=c||{};c[K.m.ld]=a;return Lw("event",b,c)}function Lw(){return arguments};var Pw=function(){this.messages=[];this.C=[]};Pw.prototype.enqueue=function(a,b,c){var d=this.messages.length+1;a["gtm.uniqueEventId"]=b;a["gtm.priorityId"]=d;var e=Object.assign({},c,{eventId:b,priorityId:d,fromContainerExecution:!0}),f={message:a,notBeforeEventId:b,priorityId:d,messageContext:e};this.messages.push(f);for(var g=0;g<this.C.length;g++)try{this.C[g](f)}catch(h){}};Pw.prototype.listen=function(a){this.C.push(a)};
Pw.prototype.get=function(){for(var a={},b=0;b<this.messages.length;b++){var c=this.messages[b],d=a[c.notBeforeEventId];d||(d=[],a[c.notBeforeEventId]=d);d.push(c)}return a};Pw.prototype.prune=function(a){for(var b=[],c=[],d=0;d<this.messages.length;d++){var e=this.messages[d];e.notBeforeEventId===a?b.push(e):c.push(e)}this.messages=c;return b};function Qw(a,b,c){c.eventMetadata=c.eventMetadata||{};c.eventMetadata[R.A.hb]=jg.canonicalContainerId;Rw().enqueue(a,b,c)}
function Sw(){var a=Tw;Rw().listen(a)}function Rw(){return wp("mb",function(){return new Pw})};var Uw,Vw=!1;function Ww(){Vw=!0;Uw=Uw||{}}function Xw(a){Vw||Ww();return Uw[a]};function Yw(){var a=x.screen;return{width:a?a.width:0,height:a?a.height:0}}
function Zw(a){if(z.hidden)return!0;var b=a.getBoundingClientRect();if(b.top===b.bottom||b.left===b.right||!x.getComputedStyle)return!0;var c=x.getComputedStyle(a,null);if(c.visibility==="hidden")return!0;for(var d=a,e=c;d;){if(e.display==="none")return!0;var f=e.opacity,g=e.filter;if(g){var h=g.indexOf("opacity(");h>=0&&(g=g.substring(h+8,g.indexOf(")",h)),g.charAt(g.length-1)==="%"&&(g=g.substring(0,g.length-1)),f=String(Math.min(Number(g),Number(f))))}if(f!==void 0&&Number(f)<=0)return!0;(d=d.parentElement)&&
(e=x.getComputedStyle(d,null))}return!1}
var ix=function(a){return a.tagName+":"+a.isVisible+":"+a.la.length+":"+hx.test(a.la)},wx=function(a){a=a||{ze:!0,Ae:!0,Eh:void 0};a.Yb=a.Yb||{email:!0,phone:!1,address:!1};var b=jx(a),c=kx[b];if(c&&zb()-c.timestamp<200)return c.result;var d=lx(),e=d.status,f=[],g,h,m=[];if(!F(33)){if(a.Yb&&a.Yb.email){var n=mx(d.elements);f=nx(n,a&&a.Uf);g=ox(f);n.length>10&&(e="3")}!a.Eh&&g&&(f=[g]);for(var p=0;p<f.length;p++)m.push(px(f[p],!!a.ze,!!a.Ae));m=m.slice(0,10)}else if(a.Yb){}g&&(h=px(g,!!a.ze,!!a.Ae));var G={elements:m,
xj:h,status:e};kx[b]={timestamp:zb(),result:G};return G},xx=function(a,b){if(a){var c=a.trim().replaceAll(/\s+/g,"").replaceAll(/(\d{2,})\./g,"$1").replaceAll(/-/g,"").replaceAll(/\((\d+)\)/g,"$1");if(b&&c.match(/^\+?\d{3,7}$/))return c;c.charAt(0)!=="+"&&(c="+"+c);if(c.match(/^\+\d{10,15}$/))return c}},zx=function(a){var b=yx(/^(\w|[- ])+$/)(a);if(!b)return b;var c=b.replaceAll(/[- ]+/g,"");return c.length>10?void 0:c},yx=function(a){return function(b){var c=b.match(a);return c?c[0].trim().toLowerCase():
void 0}},vx=function(a,b,c){var d=a.element,e={la:a.la,type:a.xa,tagName:d.tagName};b&&(e.querySelector=Ax(d));c&&(e.isVisible=!Zw(d));return e},px=function(a,b,c){return vx({element:a.element,la:a.la,xa:ux.hc},b,c)},jx=function(a){var b=!(a==null||!a.ze)+"."+!(a==null||!a.Ae);a&&a.Uf&&a.Uf.length&&(b+="."+a.Uf.join("."));a&&a.Yb&&(b+="."+a.Yb.email+"."+a.Yb.phone+"."+a.Yb.address);return b},ox=function(a){if(a.length!==0){var b;b=Bx(a,function(c){return!Cx.test(c.la)});b=Bx(b,function(c){return c.element.tagName.toUpperCase()===
"INPUT"});b=Bx(b,function(c){return!Zw(c.element)});return b[0]}},nx=function(a,b){if(!b||b.length===0)return a;for(var c=[],d=0;d<a.length;d++){for(var e=!0,f=0;f<b.length;f++){var g=b[f];if(g&&wi(a[d].element,g)){e=!1;break}}e&&c.push(a[d])}return c},Bx=function(a,b){if(a.length<=1)return a;var c=a.filter(b);return c.length===0?a:c},Ax=function(a){var b;if(a===z.body)b="body";else{var c;if(a.id)c="#"+a.id;else{var d;if(a.parentElement){var e;a:{var f=a.parentElement;if(f){for(var g=0;g<f.childElementCount;g++)if(f.children[g]===
a){e=g+1;break a}e=-1}else e=1}d=Ax(a.parentElement)+">:nth-child("+e.toString()+")"}else d="";c=d}b=c}return b},mx=function(a){for(var b=[],c=0;c<a.length;c++){var d=a[c],e=d.textContent;d.tagName.toUpperCase()==="INPUT"&&d.value&&(e=d.value);if(e){var f=e.match(Dx);if(f){var g=f[0],h;if(x.location){var m=Qk(x.location,"host",!0);h=g.toLowerCase().indexOf(m)>=0}else h=!1;h||b.push({element:d,la:g})}}}return b},lx=function(){var a=[],b=z.body;if(!b)return{elements:a,status:"4"};for(var c=b.querySelectorAll("*"),
d=0;d<c.length&&d<1E4;d++){var e=c[d];if(!(Ex.indexOf(e.tagName.toUpperCase())>=0)&&e.children instanceof HTMLCollection){for(var f=!1,g=0;g<e.childElementCount&&g<1E4;g++)if(!(Fx.indexOf(e.children[g].tagName.toUpperCase())>=0)){f=!0;break}(!f||F(33)&&Gx.indexOf(e.tagName)!==-1)&&a.push(e)}}return{elements:a,status:c.length>1E4?"2":"1"}},Dx=/[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}/i,hx=/@(gmail|googlemail)\./i,Cx=/support|noreply/i,Ex="SCRIPT STYLE IMG SVG PATH BR NOSCRIPT TEXTAREA".split(" "),Fx=
["BR"],Hx=ug('',2),ux={hc:"1",xd:"2",pd:"3",wd:"4",Ie:"5",Mf:"6",fh:"7",Li:"8",Hh:"9",Gi:"10"},kx={},Gx=["INPUT","SELECT"],Ix=yx(/^([^\x00-\x40\x5b-\x60\x7b-\xff]|[.-]|\s)+$/);
var ly=Number('')||5,my=Number('')||50,ny=ob();
var py=function(a,b){a&&(oy("sid",a.targetId,b),oy("cc",a.clientCount,b),oy("tl",a.totalLifeMs,b),oy("hc",a.heartbeatCount,b),oy("cl",a.clientLifeMs,b))},oy=function(a,b,c){b!=null&&c.push(a+"="+b)},qy=function(){var a=z.referrer;if(a){var b;return Ok(Uk(a),"host")===((b=x.location)==null?void 0:b.host)?1:2}return 0},ry="https://"+Si(21,"www.googletagmanager.com")+"/a?",ty=function(){this.R=sy;this.N=0};ty.prototype.H=function(a,b,c,d){var e=qy(),f,
g=[];f=x===x.top&&e!==0&&b?(b==null?void 0:b.clientCount)>1?e===2?1:2:e===2?0:3:4;a&&oy("si",a.fg,g);oy("m",0,g);oy("iss",f,g);oy("if",c,g);py(b,g);d&&oy("fm",encodeURIComponent(d.substring(0,my)),g);this.P(g);};ty.prototype.C=function(a,b,c,d,e){var f=[];oy("m",1,f);oy("s",a,f);oy("po",qy(),f);b&&(oy("st",b.state,f),oy("si",b.fg,f),oy("sm",b.lg,f));py(c,f);oy("c",d,f);e&&oy("fm",encodeURIComponent(e.substring(0,
my)),f);this.P(f);};ty.prototype.P=function(a){a=a===void 0?[]:a;!il||this.N>=ly||(oy("pid",ny,a),oy("bc",++this.N,a),a.unshift("ctid="+jg.ctid+"&t=s"),this.R(""+ry+a.join("&")))};var uy=Number('')||500,vy=Number('')||5E3,wy=Number('20')||10,xy=Number('')||5E3;function yy(a){return a.performance&&a.performance.now()||Date.now()}
var zy=function(a,b){var c=x,d;var e=function(f,g,h){h=h===void 0?{km:function(){},lm:function(){},jm:function(){},onFailure:function(){}}:h;this.wo=f;this.C=g;this.N=h;this.ba=this.ka=this.heartbeatCount=this.uo=0;this.gh=!1;this.H={};this.id=String(Math.floor(Number.MAX_SAFE_INTEGER*Math.random()));this.state=0;this.fg=yy(this.C);this.lg=yy(this.C);this.R=10};e.prototype.init=function(){this.P(1);this.Ba()};e.prototype.getState=function(){return{state:this.state,
fg:Math.round(yy(this.C)-this.fg),lg:Math.round(yy(this.C)-this.lg)}};e.prototype.P=function(f){this.state!==f&&(this.state=f,this.lg=yy(this.C))};e.prototype.Il=function(){return String(this.uo++)};e.prototype.Ba=function(){var f=this;this.heartbeatCount++;this.Sa({type:0,clientId:this.id,requestId:this.Il(),maxDelay:this.hh()},function(g){if(g.type===0){var h;if(((h=g.failure)==null?void 0:h.failureType)!=null)if(g.stats&&(f.stats=g.stats),f.ba++,g.isDead||f.ba>wy){var m=g.isDead&&g.failure.failureType;
f.R=m||10;f.P(4);f.so();var n,p;(p=(n=f.N).jm)==null||p.call(n,{failureType:m||10,data:g.failure.data})}else f.P(3),f.Ml();else{if(f.heartbeatCount>g.stats.heartbeatCount+wy){f.heartbeatCount=g.stats.heartbeatCount;var q,r;(r=(q=f.N).onFailure)==null||r.call(q,{failureType:13})}f.stats=g.stats;var t=f.state;f.P(2);if(t!==2)if(f.gh){var u,v;(v=(u=f.N).lm)==null||v.call(u)}else{f.gh=!0;var w,y;(y=(w=f.N).km)==null||y.call(w)}f.ba=0;f.xo();f.Ml()}}})};e.prototype.hh=function(){return this.state===2?
vy:uy};e.prototype.Ml=function(){var f=this;this.C.setTimeout(function(){f.Ba()},Math.max(0,this.hh()-(yy(this.C)-this.ka)))};e.prototype.Ao=function(f,g,h){var m=this;this.Sa({type:1,clientId:this.id,requestId:this.Il(),command:f},function(n){if(n.type===1)if(n.result)g(n.result);else{var p,q,r,t={failureType:(r=(p=n.failure)==null?void 0:p.failureType)!=null?r:12,data:(q=n.failure)==null?void 0:q.data},u,v;(v=(u=m.N).onFailure)==null||v.call(u,t);h(t)}})};e.prototype.Sa=function(f,g){var h=this;
if(this.state===4)f.failure={failureType:this.R},g(f);else{var m=this.state!==2&&f.type!==0,n=f.requestId,p,q=this.C.setTimeout(function(){var t=h.H[n];t&&h.Kf(t,7)},(p=f.maxDelay)!=null?p:xy),r={request:f,Bm:g,wm:m,Up:q};this.H[n]=r;m||this.sendRequest(r)}};e.prototype.sendRequest=function(f){this.ka=yy(this.C);f.wm=!1;this.wo(f.request)};e.prototype.xo=function(){for(var f=l(Object.keys(this.H)),g=f.next();!g.done;g=f.next()){var h=this.H[g.value];h.wm&&this.sendRequest(h)}};e.prototype.so=function(){for(var f=
l(Object.keys(this.H)),g=f.next();!g.done;g=f.next())this.Kf(this.H[g.value],this.R)};e.prototype.Kf=function(f,g){this.qb(f);var h=f.request;h.failure={failureType:g};f.Bm(h)};e.prototype.qb=function(f){delete this.H[f.request.requestId];this.C.clearTimeout(f.Up)};e.prototype.Bp=function(f){this.ka=yy(this.C);var g=this.H[f.requestId];if(g)this.qb(g),g.Bm(f);else{var h,m;(m=(h=this.N).onFailure)==null||m.call(h,{failureType:14})}};d=new e(a,c,b);return d};var Ay;
var By=function(){Ay||(Ay=new ty);return Ay},sy=function(a){on(qn(Qm.X.Oc),function(){Jc(a)})},Cy=function(a){var b=a.substring(0,a.indexOf("/_/service_worker"));return"&1p=1"+(b?"&path="+encodeURIComponent(b):"")},Dy=function(a){var b=a,c=Pj.Ba;b?(b.charAt(b.length-1)!=="/"&&(b+="/"),a=b+c):a="https://www.googletagmanager.com/static/service_worker/"+c+"/";var d;try{d=new URL(a)}catch(e){return null}return d.protocol!=="https:"?null:d},Ey=function(a){var b=vn(rn.Z.Bl);return b&&b[a]},Fy=function(a,
b,c,d,e){var f=this;this.H=d;this.R=this.P=!1;this.ba=null;this.initTime=c;this.C=15;this.N=this.Ro(a);x.setTimeout(function(){f.initialize()},1E3);B(function(){f.Lp(a,b,e)})};k=Fy.prototype;k.delegate=function(a,b,c){this.getState()!==2?(this.H.C(this.C,{state:this.getState(),fg:this.initTime,lg:Math.round(zb())-this.initTime},void 0,a.commandType),c({failureType:this.C})):this.N.Ao(a,b,c)};k.getState=function(){return this.N.getState().state};k.Lp=function(a,b,c){var d=x.location.origin,e=this,
f=Hc();try{var g=f.contentDocument.createElement("iframe"),h=a.pathname,m=h[h.length-1]==="/"?a.toString():a.toString()+"/",n=b?Cy(h):"",p;F(133)&&(p={sandbox:"allow-same-origin allow-scripts"});Hc(m+"sw_iframe.html?origin="+encodeURIComponent(d)+n+(c?"&e=1":""),void 0,p,void 0,g);var q=function(){f.contentDocument.body.appendChild(g);g.addEventListener("load",function(){e.ba=g.contentWindow;f.contentWindow.addEventListener("message",function(r){r.origin===a.origin&&e.N.Bp(r.data)});e.initialize()})};
f.contentDocument.readyState==="complete"?q():f.contentWindow.addEventListener("load",function(){q()})}catch(r){f.parentElement.removeChild(f),this.C=11,this.H.H(void 0,void 0,this.C,r.toString())}};k.Ro=function(a){var b=this,c=zy(function(d){var e;(e=b.ba)==null||e.postMessage(d,a.origin)},{km:function(){b.P=!0;b.H.H(c.getState(),c.stats)},lm:function(){},jm:function(d){b.P?(b.C=(d==null?void 0:d.failureType)||10,b.H.C(b.C,c.getState(),c.stats,void 0,d==null?void 0:d.data)):(b.C=(d==null?void 0:
d.failureType)||4,b.H.H(c.getState(),c.stats,b.C,d==null?void 0:d.data))},onFailure:function(d){b.C=d.failureType;b.H.C(b.C,c.getState(),c.stats,d.command,d.data)}});return c};k.initialize=function(){this.R||this.N.init();this.R=!0};function Gy(){var a=ig(fg.C,"",function(){return{}});try{return a("internal_sw_allowed"),!0}catch(b){return!1}}
function Hy(a,b){var c=Math.round(zb());b=b===void 0?!1:b;var d=x.location.origin;if(!d||!Gy()||F(168))return;ok()&&(a=""+d+nk()+"/_/service_worker");var e=Dy(a);if(e===null||Ey(e.origin))return;if(!uc()){By().H(void 0,void 0,6);return}var f=new Fy(e,!!a,c||Math.round(zb()),By(),b);wn(rn.Z.Bl)[e.origin]=f;}
var Iy=function(a,b,c,d){var e;if((e=Ey(a))==null||!e.delegate){var f=uc()?16:6;By().C(f,void 0,void 0,b.commandType);d({failureType:f});return}Ey(a).delegate(b,c,d);};
function Jy(a,b,c,d,e){var f=Dy();if(f===null){d(uc()?16:6);return}var g,h=(g=Ey(f.origin))==null?void 0:g.initTime,m=Math.round(zb()),n={commandType:0,params:{url:a,method:0,templates:b,body:"",processResponse:!1,sinceInit:h?m-h:void 0}};e&&(n.params.encryptionKeyString=e);Iy(f.origin,n,function(p){c(p)},function(p){d(p.failureType)});}
function Ky(a,b,c,d){var e=Dy(a);if(e===null){d("_is_sw=f"+(uc()?16:6)+"te");return}var f=b?1:0,g=Math.round(zb()),h,m=(h=Ey(e.origin))==null?void 0:h.initTime,n=m?g-m:void 0,p=!1;F(169)&&(p=!0);Iy(e.origin,{commandType:0,params:{url:a,method:f,templates:c,body:b||"",processResponse:!0,suppressSuccessCallback:p,sinceInit:n,attributionReporting:!0,referer:x.location.href}},function(){},function(q){var r="_is_sw=f"+q.failureType,t,u=(t=Ey(e.origin))==
null?void 0:t.getState();u!==void 0&&(r+="s"+u);d(n?r+("t"+n):r+"te")});};function Ly(a){if(F(10)||ok()||Pj.N||bl(a.D)||F(168))return;Hy(void 0,F(131));};var My="platform platformVersion architecture model uaFullVersion bitness fullVersionList wow64".split(" ");function Ny(a){var b;return(b=a.google_tag_data)!=null?b:a.google_tag_data={}}function Oy(a){var b=a.google_tag_data,c;if(b!=null&&b.uach){var d=b.uach,e=Object.assign({},d);d.fullVersionList&&(e.fullVersionList=d.fullVersionList.slice(0));c=e}else c=null;return c}function Py(a){var b,c;return(c=(b=a.google_tag_data)==null?void 0:b.uach_promise)!=null?c:null}
function Qy(a){var b,c;return typeof((b=a.navigator)==null?void 0:(c=b.userAgentData)==null?void 0:c.getHighEntropyValues)==="function"}function Ry(a){if(!Qy(a))return null;var b=Ny(a);if(b.uach_promise)return b.uach_promise;var c=a.navigator.userAgentData.getHighEntropyValues(My).then(function(d){b.uach!=null||(b.uach=d);return d});return b.uach_promise=c};function Xy(a){var b=a.location.href;if(a===a.top)return{url:b,Qp:!0};var c=!1,d=a.document;d&&d.referrer&&(b=d.referrer,a.parent===a.top&&(c=!0));var e=a.location.ancestorOrigins;if(e){var f=e[e.length-1];f&&b.indexOf(f)===-1&&(c=!1,b=f)}return{url:b,Qp:c}};function Nz(a,b){var c=!!ok();switch(a){case 45:return"https://www.google.com/ccm/collect";case 46:return c?nk()+"/gs/ccm/collect":"https://pagead2.googlesyndication.com/ccm/collect";case 51:return"https://www.google.com/travel/flights/click/conversion";case 9:return"https://googleads.g.doubleclick.net/pagead/viewthroughconversion";case 17:return c?F(90)&&oo()?Lz():""+nk()+"/ag/g/c":Lz();case 16:return c?F(90)&&oo()?Mz():""+nk()+"/ga/g/c":Mz();case 1:return"https://ad.doubleclick.net/activity;";case 2:return c?
nk()+"/ddm/activity/":"https://ade.googlesyndication.com/ddm/activity/";case 33:return"https://ad.doubleclick.net/activity;register_conversion=1;";case 11:return c?nk()+"/d/pagead/form-data":F(141)?"https://www.google.com/pagead/form-data":"https://google.com/pagead/form-data";case 3:return"https://"+b.Bo+".fls.doubleclick.net/activityi;";case 5:return"https://www.googleadservices.com/pagead/conversion";case 6:return c?nk()+"/gs/pagead/conversion":"https://pagead2.googlesyndication.com/pagead/conversion";
case 8:return"https://www.google.com/pagead/1p-conversion";case 22:return(F(207)?c:c&&b.zh)?nk()+"/as/d/ccm/conversion":"https://www.googleadservices.com/ccm/conversion";case 60:return c?nk()+"/gs/ccm/conversion":"https://pagead2.googlesyndication.com/ccm/conversion";case 23:return(F(207)?c:c&&b.zh)?nk()+"/g/d/ccm/conversion":"https://www.google.com/ccm/conversion";case 55:return c?nk()+"/gs/measurement/conversion/":"https://pagead2.googlesyndication.com/measurement/conversion/";case 54:return F(205)?
"https://www.google.com/measurement/conversion/":c?nk()+"/g/measurement/conversion/":"https://www.google.com/measurement/conversion/";case 21:return(F(207)?c:c&&b.zh)?nk()+"/d/ccm/form-data":F(141)?"https://www.google.com/ccm/form-data":"https://google.com/ccm/form-data";case 7:case 52:case 53:case 39:case 38:case 40:case 37:case 49:case 48:case 14:case 24:case 19:case 27:case 30:case 36:case 26:case 29:case 32:case 35:case 57:case 58:case 50:case 12:case 13:case 20:case 18:case 59:case 47:case 44:case 43:case 15:case 0:case 61:case 56:case 25:case 28:case 31:case 34:throw Error("Unsupported endpoint");
default:lc(a,"Unknown endpoint")}};function Oz(a){a=a===void 0?[]:a;return Qj(a).join("~")}function Pz(){if(!F(118))return"";var a,b;return(((a=Em(tm()))==null?void 0:(b=a.context)==null?void 0:b.loadExperiments)||[]).join("~")};function Qz(a,b){b&&rb(b,function(c,d){typeof d!=="object"&&d!==void 0&&(a["1p."+c]=String(d))})};var Yz={};Yz.O=ds.O;var Zz={er:"L",qo:"S",wr:"Y",Kq:"B",Uq:"E",Yq:"I",rr:"TC",Xq:"HTC"},$z={qo:"S",Tq:"V",Nq:"E",qr:"tag"},aA={},bA=(aA[Yz.O.Ni]="6",aA[Yz.O.Oi]="5",aA[Yz.O.Mi]="7",aA);function cA(){function a(c,d){var e=gb(d);e&&b.push([c,e])}var b=[];a("u","GTM");a("ut","TAGGING");a("h","HEALTH");return b};var dA=!1;
function wA(a){}function xA(a){}
function yA(){}function zA(a){}
function AA(a){}function BA(a){}
function CA(){}function DA(a,b){}
function EA(a,b,c){}
function FA(){};var GA=Object.freeze({cache:"no-store",credentials:"include",method:"GET",keepalive:!0,redirect:"follow"});
function HA(a,b,c,d,e,f,g){var h=Object.assign({},GA);c&&(h.body=c,h.method="POST");Object.assign(h,e);x.fetch(b,h).then(function(m){if(!m.ok)g==null||g();else if(m.body){var n=m.body.getReader(),p=new TextDecoder;return new Promise(function(q){function r(){n.read().then(function(t){var u;u=t.done;var v=p.decode(t.value,{stream:!u});IA(d,v);u?(f==null||f(),q()):r()}).catch(function(){q()})}r()})}}).catch(function(){g?g():F(128)&&(b+="&_z=retryFetch",c?hm(a,b,c):gm(a,b))})};var JA=function(a){this.P=a;this.C=""},KA=function(a,b){a.H=b;return a},LA=function(a,b){a.N=b;return a},IA=function(a,b){b=a.C+b;for(var c=b.indexOf("\n\n");c!==-1;){var d=a,e;a:{var f=l(b.substring(0,c).split("\n")),g=f.next().value,h=f.next().value;if(g.indexOf("event: message")===0&&h.indexOf("data: ")===0)try{e=JSON.parse(h.substring(h.indexOf(":")+1));break a}catch(m){}e=void 0}MA(d,e);b=b.substring(c+2);c=b.indexOf("\n\n")}a.C=b},NA=function(a,b){return function(){if(b.fallback_url&&b.fallback_url_method){var c=
{};MA(a,(c[b.fallback_url_method]=[b.fallback_url],c.options={},c))}}},MA=function(a,b){b&&(OA(b.send_pixel,b.options,a.P),OA(b.create_iframe,b.options,a.H),OA(b.fetch,b.options,a.N))};function PA(a){var b=a.search;return a.protocol+"//"+a.hostname+a.pathname+(b?b+"&richsstsse":"?richsstsse")}function OA(a,b,c){if(a&&c){var d=a||[];if(Array.isArray(d))for(var e=kd(b)?b:{},f=l(d),g=f.next();!g.done;g=f.next())c(g.value,e)}};var EB=new RegExp(/^(.*\.)?(google|youtube|blogger|withgoogle)(\.com?)?(\.[a-z]{2})?\.?$/),FB={cl:["ecl"],customPixels:["nonGooglePixels"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"],html:["customScripts","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],customScripts:["html","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],nonGooglePixels:[],nonGoogleScripts:["nonGooglePixels"],nonGoogleIframes:["nonGooglePixels"]},GB={cl:["ecl"],customPixels:["customScripts",
"html"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"],html:["customScripts"],customScripts:["html"],nonGooglePixels:["customPixels","customScripts","html","nonGoogleScripts","nonGoogleIframes"],nonGoogleScripts:["customScripts","html"],nonGoogleIframes:["customScripts","html","nonGoogleScripts"]},HB="google customPixels customScripts html nonGooglePixels nonGoogleScripts nonGoogleIframes".split(" ");
function IB(){var a=uk("gtm.allowlist")||uk("gtm.whitelist");a&&M(9);dk&&(a=["google","gtagfl","lcl","zone","cmpPartners"]);EB.test(x.location&&x.location.hostname)&&(dk?M(116):(M(117),JB&&(a=[],window.console&&window.console.log&&window.console.log("GTM blocked. See go/13687728."))));var b=a&&Db(wb(a),FB),c=uk("gtm.blocklist")||uk("gtm.blacklist");c||(c=uk("tagTypeBlacklist"))&&M(3);c?M(8):c=[];EB.test(x.location&&x.location.hostname)&&(c=wb(c),c.push("nonGooglePixels","nonGoogleScripts","sandboxedScripts"));
wb(c).indexOf("google")>=0&&M(2);var d=c&&Db(wb(c),GB),e={};return function(f){var g=f&&f[ff.Ra];if(!g||typeof g!=="string")return!0;g=g.replace(/^_*/,"");if(e[g]!==void 0)return e[g];var h=kk[g]||[],m=!0;if(a){var n;if(n=m)a:{if(b.indexOf(g)<0){if(dk&&h.indexOf("cmpPartners")>=0){n=!0;break a}if(h&&h.length>0)for(var p=0;p<h.length;p++){if(b.indexOf(h[p])<0){M(11);n=!1;break a}}else{n=!1;break a}}n=!0}m=n}var q=!1;if(c){var r=d.indexOf(g)>=0;if(r)q=r;else{var t=pb(d,h||[]);t&&M(10);q=t}}var u=!m||
q;!u&&(h.indexOf("sandboxedScripts")===-1?0:dk&&h.indexOf("cmpPartners")>=0?!KB():b&&b.indexOf("sandboxedScripts")!==-1?0:pb(d,HB))&&(u=!0);return e[g]=u}}function KB(){var a=ig(fg.C,jg.ctid,function(){return{}});try{return a("inject_cmp_banner"),!0}catch(b){return!1}}var JB=!1;JB=!0;function LB(a,b,c,d,e){if(!MB()&&!Jm(a)){d.loadExperiments=Rj();sm(a,d,e);var f=NB(a),g=function(){um().container[a]&&(um().container[a].state=3);OB()},h={destinationId:a,endpoint:0};if(ok())km(h,nk()+"/"+f,void 0,g);else{var m=Eb(a,"GTM-"),n=al(),p=c?"/gtag/js":"/gtm.js",q=$k(b,p+f);if(!q){var r=Tj.vg+p;n&&wc&&m&&(r=wc.replace(/^(?:https?:\/\/)?/i,"").split(/[?#]/)[0]);q=tw("https://","http://",r+f)}km(h,q,void 0,g)}}}
function OB(){Lm()||rb(Mm(),function(a,b){PB(a,b.transportUrl,b.context);M(92)})}
function PB(a,b,c,d){if(!MB()&&!Km(a))if(c.loadExperiments||(c.loadExperiments=Rj()),Lm()){var e;(e=um().destination)[a]!=null||(e[a]={state:0,transportUrl:b,context:c,parent:tm()});um().destination[a].state=0;vm({ctid:a,isDestination:!0},d);M(91)}else{var f;(f=um().destination)[a]!=null||(f[a]={context:c,state:1,parent:tm()});um().destination[a].state=1;vm({ctid:a,isDestination:!0},d);var g={destinationId:a,endpoint:0};if(ok())km(g,nk()+("/gtd"+NB(a,!0)));else{var h="/gtag/destination"+NB(a,!0),
m=$k(b,h);m||(m=tw("https://","http://",Tj.vg+h));km(g,m)}}}function NB(a,b){b=b===void 0?!1:b;var c="?id="+encodeURIComponent(a);Wj!=="dataLayer"&&(c+="&l="+Wj);if(!Eb(a,"GTM-")||b)c=F(130)?c+(ok()?"&sc=1":"&cx=c"):c+"&cx=c";c+="&gtm="+Or();al()&&(c+="&sign="+Tj.Ji);var d=Pj.H;d===1?c+="&fps=fc":d===2&&(c+="&fps=fe");!F(191)&&Rj().join("~")&&(c+="&tag_exp="+Rj().join("~"));return c}
function MB(){if(Ir()){return!0}return!1};var QB=function(){this.H=0;this.C={}};QB.prototype.addListener=function(a,b,c){var d=++this.H;this.C[a]=this.C[a]||{};this.C[a][String(d)]={listener:b,Ee:c};return d};QB.prototype.removeListener=function(a,b){var c=this.C[a],d=String(b);if(!c||!c[d])return!1;delete c[d];return!0};var SB=function(a,b){var c=[];rb(RB.C[a],function(d,e){c.indexOf(e.listener)<0&&(e.Ee===void 0||b.indexOf(e.Ee)>=0)&&c.push(e.listener)});return c};function TB(a,b,c){return{entityType:a,indexInOriginContainer:b,nameInOriginContainer:c,originContainerId:jg.ctid}};function UB(a,b){if(data.entities){var c=data.entities[a];if(c)return c[b]}};var WB=function(a,b){this.C=!1;this.P=[];this.eventData={tags:[]};this.R=!1;this.H=this.N=0;VB(this,a,b)},XB=function(a,b,c,d){if(Yj.hasOwnProperty(b)||b==="__zone")return-1;var e={};kd(d)&&(e=ld(d,e));e.id=c;e.status="timeout";return a.eventData.tags.push(e)-1},YB=function(a,b,c,d){var e=a.eventData.tags[b];e&&(e.status=c,e.executionTime=d)},ZB=function(a){if(!a.C){for(var b=a.P,c=0;c<b.length;c++)b[c]();a.C=!0;a.P.length=0}},VB=function(a,b,c){b!==void 0&&a.Rf(b);c&&x.setTimeout(function(){ZB(a)},
Number(c))};WB.prototype.Rf=function(a){var b=this,c=Bb(function(){B(function(){a(jg.ctid,b.eventData)})});this.C?c():this.P.push(c)};var $B=function(a){a.N++;return Bb(function(){a.H++;a.R&&a.H>=a.N&&ZB(a)})},aC=function(a){a.R=!0;a.H>=a.N&&ZB(a)};var bC={};function cC(){return x[dC()]}var eC=function(a){if(bn()){var b=cC();b(a+"require","linker");b(a+"linker:passthrough",!0)}},fC=function(a){var b=x;b.GoogleAnalyticsObject||(b.GoogleAnalyticsObject=a||"ga");var c=b.GoogleAnalyticsObject;if(b[c])b.hasOwnProperty(c);else{var d=function(){var e=za.apply(0,arguments);d.q=d.q||[];d.q.push(e)};d.l=Number(yb());b[c]=d}return b[c]};
function dC(){return x.GoogleAnalyticsObject||"ga"}function gC(){var a=jg.ctid;}
function hC(a,b){return function(){var c=cC(),d=c&&c.getByName&&c.getByName(a);if(d){var e=d.get("sendHitTask");d.set("sendHitTask",function(f){var g=f.get("hitPayload"),h=f.get("hitCallback"),m=g.indexOf("&tid="+b)<0;m&&(f.set("hitPayload",g.replace(/&tid=UA-[0-9]+-[0-9]+/,"&tid="+b),!0),f.set("hitCallback",void 0,!0));e(f);m&&(f.set("hitPayload",g,!0),f.set("hitCallback",h,!0),f.set("_x_19",void 0,!0),e(f))})}}};var nC=["es","1"],oC={},pC={};function qC(a,b){if(il){var c;c=b.match(/^(gtm|gtag)\./)?encodeURIComponent(b):"*";oC[a]=[["e",c],["eid",a]];yq(a)}}function rC(a){var b=a.eventId,c=a.Nd;if(!oC[b])return[];var d=[];pC[b]||d.push(nC);d.push.apply(d,va(oC[b]));c&&(pC[b]=!0);return d};var sC={},tC={},uC={};function vC(a,b,c,d){il&&F(120)&&((d===void 0?0:d)?(uC[b]=uC[b]||0,++uC[b]):c!==void 0?(tC[a]=tC[a]||{},tC[a][b]=Math.round(c)):(sC[a]=sC[a]||{},sC[a][b]=(sC[a][b]||0)+1))}function wC(a){var b=a.eventId,c=a.Nd,d=sC[b]||{},e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(""+f+d[f]);c&&delete sC[b];return e.length?[["md",e.join(".")]]:[]}
function xC(a){var b=a.eventId,c=a.Nd,d=tC[b]||{},e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(""+f+d[f]);c&&delete tC[b];return e.length?[["mtd",e.join(".")]]:[]}function yC(){for(var a=[],b=l(Object.keys(uC)),c=b.next();!c.done;c=b.next()){var d=c.value;a.push(""+d+uC[d])}return a.length?[["mec",a.join(".")]]:[]};var zC={},AC={};function BC(a,b,c){if(il&&b){var d=el(b);zC[a]=zC[a]||[];zC[a].push(c+d);var e=b[ff.Ra];if(!e)throw Error("Error: No function name given for function call.");var f=(If[e]?"1":"2")+d;AC[a]=AC[a]||[];AC[a].push(f);yq(a)}}function CC(a){var b=a.eventId,c=a.Nd,d=[],e=zC[b]||[];e.length&&d.push(["tr",e.join(".")]);var f=AC[b]||[];f.length&&d.push(["ti",f.join(".")]);c&&(delete zC[b],delete AC[b]);return d};function DC(a,b,c){c=c===void 0?!1:c;EC().addRestriction(0,a,b,c)}function FC(a,b,c){c=c===void 0?!1:c;EC().addRestriction(1,a,b,c)}function GC(){var a=Bm();return EC().getRestrictions(1,a)}var HC=function(){this.container={};this.C={}},IC=function(a,b){var c=a.container[b];c||(c={_entity:{internal:[],external:[]},_event:{internal:[],external:[]}},a.container[b]=c);return c};
HC.prototype.addRestriction=function(a,b,c,d){d=d===void 0?!1:d;if(!d||!this.C[b]){var e=IC(this,b);a===0?d?e._entity.external.push(c):e._entity.internal.push(c):a===1&&(d?e._event.external.push(c):e._event.internal.push(c))}};
HC.prototype.getRestrictions=function(a,b){var c=IC(this,b);if(a===0){var d,e;return[].concat(va((c==null?void 0:(d=c._entity)==null?void 0:d.internal)||[]),va((c==null?void 0:(e=c._entity)==null?void 0:e.external)||[]))}if(a===1){var f,g;return[].concat(va((c==null?void 0:(f=c._event)==null?void 0:f.internal)||[]),va((c==null?void 0:(g=c._event)==null?void 0:g.external)||[]))}return[]};
HC.prototype.getExternalRestrictions=function(a,b){var c=IC(this,b),d,e;return a===0?(c==null?void 0:(d=c._entity)==null?void 0:d.external)||[]:(c==null?void 0:(e=c._event)==null?void 0:e.external)||[]};HC.prototype.removeExternalRestrictions=function(a){var b=IC(this,a);b._event&&(b._event.external=[]);b._entity&&(b._entity.external=[]);this.C[a]=!0};function EC(){return wp("r",function(){return new HC})};function JC(a,b,c,d){var e=Gf[a],f=KC(a,b,c,d);if(!f)return null;var g=Vf(e[ff.Cl],c,[]);if(g&&g.length){var h=g[0];f=JC(h.index,{onSuccess:f,onFailure:h.Xl===1?b.terminate:f,terminate:b.terminate},c,d)}return f}
function KC(a,b,c,d){function e(){function w(){bo(3);var N=zb()-I;BC(c.id,f,"7");YB(c.Pc,E,"exception",N);F(109)&&EA(c,f,Yz.O.Mi);G||(G=!0,h())}if(f[ff.jo])h();else{var y=Uf(f,c,[]),A=y[ff.Pm];if(A!=null)for(var C=0;C<A.length;C++)if(!Q(A[C])){h();return}var E=XB(c.Pc,String(f[ff.Ra]),Number(f[ff.kh]),y[ff.METADATA]),G=!1;y.vtp_gtmOnSuccess=function(){if(!G){G=!0;var N=zb()-I;BC(c.id,Gf[a],"5");YB(c.Pc,E,"success",N);F(109)&&EA(c,f,Yz.O.Oi);g()}};y.vtp_gtmOnFailure=function(){if(!G){G=!0;var N=zb()-
I;BC(c.id,Gf[a],"6");YB(c.Pc,E,"failure",N);F(109)&&EA(c,f,Yz.O.Ni);h()}};y.vtp_gtmTagId=f.tag_id;y.vtp_gtmEventId=c.id;c.priorityId&&(y.vtp_gtmPriorityId=c.priorityId);BC(c.id,f,"1");F(109)&&DA(c,f);var I=zb();try{Wf(y,{event:c,index:a,type:1})}catch(N){w(N)}F(109)&&EA(c,f,Yz.O.Jl)}}var f=Gf[a],g=b.onSuccess,h=b.onFailure,m=b.terminate;if(c.isBlocked(f))return null;var n=Vf(f[ff.Kl],c,[]);if(n&&n.length){var p=n[0],q=JC(p.index,{onSuccess:g,onFailure:h,terminate:m},c,d);if(!q)return null;g=q;h=p.Xl===
2?m:q}if(f[ff.rl]||f[ff.lo]){var r=f[ff.rl]?Hf:c.Cq,t=g,u=h;if(!r[a]){var v=LC(a,r,Bb(e));g=v.onSuccess;h=v.onFailure}return function(){r[a](t,u)}}return e}function LC(a,b,c){var d=[],e=[];b[a]=MC(d,e,c);return{onSuccess:function(){b[a]=NC;for(var f=0;f<d.length;f++)d[f]()},onFailure:function(){b[a]=OC;for(var f=0;f<e.length;f++)e[f]()}}}function MC(a,b,c){return function(d,e){a.push(d);b.push(e);c()}}function NC(a){a()}function OC(a,b){b()};var RC=function(a,b){for(var c=[],d=0;d<Gf.length;d++)if(a[d]){var e=Gf[d];var f=$B(b.Pc);try{var g=JC(d,{onSuccess:f,onFailure:f,terminate:f},b,d);if(g){var h=e[ff.Ra];if(!h)throw Error("Error: No function name given for function call.");var m=If[h];c.push({Hm:d,priorityOverride:(m?m.priorityOverride||0:0)||UB(e[ff.Ra],1)||0,execute:g})}else PC(d,b),f()}catch(p){f()}}c.sort(QC);for(var n=0;n<c.length;n++)c[n].execute();
return c.length>0};function SC(a,b){if(!RB)return!1;var c=a["gtm.triggers"]&&String(a["gtm.triggers"]),d=SB(a.event,c?String(c).split(","):[]);if(!d.length)return!1;for(var e=0;e<d.length;++e){var f=$B(b);try{d[e](a,f)}catch(g){f()}}return!0}function QC(a,b){var c,d=b.priorityOverride,e=a.priorityOverride;c=d>e?1:d<e?-1:0;var f;if(c!==0)f=c;else{var g=a.Hm,h=b.Hm;f=g>h?1:g<h?-1:0}return f}
function PC(a,b){if(il){var c=function(d){var e=b.isBlocked(Gf[d])?"3":"4",f=Vf(Gf[d][ff.Cl],b,[]);f&&f.length&&c(f[0].index);BC(b.id,Gf[d],e);var g=Vf(Gf[d][ff.Kl],b,[]);g&&g.length&&c(g[0].index)};c(a)}}var TC=!1,RB;function UC(){RB||(RB=new QB);return RB}
function VC(a){var b=a["gtm.uniqueEventId"],c=a["gtm.priorityId"],d=a.event;if(F(109)){}if(d==="gtm.js"){if(TC)return!1;TC=!0}var e=!1,f=GC(),g=ld(a,null);if(!f.every(function(t){return t({originalEventData:g})})){if(d!=="gtm.js"&&d!=="gtm.init"&&d!=="gtm.init_consent")return!1;e=!0}qC(b,d);var h=a.eventCallback,m=
a.eventTimeout,n={id:b,priorityId:c,name:d,isBlocked:WC(g,e),Cq:[],logMacroError:function(){M(6);bo(0)},cachedModelValues:XC(),Pc:new WB(function(){if(F(109)){}h&&h.apply(h,Array.prototype.slice.call(arguments,0))},m),
originalEventData:g};F(120)&&il&&(n.reportMacroDiscrepancy=vC);F(109)&&AA(n.id);var p=ag(n);F(109)&&BA(n.id);e&&(p=YC(p));F(109)&&zA(b);var q=RC(p,n),r=SC(a,n.Pc);aC(n.Pc);d!=="gtm.js"&&d!=="gtm.sync"||gC();return ZC(p,q)||r}function XC(){var a={};a.event=zk("event",1);a.ecommerce=zk("ecommerce",1);a.gtm=zk("gtm");a.eventModel=zk("eventModel");return a}
function WC(a,b){var c=IB();return function(d){if(c(d))return!0;var e=d&&d[ff.Ra];if(!e||typeof e!=="string")return!0;e=e.replace(/^_*/,"");var f,g=Bm();f=EC().getRestrictions(0,g);var h=a;b&&(h=ld(a,null),h["gtm.uniqueEventId"]=Number.MAX_SAFE_INTEGER);for(var m=kk[e]||[],n=l(f),p=n.next();!p.done;p=n.next()){var q=p.value;try{if(!q({entityId:e,securityGroups:m,originalEventData:h}))return!0}catch(r){return!0}}return!1}}
function YC(a){for(var b=[],c=0;c<a.length;c++)if(a[c]){var d=String(Gf[c][ff.Ra]);if(Xj[d]||Gf[c][ff.mo]!==void 0||UB(d,2))b[c]=!0}return b}function ZC(a,b){if(!b)return b;for(var c=0;c<a.length;c++)if(a[c]&&Gf[c]&&!Yj[String(Gf[c][ff.Ra])])return!0;return!1};function $C(){UC().addListener("gtm.init",function(a,b){Pj.ba=!0;Mn();b()})};var aD=!1,bD=0,cD=[];function dD(a){if(!aD){var b=z.createEventObject,c=z.readyState==="complete",d=z.readyState==="interactive";if(!a||a.type!=="readystatechange"||c||!b&&d){aD=!0;for(var e=0;e<cD.length;e++)B(cD[e])}cD.push=function(){for(var f=za.apply(0,arguments),g=0;g<f.length;g++)B(f[g]);return 0}}}function eD(){if(!aD&&bD<140){bD++;try{var a,b;(b=(a=z.documentElement).doScroll)==null||b.call(a,"left");dD()}catch(c){x.setTimeout(eD,50)}}}
function fD(){var a=x;aD=!1;bD=0;if(z.readyState==="interactive"&&!z.createEventObject||z.readyState==="complete")dD();else{Kc(z,"DOMContentLoaded",dD);Kc(z,"readystatechange",dD);if(z.createEventObject&&z.documentElement.doScroll){var b=!0;try{b=!a.frameElement}catch(c){}b&&eD()}Kc(a,"load",dD)}}function gD(a){aD?a():cD.push(a)};var hD={},iD={};function jD(a,b){for(var c=[],d=[],e={},f=0;f<a.length;e={wj:void 0,cj:void 0},f++){var g=a[f];if(g.indexOf("-")>=0){if(e.wj=Gp(g,b),e.wj){var h=Am();nb(h,function(r){return function(t){return r.wj.destinationId===t}}(e))?c.push(g):d.push(g)}}else{var m=hD[g]||[];e.cj={};m.forEach(function(r){return function(t){r.cj[t]=!0}}(e));for(var n=Cm(),p=0;p<n.length;p++)if(e.cj[n[p]]){c=c.concat(Am());break}var q=iD[g]||[];q.length&&(c=c.concat(q))}}return{qj:c,Wp:d}}
function kD(a){rb(hD,function(b,c){var d=c.indexOf(a);d>=0&&c.splice(d,1)})}function lD(a){rb(iD,function(b,c){var d=c.indexOf(a);d>=0&&c.splice(d,1)})};var mD=!1,nD=!1;function oD(a,b){var c={},d=(c.event=a,c);b&&(d.eventModel=ld(b,null),b[K.m.cf]&&(d.eventCallback=b[K.m.cf]),b[K.m.Kg]&&(d.eventTimeout=b[K.m.Kg]));return d}function pD(a,b){a.hasOwnProperty("gtm.uniqueEventId")||Object.defineProperty(a,"gtm.uniqueEventId",{value:zp()});b.eventId=a["gtm.uniqueEventId"];b.priorityId=a["gtm.priorityId"];return{eventId:b.eventId,priorityId:b.priorityId}}
function qD(a,b){var c=a&&a[K.m.ld];c===void 0&&(c=uk(K.m.ld,2),c===void 0&&(c="default"));if(kb(c)||Array.isArray(c)){var d;d=b.isGtmEvent?kb(c)?[c]:c:c.toString().replace(/\s+/g,"").split(",");var e=jD(d,b.isGtmEvent),f=e.qj,g=e.Wp;if(g.length)for(var h=rD(a),m=0;m<g.length;m++){var n=Gp(g[m],b.isGtmEvent);if(n){var p=n.destinationId,q=n.destinationId,r=um().destination[q];r&&r.state===0||PB(p,h,{source:3,fromContainerExecution:b.fromContainerExecution})}}var t=f.concat(g);return{qj:Hp(f,b.isGtmEvent),
Co:Hp(t,b.isGtmEvent)}}}var sD=void 0,tD=void 0;function uD(a,b,c){var d=ld(a,null);d.eventId=void 0;d.inheritParentConfig=void 0;Object.keys(b).some(function(f){return b[f]!==void 0})&&M(136);var e=ld(b,null);ld(c,e);Qw(Mw(Cm()[0],e),a.eventId,d)}function rD(a){for(var b=l([K.m.md,K.m.vc]),c=b.next();!c.done;c=b.next()){var d=c.value,e=a&&a[d]||Gq.C[d];if(e)return e}}
var vD={config:function(a,b){var c=pD(a,b);if(!(a.length<2)&&kb(a[1])){var d={};if(a.length>2){if(a[2]!==void 0&&!kd(a[2])||a.length>3)return;d=a[2]}var e=Gp(a[1],b.isGtmEvent);if(e){var f,g,h;a:{if(!ym.pe){var m=Em(tm());if(Nm(m)){var n=m.parent,p=n.isDestination;h={Yp:Em(n),Sp:p};break a}}h=void 0}var q=h;q&&(f=q.Yp,g=q.Sp);qC(c.eventId,"gtag.config");var r=e.destinationId,t=e.id!==r;if(t?Am().indexOf(r)===-1:Cm().indexOf(r)===-1){if(!b.inheritParentConfig&&!d[K.m.Lc]){var u=rD(d);if(t)PB(r,u,{source:2,
fromContainerExecution:b.fromContainerExecution});else if(f!==void 0&&f.containers.indexOf(r)!==-1){var v=d;sD?uD(b,v,sD):tD||(tD=ld(v,null))}else LB(r,u,!0,{source:2,fromContainerExecution:b.fromContainerExecution})}}else{if(f&&(M(128),g&&M(130),b.inheritParentConfig)){var w;var y=d;tD?(uD(b,tD,y),w=!1):(!y[K.m.od]&&ak&&sD||(sD=ld(y,null)),w=!0);w&&f.containers&&f.containers.join(",");return}jl&&(Bp===1&&(En.mcc=!1),Bp=2);if(ak&&!t&&!d[K.m.od]){var A=nD;nD=!0;if(A)return}mD||M(43);if(!b.noTargetGroup)if(t){lD(e.id);
var C=e.id,E=d[K.m.Ng]||"default";E=String(E).split(",");for(var G=0;G<E.length;G++){var I=iD[E[G]]||[];iD[E[G]]=I;I.indexOf(C)<0&&I.push(C)}}else{kD(e.id);var N=e.id,T=d[K.m.Ng]||"default";T=T.toString().split(",");for(var ca=0;ca<T.length;ca++){var P=hD[T[ca]]||[];hD[T[ca]]=P;P.indexOf(N)<0&&P.push(N)}}delete d[K.m.Ng];var ha=b.eventMetadata||{};ha.hasOwnProperty(R.A.ud)||(ha[R.A.ud]=!b.fromContainerExecution);b.eventMetadata=ha;delete d[K.m.cf];for(var da=t?[e.id]:Am(),ka=0;ka<da.length;ka++){var X=
d,W=da[ka],ta=ld(b,null),ra=Gp(W,ta.isGtmEvent);ra&&Gq.push("config",[X],ra,ta)}}}}},consent:function(a,b){if(a.length===3){M(39);var c=pD(a,b),d=a[1],e={},f=Fo(a[2]),g;for(g in f)if(f.hasOwnProperty(g)){var h=f[g];e[g]=g===K.m.qg?Array.isArray(h)?NaN:Number(h):g===K.m.fc?(Array.isArray(h)?h:[h]).map(Go):Ho(h)}b.fromContainerExecution||(e[K.m.V]&&M(139),e[K.m.La]&&M(140));d==="default"?ip(e):d==="update"?kp(e,c):d==="declare"&&b.fromContainerExecution&&hp(e)}},event:function(a,b){var c=a[1];if(!(a.length<
2)&&kb(c)){var d=void 0;if(a.length>2){if(!kd(a[2])&&a[2]!==void 0||a.length>3)return;d=a[2]}var e=oD(c,d),f=pD(a,b),g=f.eventId,h=f.priorityId;e["gtm.uniqueEventId"]=g;h&&(e["gtm.priorityId"]=h);if(c==="optimize.callback")return e.eventModel=e.eventModel||{},e;var m=qD(d,b);if(m){for(var n=m.qj,p=m.Co,q=p.map(function(N){return N.id}),r=p.map(function(N){return N.destinationId}),t=n.map(function(N){return N.id}),u=l(Am()),v=u.next();!v.done;v=u.next()){var w=v.value;r.indexOf(w)<0&&t.push(w)}qC(g,
c);for(var y=l(t),A=y.next();!A.done;A=y.next()){var C=A.value,E=ld(b,null),G=ld(d,null);delete G[K.m.cf];var I=E.eventMetadata||{};I.hasOwnProperty(R.A.ud)||(I[R.A.ud]=!E.fromContainerExecution);I[R.A.Hi]=q.slice();I[R.A.Of]=r.slice();E.eventMetadata=I;Hq(c,G,C,E)}e.eventModel=e.eventModel||{};q.length>0?e.eventModel[K.m.ld]=q.join(","):delete e.eventModel[K.m.ld];mD||M(43);b.noGtmEvent===void 0&&b.eventMetadata&&b.eventMetadata[R.A.Hl]&&(b.noGtmEvent=!0);e.eventModel[K.m.Kc]&&(b.noGtmEvent=!0);
return b.noGtmEvent?void 0:e}}},get:function(a,b){M(53);if(a.length===4&&kb(a[1])&&kb(a[2])&&jb(a[3])){var c=Gp(a[1],b.isGtmEvent),d=String(a[2]),e=a[3];if(c){mD||M(43);var f=rD();if(nb(Am(),function(h){return c.destinationId===h})){pD(a,b);var g={};ld((g[K.m.qc]=d,g[K.m.Ic]=e,g),null);Iq(d,function(h){B(function(){e(h)})},c.id,b)}else PB(c.destinationId,f,{source:4,fromContainerExecution:b.fromContainerExecution})}}},js:function(a,b){if(a.length===2&&a[1].getTime){mD=!0;var c=pD(a,b),d=c.eventId,
e=c.priorityId,f={};return f.event="gtm.js",f["gtm.start"]=a[1].getTime(),f["gtm.uniqueEventId"]=d,f["gtm.priorityId"]=e,f}},policy:function(a){if(a.length===3&&kb(a[1])&&jb(a[2])){if(gg(a[1],a[2]),M(74),a[1]==="all"){M(75);var b=!1;try{b=a[2](jg.ctid,"unknown",{})}catch(c){}b||M(76)}}else M(73)},set:function(a,b){var c=void 0;a.length===2&&kd(a[1])?c=ld(a[1],null):a.length===3&&kb(a[1])&&(c={},kd(a[2])||Array.isArray(a[2])?c[a[1]]=ld(a[2],null):c[a[1]]=a[2]);if(c){var d=pD(a,b),e=d.eventId,f=d.priorityId;
ld(c,null);var g=ld(c,null);Gq.push("set",[g],void 0,b);c["gtm.uniqueEventId"]=e;f&&(c["gtm.priorityId"]=f);delete c.event;b.overwriteModelFields=!0;return c}}},wD={policy:!0};var yD=function(a){if(xD(a))return a;this.value=a};yD.prototype.getUntrustedMessageValue=function(){return this.value};var xD=function(a){return!a||id(a)!=="object"||kd(a)?!1:"getUntrustedMessageValue"in a};yD.prototype.getUntrustedMessageValue=yD.prototype.getUntrustedMessageValue;var zD=!1,AD=[];function BD(){if(!zD){zD=!0;for(var a=0;a<AD.length;a++)B(AD[a])}}function CD(a){zD?B(a):AD.push(a)};var DD=0,ED={},FD=[],GD=[],HD=!1,ID=!1;function JD(a,b){return a.messageContext.eventId-b.messageContext.eventId||a.messageContext.priorityId-b.messageContext.priorityId}function KD(a,b,c){a.eventCallback=b;c&&(a.eventTimeout=c);return LD(a)}function MD(a,b){if(!lb(b)||b<0)b=0;var c=vp[Wj],d=0,e=!1,f=void 0;f=x.setTimeout(function(){e||(e=!0,a());f=void 0},b);return function(){var g=c?c.subscribers:1;++d===g&&(f&&(x.clearTimeout(f),f=void 0),e||(a(),e=!0))}}
function ND(a,b){var c=a._clear||b.overwriteModelFields;rb(a,function(e,f){e!=="_clear"&&(c&&xk(e),xk(e,f))});hk||(hk=a["gtm.start"]);var d=a["gtm.uniqueEventId"];if(!a.event)return!1;typeof d!=="number"&&(d=zp(),a["gtm.uniqueEventId"]=d,xk("gtm.uniqueEventId",d));return VC(a)}function OD(a){if(a==null||typeof a!=="object")return!1;if(a.event)return!0;if(tb(a)){var b=a[0];if(b==="config"||b==="event"||b==="js"||b==="get")return!0}return!1}
function PD(){var a;if(GD.length)a=GD.shift();else if(FD.length)a=FD.shift();else return;var b;var c=a;if(HD||!OD(c.message))b=c;else{HD=!0;var d=c.message["gtm.uniqueEventId"],e,f;typeof d==="number"?(e=d-2,f=d-1):(e=zp(),f=zp(),c.message["gtm.uniqueEventId"]=zp());var g={},h={message:(g.event="gtm.init_consent",g["gtm.uniqueEventId"]=e,g),messageContext:{eventId:e}},m={},n={message:(m.event="gtm.init",m["gtm.uniqueEventId"]=f,m),messageContext:{eventId:f}};FD.unshift(n,c);b=h}return b}
function QD(){for(var a=!1,b;!ID&&(b=PD());){ID=!0;delete rk.eventModel;tk();var c=b,d=c.message,e=c.messageContext;if(d==null)ID=!1;else{e.fromContainerExecution&&yk();try{if(jb(d))try{d.call(vk)}catch(u){}else if(Array.isArray(d)){if(kb(d[0])){var f=d[0].split("."),g=f.pop(),h=d.slice(1),m=uk(f.join("."),2);if(m!=null)try{m[g].apply(m,h)}catch(u){}}}else{var n=void 0;if(tb(d))a:{if(d.length&&kb(d[0])){var p=vD[d[0]];if(p&&(!e.fromContainerExecution||!wD[d[0]])){n=p(d,e);break a}}n=void 0}else n=
d;n&&(a=ND(n,e)||a)}}finally{e.fromContainerExecution&&tk(!0);var q=d["gtm.uniqueEventId"];if(typeof q==="number"){for(var r=ED[String(q)]||[],t=0;t<r.length;t++)GD.push(RD(r[t]));r.length&&GD.sort(JD);delete ED[String(q)];q>DD&&(DD=q)}ID=!1}}}return!a}
function SD(){if(F(109)){var a=!Pj.ka;}var c=QD();if(F(109)){}try{var e=jg.ctid,f=x[Wj].hide;if(f&&f[e]!==void 0&&f.end){f[e]=
!1;var g=!0,h;for(h in f)if(f.hasOwnProperty(h)&&f[h]===!0){g=!1;break}g&&(f.end(),f.end=null)}}catch(m){}return c}function Tw(a){if(DD<a.notBeforeEventId){var b=String(a.notBeforeEventId);ED[b]=ED[b]||[];ED[b].push(a)}else GD.push(RD(a)),GD.sort(JD),B(function(){ID||QD()})}function RD(a){return{message:a.message,messageContext:a.messageContext}}
function TD(){function a(f){var g={};if(xD(f)){var h=f;f=xD(h)?h.getUntrustedMessageValue():void 0;g.fromContainerExecution=!0}return{message:f,messageContext:g}}var b=xc(Wj,[]),c=vp[Wj]=vp[Wj]||{};c.pruned===!0&&M(83);ED=Rw().get();Sw();gD(function(){if(!c.gtmDom){c.gtmDom=!0;var f={};b.push((f.event="gtm.dom",f))}});CD(function(){if(!c.gtmLoad){c.gtmLoad=!0;var f={};b.push((f.event="gtm.load",f))}});c.subscribers=(c.subscribers||0)+1;var d=b.push;b.push=function(){var f;if(vp.SANDBOXED_JS_SEMAPHORE>
0){f=[];for(var g=0;g<arguments.length;g++)f[g]=new yD(arguments[g])}else f=[].slice.call(arguments,0);var h=f.map(function(q){return a(q)});FD.push.apply(FD,h);var m=d.apply(b,f),n=Math.max(100,Number("1000")||300);if(this.length>n)for(M(4),c.pruned=!0;this.length>n;)this.shift();var p=typeof m!=="boolean"||m;return QD()&&p};var e=b.slice(0).map(function(f){return a(f)});FD.push.apply(FD,e);if(!Pj.ka){if(F(109)){}B(SD)}}var LD=function(a){return x[Wj].push(a)};function UD(a){LD(a)};function VD(){var a,b=Uk(x.location.href);(a=b.hostname+b.pathname)&&In("dl",encodeURIComponent(a));var c;var d=jg.ctid;if(d){var e=ym.pe?1:0,f,g=Em(tm());f=g&&g.context;c=d+";"+jg.canonicalContainerId+";"+(f&&f.fromContainerExecution?1:0)+";"+(f&&f.source||0)+";"+e}else c=void 0;var h=c;h&&In("tdp",h);var m=Jl(!0);m!==void 0&&In("frm",String(m))};function WD(){(So()||jl)&&x.addEventListener("securitypolicyviolation",function(a){if(a.disposition==="enforce"){M(179);var b=fm(a.effectiveDirective);if(b){var c;var d=dm(b,a.blockedURI);c=d?bm[b][d]:void 0;if(c){var e;a:{try{var f=new URL(a.blockedURI),g=f.pathname.indexOf(";");e=g>=0?f.origin+f.pathname.substring(0,g):f.origin+f.pathname;break a}catch(v){}e=void 0}var h=e;if(h){for(var m=l(c),n=m.next();!n.done;n=m.next()){var p=n.value;if(!p.Am){p.Am=!0;if(F(59)){var q={eventId:p.eventId,priorityId:p.priorityId};
if(So()){var r=q,t={type:1,blockedUrl:h,endpoint:p.endpoint,violation:a.effectiveDirective};if(So()){var u=Yo("TAG_DIAGNOSTICS",{eventId:r==null?void 0:r.eventId,priorityId:r==null?void 0:r.priorityId});u.tagDiagnostics=t;Ro(u)}}}On(p.endpoint)}}em(b,a.blockedURI)}}}}})};function XD(){var a;var b=Dm();if(b)if(b.canonicalContainerId)a=b.canonicalContainerId;else{var c,d=b.scriptContainerId||((c=b.destinations)==null?void 0:c[0]);a=d?"_"+d:void 0}else a=void 0;var e=a;e&&In("pcid",e)};var YD=/^(https?:)?\/\//;
function ZD(){var a=Fm();if(a){var b;a:{var c,d=(c=a.scriptElement)==null?void 0:c.src;if(d){var e;try{var f;e=(f=$c())==null?void 0:f.getEntriesByType("resource")}catch(q){}if(e){for(var g=-1,h=l(e),m=h.next();!m.done;m=h.next()){var n=m.value;if(n.initiatorType==="script"&&(g+=1,n.name.replace(YD,"")===d.replace(YD,""))){b=g;break a}}M(146)}else M(145)}b=void 0}var p=b;p!==void 0&&(a.canonicalContainerId&&In("rtg",String(a.canonicalContainerId)),In("slo",String(p)),In("hlo",a.htmlLoadOrder||"-1"),
In("lst",String(a.loadScriptType||"0")))}else M(144)};function $D(){var a=[],b=Number('1')||0,c=Number('')||0;c||(c=b/100);var d=function(){var h=!1;return h}();a.push({Gm:195,studyId:195,experimentId:104527906,controlId:104527907,controlId2:104898015,probability:c,active:d,Vi:1});var e=Number('1')||
0,f=Number('')||0;f||(f=e/100);var g=function(){var h=!1;return h}();a.push({Gm:196,studyId:196,experimentId:104528500,controlId:104528501,controlId2:104898016,probability:f,active:g,Vi:0});return a};var aE={};function bE(a){for(var b=l(Object.keys(a.exp||{})),c=b.next();!c.done;c=b.next())Pj.R.H.add(Number(c.value))}function cE(a){var b=wn(rn.Z.sl);return pi(a)||!!(b.exp||{})[ki[a].experimentId]||pi(a)||!!(aE.exp||{})[ki[a].experimentId]}function dE(){for(var a=l($D()),b=a.next();!b.done;b=a.next()){var c=b.value,d=c.Gm;ki[d]=c;if(c.Vi===1){var e=d,f=wn(rn.Z.sl);oi(f,e);bE(f);cE(e)&&D(e)}else if(c.Vi===0){var g=d,h=aE;oi(h,g);bE(h);cE(g)&&D(g)}}};
function yE(){};var zE=function(){};zE.prototype.toString=function(){return"undefined"};var AE=new zE;function HE(a,b){function c(g){var h=Uk(g),m=Ok(h,"protocol"),n=Ok(h,"host",!0),p=Ok(h,"port"),q=Ok(h,"path").toLowerCase().replace(/\/$/,"");if(m===void 0||m==="http"&&p==="80"||m==="https"&&p==="443")m="web",p="default";return[m,n,p,q]}for(var d=c(String(a)),e=c(String(b)),f=0;f<d.length;f++)if(d[f]!==e[f])return!1;return!0}
function IE(a){return JE(a)?1:0}
function JE(a){var b=a.arg0,c=a.arg1;if(a.any_of&&Array.isArray(c)){for(var d=0;d<c.length;d++){var e=ld(a,{});ld({arg1:c[d],any_of:void 0},e);if(IE(e))return!0}return!1}switch(a["function"]){case "_cn":return Pg(b,c);case "_css":var f;a:{if(b)try{for(var g=0;g<Kg.length;g++){var h=Kg[g];if(b[h]!=null){f=b[h](c);break a}}}catch(m){}f=!1}return f;case "_ew":return Lg(b,c);case "_eq":return Qg(b,c);case "_ge":return Rg(b,c);case "_gt":return Tg(b,c);case "_lc":return Mg(b,c);case "_le":return Sg(b,
c);case "_lt":return Ug(b,c);case "_re":return Og(b,c,a.ignore_case);case "_sw":return Vg(b,c);case "_um":return HE(b,c)}return!1};[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});var KE=function(a,b,c,d){Xq.call(this);this.gh=b;this.Kf=c;this.qb=d;this.Sa=new Map;this.hh=0;this.ka=new Map;this.Ba=new Map;this.R=void 0;this.H=a};sa(KE,Xq);KE.prototype.N=function(){delete this.C;this.Sa.clear();this.ka.clear();this.Ba.clear();this.R&&(Tq(this.H,"message",this.R),delete this.R);delete this.H;delete this.qb;Xq.prototype.N.call(this)};
var LE=function(a){if(a.C)return a.C;a.Kf&&a.Kf(a.H)?a.C=a.H:a.C=Il(a.H,a.gh);var b;return(b=a.C)!=null?b:null},NE=function(a,b,c){if(LE(a))if(a.C===a.H){var d=a.Sa.get(b);d&&d(a.C,c)}else{var e=a.ka.get(b);if(e&&e.pj){ME(a);var f=++a.hh;a.Ba.set(f,{Dh:e.Dh,Vo:e.gm(c),persistent:b==="addEventListener"});a.C.postMessage(e.pj(c,f),"*")}}},ME=function(a){a.R||(a.R=function(b){try{var c;c=a.qb?a.qb(b):void 0;if(c){var d=c.bq,e=a.Ba.get(d);if(e){e.persistent||a.Ba.delete(d);var f;(f=e.Dh)==null||f.call(e,
e.Vo,c.payload)}}}catch(g){}},Sq(a.H,"message",a.R))};var OE=function(a,b){var c=b.listener,d=(0,a.__gpp)("addEventListener",c);d&&c(d,!0)},PE=function(a,b){(0,a.__gpp)("removeEventListener",b.listener,b.listenerId)},QE={gm:function(a){return a.listener},pj:function(a,b){var c={};return c.__gppCall={callId:b,command:"addEventListener",version:"1.1"},c},Dh:function(a,b){var c=b.__gppReturn;a(c.returnValue,c.success)}},RE={gm:function(a){return a.listener},pj:function(a,b){var c={};return c.__gppCall={callId:b,command:"removeEventListener",version:"1.1",
parameter:a.listenerId},c},Dh:function(a,b){var c=b.__gppReturn,d=c.returnValue.data;a==null||a(d,c.success)}};function SE(a){var b={};typeof a.data==="string"?b=JSON.parse(a.data):b=a.data;return{payload:b,bq:b.__gppReturn.callId}}
var TE=function(a,b){var c;c=(b===void 0?{}:b).timeoutMs;Xq.call(this);this.caller=new KE(a,"__gppLocator",function(d){return typeof d.__gpp==="function"},SE);this.caller.Sa.set("addEventListener",OE);this.caller.ka.set("addEventListener",QE);this.caller.Sa.set("removeEventListener",PE);this.caller.ka.set("removeEventListener",RE);this.timeoutMs=c!=null?c:500};sa(TE,Xq);TE.prototype.N=function(){this.caller.dispose();Xq.prototype.N.call(this)};
TE.prototype.addEventListener=function(a){var b=this,c=ll(function(){a(UE,!0)}),d=this.timeoutMs===-1?void 0:setTimeout(function(){c()},this.timeoutMs);NE(this.caller,"addEventListener",{listener:function(e,f){clearTimeout(d);try{var g;var h;((h=e.pingData)==null?void 0:h.gppVersion)===void 0||e.pingData.gppVersion==="1"||e.pingData.gppVersion==="1.0"?(b.removeEventListener(e.listenerId),g={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:1,gppString:"GPP_ERROR_STRING_IS_DEPRECATED_SPEC",
applicableSections:[-1]}}):Array.isArray(e.pingData.applicableSections)?g=e:(b.removeEventListener(e.listenerId),g={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_EXPECTED_APPLICATION_SECTION_ARRAY",applicableSections:[-1]}});a(g,f)}catch(m){if(e==null?0:e.listenerId)try{b.removeEventListener(e.listenerId)}catch(n){a(VE,!0);return}a(WE,!0)}}})};
TE.prototype.removeEventListener=function(a){NE(this.caller,"removeEventListener",{listener:function(){},listenerId:a})};
var WE={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_UNAVAILABLE",applicableSections:[-1]},listenerId:-1},UE={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_LISTENER_REGISTRATION_TIMEOUT",internalErrorState:2,applicableSections:[-1]},listenerId:-1},VE={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_REMOVE_EVENT_LISTENER_ERROR",internalErrorState:2,applicableSections:[-1]},listenerId:-1};function XE(a){var b;if(!(b=a.pingData.signalStatus==="ready")){var c=a.pingData.applicableSections;b=!c||c.length===1&&c[0]===-1}if(b){zv.gppString=a.pingData.gppString;var d=a.pingData.applicableSections.join(",");zv.C=d}}function YE(){try{var a=new TE(x,{timeoutMs:-1});LE(a.caller)&&a.addEventListener(XE)}catch(b){}};function ZE(){var a=[["cv",Ti(1)],["rv",Uj],["tc",Gf.filter(function(b){return b}).length]];Vj&&a.push(["x",Vj]);mk()&&a.push(["tag_exp",mk()]);return a};var $E={};function Wi(a){$E[a]=($E[a]||0)+1}function aF(){for(var a=[],b=l(Object.keys($E)),c=b.next();!c.done;c=b.next()){var d=c.value;a.push(d+"."+$E[d])}return a.length===0?[]:[["bdm",a.join("~")]]};var bF={},cF={};function dF(a){var b=a.eventId,c=a.Nd,d=[],e=bF[b]||[];e.length&&d.push(["hf",e.join(".")]);var f=cF[b]||[];f.length&&d.push(["ht",f.join(".")]);c&&(delete bF[b],delete cF[b]);return d};function eF(){return!1}function fF(){var a={};return function(b,c,d){}};function gF(){var a=hF;return function(b,c,d){var e=d&&d.event;iF(c);var f=Ah(b)?void 0:1,g=new Ua;rb(c,function(r,t){var u=Bd(t,void 0,f);u===void 0&&t!==void 0&&M(44);g.set(r,u)});a.Nb(Zf());var h={Ql:ng(b),eventId:e==null?void 0:e.id,priorityId:e!==void 0?e.priorityId:void 0,Rf:e!==void 0?function(r){e.Pc.Rf(r)}:void 0,Jb:function(){return b},log:function(){},hp:{index:d==null?void 0:d.index,type:d==null?void 0:d.type,name:d==null?void 0:d.name},mq:!!UB(b,3),originalEventData:e==null?void 0:e.originalEventData};
e&&e.cachedModelValues&&(h.cachedModelValues={gtm:e.cachedModelValues.gtm,ecommerce:e.cachedModelValues.ecommerce});if(eF()){var m=fF(),n,p;h.wb={Ej:[],Sf:{},ac:function(r,t,u){t===1&&(n=r);t===7&&(p=u);m(r,t,u)},Bh:Sh()};h.log=function(r){var t=za.apply(1,arguments);n&&m(n,4,{level:r,source:p,message:t})}}var q=Xe(a,h,[b,g]);a.Nb();q instanceof Ca&&(q.type==="return"?q=q.data:q=void 0);return Ad(q,void 0,f)}}function iF(a){var b=a.gtmOnSuccess,c=a.gtmOnFailure;jb(b)&&(a.gtmOnSuccess=function(){B(b)});jb(c)&&(a.gtmOnFailure=function(){B(c)})};function jF(a){}jF.M="internal.addAdsClickIds";function kF(a,b){var c=this;}kF.publicName="addConsentListener";var lF=!1;function mF(a){for(var b=0;b<a.length;++b)if(lF)try{a[b]()}catch(c){M(77)}else a[b]()}function nF(a,b,c){var d=this,e;return e}nF.M="internal.addDataLayerEventListener";function oF(a,b,c){}oF.publicName="addDocumentEventListener";function pF(a,b,c,d){}pF.publicName="addElementEventListener";function qF(a){return a.K.sb()};function rF(a){}rF.publicName="addEventCallback";
function GF(a){}GF.M="internal.addFormAbandonmentListener";function HF(a,b,c,d){}
HF.M="internal.addFormData";var IF={},JF=[],KF={},LF=0,MF=0;
function TF(a,b){}TF.M="internal.addFormInteractionListener";
function $F(a,b){}$F.M="internal.addFormSubmitListener";
function eG(a){}eG.M="internal.addGaSendListener";function fG(a){if(!a)return{};var b=a.hp;return TB(b.type,b.index,b.name)}function gG(a){return a?{originatingEntity:fG(a)}:{}};
var iG=function(a,b,c){hG().updateZone(a,b,c)},kG=function(a,b,c,d,e,f){var g=hG();c=c&&Db(c,jG);for(var h=g.createZone(a,c),m=0;m<b.length;m++){var n=String(b[m]);if(g.registerChild(n,jg.ctid,h)){var p=n,q=a,r=d,t=e,u=f;if(Eb(p,"GTM-"))LB(p,void 0,!1,{source:1,fromContainerExecution:!0});else{var v=Lw("js",yb());LB(p,void 0,!0,{source:1,fromContainerExecution:!0});var w={originatingEntity:t,inheritParentConfig:u};Qw(v,q,w);Qw(Mw(p,r),q,w)}}}return h},hG=function(){return wp("zones",function(){return new lG})},
mG={zone:1,cn:1,css:1,ew:1,eq:1,ge:1,gt:1,lc:1,le:1,lt:1,re:1,sw:1,um:1},jG={cl:["ecl"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"]},lG=function(){this.C={};this.H={};this.N=0};k=lG.prototype;k.isActive=function(a,b){for(var c,d=0;d<a.length&&!(c=this.C[a[d]]);d++);if(!c)return!0;if(!this.isActive([c.vj],b))return!1;for(var e=0;e<c.og.length;e++)if(this.H[c.og[e]].ye(b))return!0;return!1};k.getIsAllowedFn=function(a,b){if(!this.isActive(a,b))return function(){return!1};for(var c,d=0;d<a.length&&
!(c=this.C[a[d]]);d++);if(!c)return function(){return!0};for(var e=[],f=0;f<c.og.length;f++){var g=this.H[c.og[f]];g.ye(b)&&e.push(g)}if(!e.length)return function(){return!1};var h=this.getIsAllowedFn([c.vj],b);return function(m,n){n=n||[];if(!h(m,n))return!1;for(var p=0;p<e.length;++p)if(e[p].N(m,n))return!0;return!1}};k.unregisterChild=function(a){for(var b=0;b<a.length;b++)delete this.C[a[b]]};k.createZone=function(a,b){var c=String(++this.N);this.H[c]=new nG(a,b);return c};k.updateZone=function(a,
b,c){var d=this.H[a];d&&d.P(b,c)};k.registerChild=function(a,b,c){var d=this.C[a];if(!d&&vp[a]||!d&&Jm(a)||d&&d.vj!==b)return!1;if(d)return d.og.push(c),!1;this.C[a]={vj:b,og:[c]};return!0};var nG=function(a,b){this.H=null;this.C=[{eventId:a,ye:!0}];if(b){this.H={};for(var c=0;c<b.length;c++)this.H[b[c]]=!0}};nG.prototype.P=function(a,b){var c=this.C[this.C.length-1];a<=c.eventId||c.ye!==b&&this.C.push({eventId:a,ye:b})};nG.prototype.ye=function(a){for(var b=this.C.length-1;b>=0;b--)if(this.C[b].eventId<=
a)return this.C[b].ye;return!1};nG.prototype.N=function(a,b){b=b||[];if(!this.H||mG[a]||this.H[a])return!0;for(var c=0;c<b.length;++c)if(this.H[b[c]])return!0;return!1};function oG(a){var b=vp.zones;return b?b.getIsAllowedFn(Cm(),a):function(){return!0}}function pG(){var a=vp.zones;a&&a.unregisterChild(Cm())}
function qG(){FC(Bm(),function(a){var b=vp.zones;return b?b.isActive(Cm(),a.originalEventData["gtm.uniqueEventId"]):!0});DC(Bm(),function(a){var b,c;b=a.entityId;c=a.securityGroups;return oG(Number(a.originalEventData["gtm.uniqueEventId"]))(b,c)})};var rG=function(a,b){this.tagId=a;this.ve=b};
function sG(a,b){var c=this;return a}sG.M="internal.loadGoogleTag";function tG(a){return new sd("",function(b){var c=this.evaluate(b);if(c instanceof sd)return new sd("",function(){var d=za.apply(0,arguments),e=this,f=ld(qF(this),null);f.eventId=a.eventId;f.priorityId=a.priorityId;f.originalEventData=a.originalEventData;var g=d.map(function(m){return e.evaluate(m)}),h=this.K.rb();h.Ld(f);return c.Lb.apply(c,[h].concat(va(g)))})})};function uG(a,b,c){var d=this;}uG.M="internal.addGoogleTagRestriction";var vG={},wG=[];
function DG(a,b){}
DG.M="internal.addHistoryChangeListener";function EG(a,b,c){}EG.publicName="addWindowEventListener";function FG(a,b){return!0}FG.publicName="aliasInWindow";function GG(a,b,c){}GG.M="internal.appendRemoteConfigParameter";function HG(a){var b;return b}
HG.publicName="callInWindow";function IG(a){}IG.publicName="callLater";function JG(a){}JG.M="callOnDomReady";function KG(a){}KG.M="callOnWindowLoad";function LG(a,b){var c;return c}LG.M="internal.computeGtmParameter";function MG(a,b){var c=this;}MG.M="internal.consentScheduleFirstTry";function NG(a,b){var c=this;}NG.M="internal.consentScheduleRetry";function OG(a){var b;return b}OG.M="internal.copyFromCrossContainerData";function PG(a,b){var c;var d=Bd(c,this.K,Ah(qF(this).Jb())?2:1);d===void 0&&c!==void 0&&M(45);return d}PG.publicName="copyFromDataLayer";
function QG(a){var b=void 0;return b}QG.M="internal.copyFromDataLayerCache";function RG(a){var b;return b}RG.publicName="copyFromWindow";function SG(a){var b=void 0;return Bd(b,this.K,1)}SG.M="internal.copyKeyFromWindow";var TG=function(a){return a===Qm.X.Da&&hn[a]===Pm.Ia.oe&&!Q(K.m.U)};var UG=function(){return"0"},VG=function(a){if(typeof a!=="string")return"";var b=["gclid","dclid","wbraid","_gl"];F(102)&&b.push("gbraid");return Vk(a,b,"0")};var WG={},XG={},YG={},ZG={},$G={},aH={},bH={},cH={},dH={},eH={},fH={},gH={},hH={},iH={},jH={},kH={},lH={},mH={},nH={},oH={},pH={},qH={},rH={},sH={},tH={},uH={},vH=(uH[K.m.Qa]=(WG[2]=[TG],WG),uH[K.m.qf]=(XG[2]=[TG],XG),uH[K.m.df]=(YG[2]=[TG],YG),uH[K.m.li]=(ZG[2]=[TG],ZG),uH[K.m.mi]=($G[2]=[TG],$G),uH[K.m.ni]=(aH[2]=[TG],aH),uH[K.m.oi]=(bH[2]=[TG],bH),uH[K.m.ri]=(cH[2]=[TG],cH),uH[K.m.wc]=(dH[2]=[TG],dH),uH[K.m.tf]=(eH[2]=[TG],eH),uH[K.m.uf]=(fH[2]=[TG],fH),uH[K.m.vf]=(gH[2]=[TG],gH),uH[K.m.wf]=(hH[2]=
[TG],hH),uH[K.m.xf]=(iH[2]=[TG],iH),uH[K.m.yf]=(jH[2]=[TG],jH),uH[K.m.zf]=(kH[2]=[TG],kH),uH[K.m.Af]=(lH[2]=[TG],lH),uH[K.m.lb]=(mH[1]=[TG],mH),uH[K.m.Zc]=(nH[1]=[TG],nH),uH[K.m.fd]=(oH[1]=[TG],oH),uH[K.m.Xd]=(pH[1]=[TG],pH),uH[K.m.Oe]=(qH[1]=[function(a){return F(102)&&TG(a)}],qH),uH[K.m.gd]=(rH[1]=[TG],rH),uH[K.m.Aa]=(sH[1]=[TG],sH),uH[K.m.Wa]=(tH[1]=[TG],tH),uH),wH={},xH=(wH[K.m.lb]=UG,wH[K.m.Zc]=UG,wH[K.m.fd]=UG,wH[K.m.Xd]=UG,wH[K.m.Oe]=UG,wH[K.m.gd]=function(a){if(!kd(a))return{};var b=ld(a,
null);delete b.match_id;return b},wH[K.m.Aa]=VG,wH[K.m.Wa]=VG,wH),yH={},zH={},AH=(zH[R.A.ib]=(yH[2]=[TG],yH),zH),BH={};var CH=function(a,b,c,d){this.C=a;this.N=b;this.P=c;this.R=d};CH.prototype.getValue=function(a){a=a===void 0?Qm.X.Fb:a;if(!this.N.some(function(b){return b(a)}))return this.P.some(function(b){return b(a)})?this.R(this.C):this.C};CH.prototype.H=function(){return id(this.C)==="array"||kd(this.C)?ld(this.C,null):this.C};
var DH=function(){},EH=function(a,b){this.conditions=a;this.C=b},FH=function(a,b,c){var d,e=((d=a.conditions[b])==null?void 0:d[2])||[],f,g=((f=a.conditions[b])==null?void 0:f[1])||[];return new CH(c,e,g,a.C[b]||DH)},GH,HH;var IH=function(a,b,c){this.eventName=b;this.D=c;this.C={};this.isAborted=!1;this.target=a;this.metadata={};for(var d=c.eventMetadata||{},e=l(Object.keys(d)),f=e.next();!f.done;f=e.next()){var g=f.value;U(this,g,d[g])}},Pv=function(a,b){var c,d;return(c=a.C[b])==null?void 0:(d=c.getValue)==null?void 0:d.call(c,S(a,R.A.Pf))},V=function(a,b,c){var d=a.C,e;c===void 0?e=void 0:(GH!=null||(GH=new EH(vH,xH)),e=FH(GH,b,c));d[b]=e};
IH.prototype.mergeHitDataForKey=function(a,b){var c,d,e;c=(d=this.C[a])==null?void 0:(e=d.H)==null?void 0:e.call(d);if(!c)return V(this,a,b),!0;if(!kd(c))return!1;V(this,a,Object.assign(c,b));return!0};var JH=function(a,b){b=b===void 0?{}:b;for(var c=l(Object.keys(a.C)),d=c.next();!d.done;d=c.next()){var e=d.value,f=void 0,g=void 0,h=void 0;b[e]=(f=a.C[e])==null?void 0:(h=(g=f).H)==null?void 0:h.call(g)}return b};
IH.prototype.copyToHitData=function(a,b,c){var d=O(this.D,a);d===void 0&&(d=b);if(d!==void 0&&c!==void 0&&kb(d)&&F(92))try{d=c(d)}catch(e){}d!==void 0&&V(this,a,d)};
var S=function(a,b){var c=a.metadata[b];if(b===R.A.Pf){var d;return c==null?void 0:(d=c.H)==null?void 0:d.call(c)}var e;return c==null?void 0:(e=c.getValue)==null?void 0:e.call(c,S(a,R.A.Pf))},U=function(a,b,c){var d=a.metadata,e;c===void 0?e=void 0:(HH!=null||(HH=new EH(AH,BH)),e=FH(HH,b,c));d[b]=e},KH=function(a,b){b=b===void 0?{}:b;for(var c=l(Object.keys(a.metadata)),d=c.next();!d.done;d=c.next()){var e=d.value,f=void 0,g=void 0,h=void 0;b[e]=(f=a.metadata[e])==null?void 0:(h=(g=f).H)==null?void 0:
h.call(g)}return b},iw=function(a,b,c){var d=Xw(a.target.destinationId);return d&&d[b]!==void 0?d[b]:c};function LH(a,b){var c;return c}LH.M="internal.copyPreHit";function MH(a,b){var c=null;return Bd(c,this.K,2)}MH.publicName="createArgumentsQueue";function NH(a){return Bd(function(c){var d=cC();if(typeof c==="function")d(function(){c(function(f,g,h){var m=
cC(),n=m&&m.getByName&&m.getByName(f);return(new x.gaplugins.Linker(n)).decorate(g,h)})});else if(Array.isArray(c)){var e=String(c[0]).split(".");b[e.length===1?e[0]:e[1]]&&d.apply(null,c)}else if(c==="isLoaded")return!!d.loaded},this.K,1)}NH.M="internal.createGaCommandQueue";function OH(a){return Bd(function(){if(!jb(e.push))throw Error("Object at "+a+" in window is not an array.");e.push.apply(e,Array.prototype.slice.call(arguments,0))},this.K,
Ah(qF(this).Jb())?2:1)}OH.publicName="createQueue";function PH(a,b){var c=null;return c}PH.M="internal.createRegex";function QH(a){}QH.M="internal.declareConsentState";function RH(a){var b="";return b}RH.M="internal.decodeUrlHtmlEntities";function SH(a,b,c){var d;return d}SH.M="internal.decorateUrlWithGaCookies";function TH(){}TH.M="internal.deferCustomEvents";function UH(a){var b;J(this,"detect_user_provided_data","auto");var c=Ad(a)||{},d=wx({ze:!!c.includeSelector,Ae:!!c.includeVisibility,Uf:c.excludeElementSelectors,Yb:c.fieldFilters,Eh:!!c.selectMultipleElements});b=new Ua;var e=new od;b.set("elements",e);for(var f=d.elements,g=0;g<f.length;g++)e.push(VH(f[g]));d.xj!==void 0&&b.set("preferredEmailElement",VH(d.xj));b.set("status",d.status);if(F(129)&&c.performDataLayerSearch&&!/Mobile|iPhone|iPad|iPod|Android|IEMobile/.test(tc&&
tc.userAgent||"")){}return b}
var WH=function(a){switch(a){case ux.hc:return"email";case ux.xd:return"phone_number";case ux.pd:return"first_name";case ux.wd:return"last_name";case ux.Li:return"street";case ux.Hh:return"city";case ux.Gi:return"region";case ux.Mf:return"postal_code";case ux.Ie:return"country"}},VH=function(a){var b=new Ua;b.set("userData",a.la);b.set("tagName",a.tagName);a.querySelector!==void 0&&b.set("querySelector",a.querySelector);a.isVisible!==void 0&&b.set("isVisible",a.isVisible);if(F(33)){}else switch(a.type){case ux.hc:b.set("type","email")}return b};UH.M="internal.detectUserProvidedData";
function ZH(a,b){return f}ZH.M="internal.enableAutoEventOnClick";
function gI(a,b){return p}gI.M="internal.enableAutoEventOnElementVisibility";function hI(){}hI.M="internal.enableAutoEventOnError";var iI={},jI=[],kI={},lI=0,mI=0;
function sI(a,b){var c=this;return d}sI.M="internal.enableAutoEventOnFormInteraction";
function xI(a,b){var c=this;return f}xI.M="internal.enableAutoEventOnFormSubmit";
function CI(){var a=this;}CI.M="internal.enableAutoEventOnGaSend";var DI={},EI=[];
function LI(a,b){var c=this;return f}LI.M="internal.enableAutoEventOnHistoryChange";var MI=["http://","https://","javascript:","file://"];
function QI(a,b){var c=this;return h}QI.M="internal.enableAutoEventOnLinkClick";var RI,SI;
function cJ(a,b){var c=this;return d}cJ.M="internal.enableAutoEventOnScroll";function dJ(a){return function(){if(a.limit&&a.sj>=a.limit)a.yh&&x.clearInterval(a.yh);else{a.sj++;var b=zb();LD({event:a.eventName,"gtm.timerId":a.yh,"gtm.timerEventNumber":a.sj,"gtm.timerInterval":a.interval,"gtm.timerLimit":a.limit,"gtm.timerStartTime":a.Fm,"gtm.timerCurrentTime":b,"gtm.timerElapsedTime":b-a.Fm,"gtm.triggers":a.Hq})}}}
function eJ(a,b){
return f}eJ.M="internal.enableAutoEventOnTimer";var nc=xa(["data-gtm-yt-inspected-"]),gJ=["www.youtube.com","www.youtube-nocookie.com"],hJ,iJ=!1;
function sJ(a,b){var c=this;return e}sJ.M="internal.enableAutoEventOnYouTubeActivity";iJ=!1;function tJ(a,b){if(!lh(a)||!fh(b))throw H(this.getName(),["string","Object|undefined"],arguments);var c=b?Ad(b):{},d=a,e=!1;return e}tJ.M="internal.evaluateBooleanExpression";var uJ;function vJ(a){var b=!1;return b}vJ.M="internal.evaluateMatchingRules";function eK(){return qr(7)&&qr(9)&&qr(10)};
var iK=function(a,b){if(!b.isGtmEvent){var c=O(b,K.m.qc),d=O(b,K.m.Ic),e=O(b,c);if(e===void 0){var f=void 0;fK.hasOwnProperty(c)?f=fK[c]:gK.hasOwnProperty(c)&&(f=gK[c]);f===1&&(f=hK(c));kb(f)?cC()(function(){var g,h,m,n=(m=(g=cC())==null?void 0:(h=g.getByName)==null?void 0:h.call(g,a))==null?void 0:m.get(f);d(n)}):d(void 0)}else d(e)}},jK=function(a,b){var c=a[K.m.Mc],d=b+".",e=a[K.m.ma]||"",f=c===void 0?!!a.use_anchor:c==="fragment",g=!!a[K.m.sc];e=String(e).replace(/\s+/g,"").split(",");var h=cC();
h(d+"require","linker");h(d+"linker:autoLink",e,f,g)},nK=function(a,b,c){if(!c.isGtmEvent||!kK[a]){var d=!Q(K.m.ja),e=function(f){var g="gtm"+String(zp()),h,m=cC(),n=lK(b,"",c),p,q=n.createOnlyFields._useUp;if(c.isGtmEvent||mK(b,n.createOnlyFields)){c.isGtmEvent&&(h=n.createOnlyFields,n.gtmTrackerName&&(h.name=g));m(function(){var t,u=m==null?void 0:(t=m.getByName)==null?void 0:t.call(m,b);u&&(p=u.get("clientId"));if(!c.isGtmEvent){var v;m==null||(v=m.remove)==null||v.call(m,b)}});m("create",a,c.isGtmEvent?
h:n.createOnlyFields);d&&Q(K.m.ja)&&(d=!1,m(function(){var t,u,v=(t=cC())==null?void 0:(u=t.getByName)==null?void 0:u.call(t,c.isGtmEvent?g:b);!v||v.get("clientId")==p&&q||(c.isGtmEvent?(n.fieldsToSet["&gcu"]="1",n.fieldsToSet["&sst.gcut"]=Co[f]):(n.fieldsToSend["&gcu"]="1",n.fieldsToSend["&sst.gcut"]=Co[f]),v.set(n.fieldsToSet),
c.isGtmEvent?v.send("pageview"):v.send("pageview",n.fieldsToSend))}));c.isGtmEvent&&m(function(){var t;m==null||(t=m.remove)==null||t.call(m,g)})}};np(function(){return void e(K.m.ja)},K.m.ja);np(function(){return void e(K.m.U)},K.m.U);np(function(){return void e(K.m.V)},K.m.V);c.isGtmEvent&&(kK[a]=!0)}},oK=function(a,b){al()&&b&&(a[K.m.oc]=b)},xK=function(a,b,c){function d(){var da=za.apply(0,arguments);da[0]=w?w+"."+da[0]:""+da[0];u.apply(window,da)}function e(da){function ka(Va,Ya){for(var sb=
0;Ya&&sb<Ya.length;sb++)d(Va,Ya[sb])}var X=c.isGtmEvent,W=X?pK(y):qK(b,c);if(W){var ta={};oK(ta,da);d("require","ec","ec.js",ta);X&&W.Ti&&d("set","&cu",W.Ti);var ra=W.action;if(X||ra==="impressions")if(ka("ec:addImpression",W.bm),!X)return;if(ra==="promo_click"||ra==="promo_view"||X&&W.kg){var na=W.kg;ka("ec:addPromo",na);if(na&&na.length>0&&ra==="promo_click"){X?d("ec:setAction",ra,W.Xb):d("ec:setAction",ra);return}if(!X)return}ra!=="promo_view"&&ra!=="impressions"&&(ka("ec:addProduct",W.Jd),d("ec:setAction",
ra,W.Xb))}}function f(da){if(da){var ka={};if(kd(da))for(var X in rK)rK.hasOwnProperty(X)&&sK(rK[X],X,da[X],ka);oK(ka,E);d("require","linkid",ka)}}function g(){if(Ir()){}else{var da=O(c,K.m.Ln);da&&(d("require",da,{dataLayer:Wj}),d("require","render"))}}function h(){var da=O(c,K.m.Ze);u(function(){if(!c.isGtmEvent&&kd(da)){var ka=y.fieldsToSend,X,W,ta=(X=v())==null?void 0:(W=X.getByName)==null?void 0:W.call(X,w),ra;for(ra in da)if(da[ra]!=
null&&/^(dimension|metric)\d+$/.test(ra)){var na=void 0,Va=(na=ta)==null?void 0:na.get(hK(da[ra]));tK(ka,ra,Va)}}})}function m(da,ka,X){X&&(ka=String(ka));y.fieldsToSend[da]=ka}function n(){if(y.displayfeatures){var da="_dc_gtm_"+p.replace(/[^A-Za-z0-9-]/g,"");d("require","displayfeatures",void 0,{cookieName:da})}}var p=a,q=Gp(a),r=c.eventMetadata[R.A.Of];if(!(q&&r&&r.indexOf(q.destinationId)<0)){jl&&(Vn=!0,b===K.m.qa?ao(c,a):(c.eventMetadata[R.A.Je]||(Yn[a]=!0),Cp(c.eventMetadata[R.A.hb])));var t,
u=c.isGtmEvent?fC(O(c,"gaFunctionName")):fC();if(jb(u)){var v=cC,w;w=c.isGtmEvent?O(c,"name")||O(c,"gtmTrackerName"):"gtag_"+p.split("-").join("_");var y=lK(w,b,c);!c.isGtmEvent&&mK(w,y.createOnlyFields)&&(u(function(){var da,ka;v()&&((da=v())==null||(ka=da.remove)==null||ka.call(da,w))}),uK[w]=!1);u("create",p,y.createOnlyFields);var A=c.isGtmEvent&&y.fieldsToSet[K.m.oc];if(!c.isGtmEvent&&y.createOnlyFields[K.m.oc]||A){var C=$k(c.isGtmEvent?y.fieldsToSet[K.m.oc]:y.createOnlyFields[K.m.oc],"/analytics.js");
C&&(t=C)}var E=c.isGtmEvent?y.fieldsToSet[K.m.oc]:y.createOnlyFields[K.m.oc];if(E){var G=c.isGtmEvent?y.fieldsToSet[K.m.Lg]:y.createOnlyFields[K.m.Lg];G&&!uK[w]&&(uK[w]=!0,u(hC(w,G)))}c.isGtmEvent?y.enableRecaptcha&&d("require","recaptcha","recaptcha.js"):(h(),f(y.linkAttribution));var I=y[K.m.Pa];I&&I[K.m.ma]&&jK(I,w);d("set",y.fieldsToSet);if(c.isGtmEvent){if(y.enableLinkId){var N={};oK(N,E);d("require","linkid","linkid.js",N)}nK(p,w,c)}if(b===K.m.Yc)if(c.isGtmEvent){n();if(y.remarketingLists){var T=
"_dc_gtm_"+p.replace(/[^A-Za-z0-9-]/g,"");d("require","adfeatures",{cookieName:T})}e(E);d("send","pageview");y.createOnlyFields._useUp&&eC(w+".")}else g(),d("send","pageview",y.fieldsToSend);else b===K.m.qa?(g(),yw(p,c),O(c,K.m.Eb)&&(Qu(["aw","dc"]),eC(w+".")),Su(["aw","dc"]),y.sendPageView!=0&&d("send","pageview",y.fieldsToSend),nK(p,w,c)):b===K.m.Cb?iK(w,c):b==="screen_view"?d("send","screenview",y.fieldsToSend):b==="timing_complete"?(y.fieldsToSend.hitType="timing",m("timingCategory",y.eventCategory,
!0),c.isGtmEvent?m("timingVar",y.timingVar,!0):m("timingVar",y.name,!0),m("timingValue",ub(y.value)),y.eventLabel!==void 0&&m("timingLabel",y.eventLabel,!0),d("send",y.fieldsToSend)):b==="exception"?d("send","exception",y.fieldsToSend):b===""&&c.isGtmEvent||(b==="track_social"&&c.isGtmEvent?(y.fieldsToSend.hitType="social",m("socialNetwork",y.socialNetwork,!0),m("socialAction",y.socialAction,!0),m("socialTarget",y.socialTarget,!0)):((c.isGtmEvent||vK[b])&&e(E),c.isGtmEvent&&n(),y.fieldsToSend.hitType=
"event",m("eventCategory",y.eventCategory,!0),m("eventAction",y.eventAction||b,!0),y.eventLabel!==void 0&&m("eventLabel",y.eventLabel,!0),y.value!==void 0&&m("eventValue",ub(y.value))),d("send",y.fieldsToSend));var ca=t&&!c.eventMetadata[R.A.Gl];if(!wK&&(!c.isGtmEvent||ca)){t=t||"https://www.google-analytics.com/analytics.js";wK=!0;var P=function(){c.onFailure()},ha=function(){var da;((da=v())==null?0:da.loaded)||P()};Ir()?B(ha):Fc(t,ha,P)}}else B(c.onFailure)}},yK=function(a,b,c,d){op(function(){xK(a,
b,d)},[K.m.ja,K.m.U])},mK=function(a,b){var c=zK[a];zK[a]=ld(b,null);if(!c)return!1;for(var d in b)if(b.hasOwnProperty(d)&&b[d]!==c[d])return!0;for(var e in c)if(c.hasOwnProperty(e)&&c[e]!==b[e])return!0;return!1},qK=function(a,b){function c(u){return{id:d(K.m.Xa),affiliation:d(K.m.lk),revenue:d(K.m.Fa),tax:d(K.m.Xh),shipping:d(K.m.bf),coupon:d(K.m.mk),list:d(K.m.Wh)||d(K.m.af)||u}}for(var d=function(u){return O(b,u)},e=d(K.m.sa),f,g=0;e&&g<e.length&&!(f=e[g][K.m.Wh]||e[g][K.m.af]);g++);var h=d(K.m.Ze);
if(kd(h))for(var m=0;e&&m<e.length;++m){var n=e[m],p;for(p in h)h.hasOwnProperty(p)&&/^(dimension|metric)\d+$/.test(p)&&h[p]!=null&&tK(n,p,n[h[p]])}var q=null,r=d(K.m.Cn);if(a===K.m.kb||a===K.m.Sd)q={action:a,Xb:c(),Jd:AK(e)};else if(a===K.m.Pd)q={action:"add",Xb:c(),Jd:AK(e)};else if(a===K.m.Qd)q={action:"remove",Xb:c(),Jd:AK(e)};else if(a===K.m.xb)q={action:"detail",Xb:c(f),Jd:AK(e)};else if(a===K.m.jc)q={action:"impressions",bm:AK(e)};else if(a===K.m.kc)q={action:"promo_view",kg:AK(r)||AK(e)};
else if(a==="select_content"&&r&&r.length>0||a===K.m.Gc)q={action:"promo_click",kg:AK(r)||AK(e)};else if(a==="select_content"||a===K.m.Rd)q={action:"click",Xb:{list:d(K.m.Wh)||d(K.m.af)||f},Jd:AK(e)};else if(a===K.m.Xc||a==="checkout_progress"){var t={step:a===K.m.Xc?1:d(K.m.Vh),option:d(K.m.Fg)};q={action:"checkout",Jd:AK(e),Xb:ld(c(),t)}}else a==="set_checkout_option"&&(q={action:"checkout_option",Xb:{step:d(K.m.Vh),option:d(K.m.Fg)}});q&&(q.Ti=d(K.m.Va));return q},pK=function(a){var b=a.gtmEcommerceData;
if(!b)return null;var c={};b.currencyCode&&(c.Ti=b.currencyCode);if(b.impressions){c.action="impressions";var d=b.impressions;c.bm=b.translateIfKeyEquals==="impressions"?AK(d):d}if(b.promoView){c.action="promo_view";var e=b.promoView.promotions;c.kg=b.translateIfKeyEquals==="promoView"?AK(e):e}if(b.promoClick){var f=b.promoClick;c.action="promo_click";var g=f.promotions;c.kg=b.translateIfKeyEquals==="promoClick"?AK(g):g;c.Xb=f.actionField;return c}for(var h in b)if(b[h]!==void 0&&h!=="translateIfKeyEquals"&&
h!=="impressions"&&h!=="promoView"&&h!=="promoClick"&&h!=="currencyCode"){c.action=h;var m=b[h].products;c.Jd=b.translateIfKeyEquals==="products"?AK(m):m;c.Xb=b[h].actionField;break}return Object.keys(c).length?c:null},AK=function(a){function b(e){function f(h,m){for(var n=0;n<m.length;n++){var p=m[n];if(e[p]){g[h]=e[p];break}}}var g=ld(e,null);f("id",["id","item_id","promotion_id"]);f("name",["name","item_name","promotion_name"]);f("brand",["brand","item_brand"]);f("variant",["variant","item_variant"]);
f("list",["list_name","item_list_name"]);f("position",["list_position","creative_slot","index"]);(function(){if(e.category)g.category=e.category;else{for(var h="",m=0;m<BK.length;m++)e[BK[m]]!==void 0&&(h&&(h+="/"),h+=e[BK[m]]);h&&(g.category=h)}})();f("listPosition",["list_position"]);f("creative",["creative_name"]);f("list",["list_name"]);f("position",["list_position","creative_slot"]);return g}for(var c=[],d=0;a&&d<a.length;d++)a[d]&&kd(a[d])&&c.push(b(a[d]));return c.length?c:void 0},lK=function(a,
b,c){var d=function(P){return O(c,P)},e={},f={},g={},h={},m=CK(d(K.m.In));!c.isGtmEvent&&m&&tK(f,"exp",m);g["&gtm"]=Nr({Ma:c.eventMetadata[R.A.hb],oh:!0});c.isGtmEvent||(g._no_slc=!0);bn()&&(h._cs=DK);var n=d(K.m.Ze);if(!c.isGtmEvent&&kd(n))for(var p in n)if(n.hasOwnProperty(p)&&/^(dimension|metric)\d+$/.test(p)&&n[p]!=null){var q=d(String(n[p]));q!==void 0&&tK(f,p,q)}for(var r=!c.isGtmEvent,t=Xp(c),u=0;u<t.length;++u){var v=t[u];if(c.isGtmEvent){var w=d(v);EK.hasOwnProperty(v)?e[v]=w:FK.hasOwnProperty(v)?
h[v]=w:g[v]=w}else{var y=void 0;v!==K.m.oa?y=d(v):y=c.getMergedValues(v);if(GK.hasOwnProperty(v))sK(GK[v],v,y,e);else if(HK.hasOwnProperty(v))sK(HK[v],v,y,g);else if(gK.hasOwnProperty(v))sK(gK[v],v,y,f);else if(fK.hasOwnProperty(v))sK(fK[v],v,y,h);else if(/^(dimension|metric|content_group)\d+$/.test(v))sK(1,v,y,f);else if(v===K.m.oa){if(!IK){var A=Jb(y);A&&(f["&did"]=A)}var C=void 0,E=void 0;b===K.m.qa?C=Jb(c.getMergedValues(v),"."):(C=Jb(c.getMergedValues(v,1),"."),E=Jb(c.getMergedValues(v,2),"."));
C&&(f["&gdid"]=C);E&&(f["&edid"]=E)}else v===K.m.cb&&t.indexOf(K.m.ed)<0&&(h.cookieName=String(y)+"_ga");F(153)&&JK[v]&&(c.N.hasOwnProperty(v)||b===K.m.qa&&c.C.hasOwnProperty(v))&&(r=!1)}}F(153)&&r&&(f["&jsscut"]="1");d(K.m.yg)!==!1&&d(K.m.Ob)!==!1&&eK()||(g.allowAdFeatures=!1);g.allowAdPersonalizationSignals=wr(c);!c.isGtmEvent&&d(K.m.Eb)&&(h._useUp=!0);if(c.isGtmEvent){h.name=h.name||e.gtmTrackerName;var G=g.hitCallback;g.hitCallback=function(){jb(G)&&G();c.onSuccess()}}else{tK(h,"cookieDomain",
"auto");tK(g,"forceSSL",!0);tK(e,"eventCategory",KK(b));LK[b]&&tK(f,"nonInteraction",!0);b==="login"||b==="sign_up"||b==="share"?tK(e,"eventLabel",d(K.m.Gk)):b==="search"||b==="view_search_results"?tK(e,"eventLabel",d(K.m.Sn)):b==="select_content"&&tK(e,"eventLabel",d(K.m.zn));var I=e[K.m.Pa]||{},N=I[K.m.de];N||N!=0&&I[K.m.ma]?h.allowLinker=!0:N===!1&&tK(h,"useAmpClientId",!1);f.hitCallback=c.onSuccess;h.name=a}xr()&&(g["&gcs"]=yr());g["&gcd"]=Cr(c);bn()&&(Q(K.m.ja)||(h.storage="none"),Q([K.m.U,K.m.V])||
(g.allowAdFeatures=!1,h.storeGac=!1));Fr()&&(g["&dma_cps"]=Dr());g["&dma"]=Er();ar(ir())&&(g["&tcfd"]=Gr());mk()&&(g["&tag_exp"]=mk());var T=bl(c)||d(K.m.oc),ca=d(K.m.Lg);T&&(c.isGtmEvent||(h[K.m.oc]=T),h._cd2l=!0);ca&&!c.isGtmEvent&&(h[K.m.Lg]=ca);e.fieldsToSend=f;e.fieldsToSet=g;e.createOnlyFields=h;return e},DK=function(a){return Q(a)},CK=function(a){if(Array.isArray(a)){for(var b=[],c=0;c<a.length;c++){var d=a[c];if(d!=null){var e=d.id,f=d.variant;e!=null&&f!=null&&b.push(String(e)+"."+String(f))}}return b.length>
0?b.join("!"):void 0}},tK=function(a,b,c){a.hasOwnProperty(b)||(a[b]=c)},KK=function(a){var b="general";MK[a]?b="ecommerce":NK[a]?b="engagement":a==="exception"&&(b="error");return b},hK=function(a){return a&&kb(a)?a.replace(/(_[a-z])/g,function(b){return b[1].toUpperCase()}):a},sK=function(a,b,c,d){if(c!==void 0)if(OK[b]&&(c=vb(c)),b!=="anonymize_ip"||c||(c=void 0),a===1)d[hK(b)]=c;else if(kb(a))d[a]=c;else for(var e in a)a.hasOwnProperty(e)&&c[e]!==void 0&&(d[a[e]]=c[e])},IK=!1;var wK=!1,uK={},kK={},PK={},JK=(PK[K.m.Ea]=1,PK[K.m.Ob]=1,PK[K.m.nb]=1,PK[K.m.ob]=1,PK[K.m.yb]=1,PK[K.m.ed]=1,PK[K.m.Sb]=1,PK[K.m.cb]=1,PK[K.m.Hc]=1,PK[K.m.Ik]=1,PK[K.m.Aa]=1,PK[K.m.lf]=1,PK[K.m.Wa]=1,PK[K.m.Db]=1,PK),QK={},fK=(QK.client_storage="storage",QK.sample_rate=1,QK.site_speed_sample_rate=1,QK.store_gac=1,QK.use_amp_client_id=1,QK[K.m.Qb]=1,QK[K.m.Oa]="storeGac",QK[K.m.nb]=1,QK[K.m.ob]=1,QK[K.m.yb]=1,QK[K.m.ed]=1,QK[K.m.Sb]=1,QK[K.m.Hc]=
1,QK),RK={},FK=(RK._cs=1,RK._useUp=1,RK.allowAnchor=1,RK.allowLinker=1,RK.alwaysSendReferrer=1,RK.clientId=1,RK.cookieDomain=1,RK.cookieExpires=1,RK.cookieFlags=1,RK.cookieName=1,RK.cookiePath=1,RK.cookieUpdate=1,RK.legacyCookieDomain=1,RK.legacyHistoryImport=1,RK.name=1,RK.sampleRate=1,RK.siteSpeedSampleRate=1,RK.storage=1,RK.storeGac=1,RK.useAmpClientId=1,RK._cd2l=1,RK),HK={anonymize_ip:1},SK={},gK=(SK.campaign={content:"campaignContent",id:"campaignId",medium:"campaignMedium",name:"campaignName",
source:"campaignSource",term:"campaignKeyword"},SK.app_id=1,SK.app_installer_id=1,SK.app_name=1,SK.app_version=1,SK.description="exDescription",SK.fatal="exFatal",SK.language=1,SK.page_hostname="hostname",SK.transport_type="transport",SK[K.m.Va]="currencyCode",SK[K.m.Qg]=1,SK[K.m.Aa]="location",SK[K.m.lf]="page",SK[K.m.Wa]="referrer",SK[K.m.Db]="title",SK[K.m.gi]=1,SK[K.m.Qa]=1,SK),TK={},GK=(TK.content_id=1,TK.event_action=1,TK.event_category=1,TK.event_label=1,TK.link_attribution=1,TK.name=1,TK[K.m.Pa]=
1,TK[K.m.Gk]=1,TK[K.m.pb]=1,TK[K.m.Fa]=1,TK),EK={displayfeatures:1,enableLinkId:1,enableRecaptcha:1,eventAction:1,eventCategory:1,eventLabel:1,gaFunctionName:1,gtmEcommerceData:1,gtmTrackerName:1,linker:1,remarketingLists:1,socialAction:1,socialNetwork:1,socialTarget:1,timingVar:1,value:1},BK=["item_category","item_category2","item_category3","item_category4","item_category5"],UK={},rK=(UK.levels=1,UK[K.m.ob]="duration",UK[K.m.ed]=1,UK),VK={},OK=(VK.anonymize_ip=1,VK.fatal=1,VK.send_page_view=1,VK.store_gac=
1,VK.use_amp_client_id=1,VK[K.m.Oa]=1,VK[K.m.Qg]=1,VK),WK={},vK=(WK.checkout_progress=1,WK.select_content=1,WK.set_checkout_option=1,WK[K.m.Pd]=1,WK[K.m.Qd]=1,WK[K.m.Xc]=1,WK[K.m.Rd]=1,WK[K.m.jc]=1,WK[K.m.Gc]=1,WK[K.m.kc]=1,WK[K.m.kb]=1,WK[K.m.Sd]=1,WK[K.m.xb]=1,WK),XK={},MK=(XK.checkout_progress=1,XK.set_checkout_option=1,XK[K.m.Zj]=1,XK[K.m.bk]=1,XK[K.m.Pd]=1,XK[K.m.Qd]=1,XK[K.m.dk]=1,XK[K.m.Xc]=1,XK[K.m.kb]=1,XK[K.m.Sd]=1,XK[K.m.ek]=1,XK),YK={},NK=(YK.generate_lead=1,YK.login=1,YK.search=1,YK.select_content=
1,YK.share=1,YK.sign_up=1,YK.view_search_results=1,YK[K.m.Rd]=1,YK[K.m.jc]=1,YK[K.m.Gc]=1,YK[K.m.kc]=1,YK[K.m.xb]=1,YK),ZK={},LK=(ZK.view_search_results=1,ZK[K.m.jc]=1,ZK[K.m.kc]=1,ZK[K.m.xb]=1,ZK),zK={};function $K(a,b,c,d){}$K.M="internal.executeEventProcessor";function aL(a){var b;return Bd(b,this.K,1)}aL.M="internal.executeJavascriptString";function bL(a){var b;return b};function cL(a){var b="";return b}cL.M="internal.generateClientId";function dL(a){var b={};return Bd(b)}dL.M="internal.getAdsCookieWritingOptions";function eL(a,b){var c=!1;return c}eL.M="internal.getAllowAdPersonalization";function fL(){var a;return a}fL.M="internal.getAndResetEventUsage";function gL(a,b){b=b===void 0?!0:b;var c;return c}gL.M="internal.getAuid";var hL=null;
function iL(){var a=new Ua;return a}
iL.publicName="getContainerVersion";function jL(a,b){b=b===void 0?!0:b;var c;return c}jL.publicName="getCookieValues";function kL(){var a="";return a}kL.M="internal.getCorePlatformServicesParam";function lL(){return ko()}lL.M="internal.getCountryCode";function mL(){var a=[];a=Am();return Bd(a)}mL.M="internal.getDestinationIds";function nL(a){var b=new Ua;return b}nL.M="internal.getDeveloperIds";function oL(a){var b;return b}oL.M="internal.getEcsidCookieValue";function pL(a,b){var c=null;return c}pL.M="internal.getElementAttribute";function qL(a){var b=null;return b}qL.M="internal.getElementById";function rL(a){var b="";return b}rL.M="internal.getElementInnerText";function sL(a,b){var c=null;return Bd(c)}sL.M="internal.getElementProperty";function tL(a){var b;return b}tL.M="internal.getElementValue";function uL(a){var b=0;return b}uL.M="internal.getElementVisibilityRatio";function vL(a){var b=null;return b}vL.M="internal.getElementsByCssSelector";
function wL(a){var b;if(!lh(a))throw H(this.getName(),["string"],arguments);J(this,"read_event_data",a);var c;a:{var d=a,e=qF(this).originalEventData;if(e){for(var f=e,g={},h={},m={},n=[],p=d.split("\\\\"),q=0;q<p.length;q++){for(var r=p[q].split("\\."),t=0;t<r.length;t++){for(var u=r[t].split("."),v=0;v<u.length;v++)n.push(u[v]),v!==u.length-1&&n.push(m);t!==r.length-1&&n.push(h)}q!==p.length-1&&n.push(g)}for(var w=[],y="",A=l(n),C=A.next();!C.done;C=
A.next()){var E=C.value;E===m?(w.push(y),y=""):y=E===g?y+"\\":E===h?y+".":y+E}y&&w.push(y);for(var G=l(w),I=G.next();!I.done;I=G.next()){if(f==null){c=void 0;break a}f=f[I.value]}c=f}else c=void 0}b=Bd(c,this.K,1);return b}wL.M="internal.getEventData";var xL={};xL.enableDecodeUri=F(92);xL.enableGaAdsConversions=F(122);xL.enableGaAdsConversionsClientId=F(121);xL.enableOverrideAdsCps=F(170);xL.enableUrlDecodeEventUsage=F(139);function yL(){return Bd(xL)}yL.M="internal.getFlags";function zL(){var a;return a}zL.M="internal.getGsaExperimentId";function AL(){return new xd(AE)}AL.M="internal.getHtmlId";function BL(a){var b;return b}BL.M="internal.getIframingState";function CL(a,b){var c={};return Bd(c)}CL.M="internal.getLinkerValueFromLocation";function DL(){var a=new Ua;return a}DL.M="internal.getPrivacyStrings";function EL(a,b){var c;if(!lh(a)||!lh(b))throw H(this.getName(),["string","string"],arguments);var d=Xw(a)||{};c=Bd(d[b],this.K);return c}EL.M="internal.getProductSettingsParameter";function FL(a,b){var c;return c}FL.publicName="getQueryParameters";function GL(a,b){var c;return c}GL.publicName="getReferrerQueryParameters";function HL(a){var b="";return b}HL.publicName="getReferrerUrl";function IL(){return lo()}IL.M="internal.getRegionCode";function JL(a,b){var c;return c}JL.M="internal.getRemoteConfigParameter";function KL(){var a=new Ua;a.set("width",0);a.set("height",0);return a}KL.M="internal.getScreenDimensions";function LL(){var a="";return a}LL.M="internal.getTopSameDomainUrl";function ML(){var a="";return a}ML.M="internal.getTopWindowUrl";function NL(a){var b="";return b}NL.publicName="getUrl";function OL(){J(this,"get_user_agent");return tc.userAgent}OL.M="internal.getUserAgent";function PL(){var a;return a?Bd(Sy(a)):a}PL.M="internal.getUserAgentClientHints";function XL(){var a=x;return a.gaGlobal=a.gaGlobal||{}}function YL(){var a=XL();a.hid=a.hid||ob();return a.hid}function ZL(a,b){var c=XL();if(c.vid===void 0||b&&!c.from_cookie)c.vid=a,c.from_cookie=b};
function wM(a){(jy(a)||ok())&&V(a,K.m.Rk,lo()||ko());!jy(a)&&ok()&&V(a,K.m.fl,"::")}function xM(a){if(ok()&&!jy(a)&&(oo()||V(a,K.m.Fk,!0),F(78))){cw(a);dw(a,Dp.Cf.Um,Io(O(a.D,K.m.cb)));var b=Dp.Cf.Vm;var c=O(a.D,K.m.Hc);dw(a,b,c===!0?1:c===!1?0:void 0);dw(a,Dp.Cf.Tm,Io(O(a.D,K.m.yb)));dw(a,Dp.Cf.Rm,Bs(Ho(O(a.D,K.m.nb)),Ho(O(a.D,K.m.Sb))))}};var SM={AW:rn.Z.Nm,G:rn.Z.Xn,DC:rn.Z.Vn};function TM(a){var b=dj(a);return""+cs(b.map(function(c){return c.value}).join("!"))}function UM(a){var b=Gp(a);return b&&SM[b.prefix]}function VM(a,b){var c=a[b];c&&(c.clearTimerId&&x.clearTimeout(c.clearTimerId),c.clearTimerId=x.setTimeout(function(){delete a[b]},36E5))};var zN=window,AN=document,BN=function(a){var b=zN._gaUserPrefs;if(b&&b.ioo&&b.ioo()||AN.documentElement.hasAttribute("data-google-analytics-opt-out")||a&&zN["ga-disable-"+a]===!0)return!0;try{var c=zN.external;if(c&&c._gaUserPrefs&&c._gaUserPrefs=="oo")return!0}catch(p){}for(var d=[],e=String(AN.cookie).split(";"),f=0;f<e.length;f++){var g=e[f].split("="),h=g[0].replace(/^\s*|\s*$/g,"");if(h&&h=="AMP_TOKEN"){var m=g.slice(1).join("=").replace(/^\s*|\s*$/g,"");m&&(m=decodeURIComponent(m));d.push(m)}}for(var n=
0;n<d.length;n++)if(d[n]=="$OPT_OUT")return!0;return AN.getElementById("__gaOptOutExtension")?!0:!1};
function NN(a){rb(a,function(c){c.charAt(0)==="_"&&delete a[c]});var b=a[K.m.Wb]||{};rb(b,function(c){c.charAt(0)==="_"&&delete b[c]})};function vO(a,b){}function wO(a,b){var c=function(){};return c}
function xO(a,b,c){};var yO=wO;var zO=function(a,b,c){for(var d=0;d<b.length;d++)a.hasOwnProperty(b[d])&&(a[String(b[d])]=c(a[String(b[d])]))};function AO(a,b,c){var d=this;}AO.M="internal.gtagConfig";
function CO(a,b){}
CO.publicName="gtagSet";function DO(){var a={};return a};function EO(a){}EO.M="internal.initializeServiceWorker";function FO(a,b){}FO.publicName="injectHiddenIframe";var GO=function(){var a=0;return function(b){switch(b){case 1:a|=1;break;case 2:a|=2;break;case 3:a|=4}return a}}();
function HO(a,b,c,d,e){}HO.M="internal.injectHtml";var LO={};
function NO(a,b,c,d){}var OO={dl:1,id:1},PO={};
function QO(a,b,c,d){}F(160)?QO.publicName="injectScript":NO.publicName="injectScript";QO.M="internal.injectScript";function RO(){return po()}RO.M="internal.isAutoPiiEligible";function SO(a){var b=!0;return b}SO.publicName="isConsentGranted";function TO(a){var b=!1;return b}TO.M="internal.isDebugMode";function UO(){return no()}UO.M="internal.isDmaRegion";function VO(a){var b=!1;return b}VO.M="internal.isEntityInfrastructure";function WO(a){var b=!1;if(!qh(a))throw H(this.getName(),["number"],[a]);b=F(a);return b}WO.M="internal.isFeatureEnabled";function XO(){var a=!1;return a}XO.M="internal.isFpfe";function YO(){var a=!1;return a}YO.M="internal.isGcpConversion";function ZO(){var a=!1;return a}ZO.M="internal.isLandingPage";function $O(){var a=!1;return a}$O.M="internal.isOgt";function aP(){var a;return a}aP.M="internal.isSafariPcmEligibleBrowser";function bP(){var a=Nh(function(b){qF(this).log("error",b)});a.publicName="JSON";return a};function cP(a){var b=void 0;return Bd(b)}cP.M="internal.legacyParseUrl";function dP(){return!1}
var eP={getItem:function(a){var b=null;return b},setItem:function(a,b){return!1},removeItem:function(a){}};function fP(){}fP.publicName="logToConsole";function gP(a,b){}gP.M="internal.mergeRemoteConfig";function hP(a,b,c){c=c===void 0?!0:c;var d=[];return Bd(d)}hP.M="internal.parseCookieValuesFromString";function iP(a){var b=void 0;return b}iP.publicName="parseUrl";function jP(a){}jP.M="internal.processAsNewEvent";function kP(a,b,c){var d;return d}kP.M="internal.pushToDataLayer";function lP(a){var b=za.apply(1,arguments),c=!1;if(!lh(a))throw H(this.getName(),["string"],arguments);for(var d=[this,a],e=l(b),f=e.next();!f.done;f=e.next())d.push(Ad(f.value,this.K,1));try{J.apply(null,d),c=!0}catch(g){return!1}return c}lP.publicName="queryPermission";function mP(a){var b=this;}mP.M="internal.queueAdsTransmission";function nP(a,b){var c=void 0;return c}nP.publicName="readAnalyticsStorage";function oP(){var a="";return a}oP.publicName="readCharacterSet";function pP(){return Wj}pP.M="internal.readDataLayerName";function qP(){var a="";return a}qP.publicName="readTitle";function rP(a,b){var c=this;if(!lh(a)||!hh(b))throw H(this.getName(),["string","function"],arguments);Iw(a,function(d){b.invoke(c.K,Bd(d,c.K,1))});}rP.M="internal.registerCcdCallback";function sP(a,b){return!0}sP.M="internal.registerDestination";var tP=["config","event","get","set"];function uP(a,b,c){}uP.M="internal.registerGtagCommandListener";function vP(a,b){var c=!1;return c}vP.M="internal.removeDataLayerEventListener";function wP(a,b){}
wP.M="internal.removeFormData";function xP(){}xP.publicName="resetDataLayer";function yP(a,b,c){var d=void 0;return d}yP.M="internal.scrubUrlParams";function zP(a){}zP.M="internal.sendAdsHit";function AP(a,b,c,d){}AP.M="internal.sendGtagEvent";function BP(a,b,c){}BP.publicName="sendPixel";function CP(a,b){}CP.M="internal.setAnchorHref";function DP(a){}DP.M="internal.setContainerConsentDefaults";function EP(a,b,c,d){var e=this;d=d===void 0?!0:d;var f=!1;
return f}EP.publicName="setCookie";function FP(a){}FP.M="internal.setCorePlatformServices";function GP(a,b){}GP.M="internal.setDataLayerValue";function HP(a){}HP.publicName="setDefaultConsentState";function IP(a,b){}IP.M="internal.setDelegatedConsentType";function JP(a,b){}JP.M="internal.setFormAction";function KP(a,b,c){c=c===void 0?!1:c;}KP.M="internal.setInCrossContainerData";function LP(a,b,c){return!1}LP.publicName="setInWindow";function MP(a,b,c){}MP.M="internal.setProductSettingsParameter";function NP(a,b,c){if(!lh(a)||!lh(b)||arguments.length!==3)throw H(this.getName(),["string","string","any"],arguments);for(var d=b.split("."),e=Jq(a),f=0;f<d.length-1;f++){if(e[d[f]]===void 0)e[d[f]]={};else if(!kd(e[d[f]]))throw Error("setRemoteConfigParameter failed, path contains a non-object type: "+d[f]);e=e[d[f]]}e[d[f]]=Ad(c,this.K,1);}NP.M="internal.setRemoteConfigParameter";function OP(a,b){}OP.M="internal.setTransmissionMode";function PP(a,b,c,d){var e=this;}PP.publicName="sha256";function QP(a,b,c){}
QP.M="internal.sortRemoteConfigParameters";function RP(a){}RP.M="internal.storeAdsBraidLabels";function SP(a,b){var c=void 0;return c}SP.M="internal.subscribeToCrossContainerData";var TP={},UP={};TP.getItem=function(a){var b=null;return b};TP.setItem=function(a,b){};
TP.removeItem=function(a){};TP.clear=function(){};TP.publicName="templateStorage";function VP(a,b){var c=!1;return c}VP.M="internal.testRegex";function WP(a){var b;return b};function XP(a,b){var c;return c}XP.M="internal.unsubscribeFromCrossContainerData";function YP(a){}YP.publicName="updateConsentState";function ZP(a){var b=!1;return b}ZP.M="internal.userDataNeedsEncryption";var $P;function aQ(a,b,c){$P=$P||new Yh;$P.add(a,b,c)}function bQ(a,b){var c=$P=$P||new Yh;if(c.C.hasOwnProperty(a))throw Error("Attempting to add a private function which already exists: "+a+".");if(c.contains(a))throw Error("Attempting to add a private function with an existing API name: "+a+".");c.C[a]=jb(b)?th(a,b):uh(a,b)}
function cQ(){return function(a){var b;var c=$P;if(c.contains(a))b=c.get(a,this);else{var d;if(d=c.C.hasOwnProperty(a)){var e=this.K.sb();if(e){var f=!1,g=e.Jb();if(g){Ah(g)||(f=!0);}d=f}else d=!0}if(d){var h=c.C.hasOwnProperty(a)?c.C[a]:void 0;
b=h}else throw Error(a+" is not a valid API name.");}return b}};function dQ(){var a=function(c){return void bQ(c.M,c)},b=function(c){return void aQ(c.publicName,c)};b(kF);b(rF);b(FG);b(HG);b(IG);b(PG);b(RG);b(MH);b(bP());b(OH);b(iL);b(jL);b(FL);b(GL);b(HL);b(NL);b(CO);b(FO);b(SO);b(fP);b(iP);b(lP);b(oP);b(qP);b(BP);b(EP);b(HP);b(LP);b(PP);b(TP);b(YP);aQ("Math",yh());aQ("Object",Wh);aQ("TestHelper",$h());aQ("assertApi",vh);aQ("assertThat",wh);aQ("decodeUri",Bh);aQ("decodeUriComponent",Ch);aQ("encodeUri",Dh);aQ("encodeUriComponent",Eh);aQ("fail",Jh);aQ("generateRandom",
Kh);aQ("getTimestamp",Lh);aQ("getTimestampMillis",Lh);aQ("getType",Mh);aQ("makeInteger",Oh);aQ("makeNumber",Ph);aQ("makeString",Qh);aQ("makeTableMap",Rh);aQ("mock",Uh);aQ("mockObject",Vh);aQ("fromBase64",bL,!("atob"in x));aQ("localStorage",eP,!dP());aQ("toBase64",WP,!("btoa"in x));a(jF);a(nF);a(HF);a(TF);a($F);a(eG);a(uG);a(DG);a(GG);a(JG);a(KG);a(LG);a(MG);a(NG);a(OG);a(QG);a(SG);a(LH);a(NH);a(PH);a(QH);a(RH);a(SH);a(TH);a(UH);a(ZH);a(gI);a(hI);a(sI);a(xI);a(CI);a(LI);a(QI);a(cJ);a(eJ);a(sJ);a(tJ);
a(vJ);a($K);a(aL);a(cL);a(dL);a(eL);a(fL);a(gL);a(lL);a(mL);a(nL);a(oL);a(pL);a(qL);a(rL);a(sL);a(tL);a(uL);a(vL);a(wL);a(yL);a(zL);a(AL);a(BL);a(CL);a(DL);a(EL);a(IL);a(JL);a(KL);a(LL);a(ML);a(PL);a(AO);a(EO);a(HO);a(QO);a(RO);a(TO);a(UO);a(VO);a(WO);a(XO);a(YO);a(ZO);a($O);a(aP);a(cP);a(sG);a(gP);a(hP);a(jP);a(kP);a(mP);a(pP);a(rP);a(sP);a(uP);a(vP);a(wP);a(yP);a(zP);a(AP);a(CP);a(DP);a(FP);a(GP);a(IP);a(JP);a(KP);a(MP);a(NP);a(OP);a(QP);a(RP);a(SP);a(VP);a(XP);a(ZP);bQ("internal.IframingStateSchema",
DO());
F(104)&&a(kL);F(160)?b(QO):b(NO);F(177)&&b(nP);return cQ()};var hF;
function eQ(){var a=data.sandboxed_scripts,b=data.security_groups;a:{var c=data.runtime||[],d=data.runtime_lines;hF=new Ve;fQ();Cf=gF();var e=hF,f=dQ(),g=new td("require",f);g.Ua();e.C.C.set("require",g);Qa.set("require",g);for(var h=[],m=0;m<c.length;m++){var n=c[m];if(!Array.isArray(n)||n.length<3){if(n.length===0)continue;break a}d&&d[m]&&d[m].length&&Yf(n,d[m]);try{hF.execute(n),F(120)&&il&&n[0]===50&&h.push(n[1])}catch(r){}}F(120)&&(Qf=h)}if(a&&a.length)for(var p=0;p<a.length;p++){var q=a[p].replace(/^_*/,
"");kk[q]=["sandboxedScripts"]}gQ(b)}function fQ(){hF.Vc(function(a,b,c){vp.SANDBOXED_JS_SEMAPHORE=vp.SANDBOXED_JS_SEMAPHORE||0;vp.SANDBOXED_JS_SEMAPHORE++;try{return a.apply(b,c)}finally{vp.SANDBOXED_JS_SEMAPHORE--}})}function gQ(a){a&&rb(a,function(b,c){for(var d=0;d<c.length;d++){var e=c[d].replace(/^_*/,"");kk[e]=kk[e]||[];kk[e].push(b)}})};function hQ(a){Qw(Kw("developer_id."+a,!0),0,{})};var iQ=Array.isArray;function jQ(a,b){return ld(a,b||null)}function Y(a){return window.encodeURIComponent(a)}function kQ(a,b,c){Jc(a,b,c)}
function lQ(a){var b=["veinteractive.com","ve-interactive.cn"];if(!a)return!1;var c=Ok(Uk(a),"host");if(!c)return!1;for(var d=0;b&&d<b.length;d++){var e=b[d]&&b[d].toLowerCase();if(e){var f=c.length-e.length;f>0&&e.charAt(0)!=="."&&(f--,e="."+e);if(f>=0&&c.indexOf(e,f)===f)return!0}}return!1}function mQ(a,b,c){for(var d={},e=!1,f=0;a&&f<a.length;f++)a[f]&&a[f].hasOwnProperty(b)&&a[f].hasOwnProperty(c)&&(d[a[f][b]]=a[f][c],e=!0);return e?d:null}
function nQ(a,b){var c={};if(a)for(var d in a)a.hasOwnProperty(d)&&(c[d]=a[d]);if(b){var e=mQ(b,"parameter","parameterValue");e&&(c=jQ(e,c))}return c}function oQ(a,b,c){if(Ir()){b&&B(b)}else return Fc(a,b,c,void 0)}function pQ(){return x.location.href}function qQ(a,b){return uk(a,b||2)}function rQ(a,b){x[a]=b}function sQ(a,b,c){var d=x;b&&(d[a]===void 0||c&&!d[a])&&(d[a]=b);return d[a]}function tQ(a,b){if(Ir()){b&&B(b)}else Hc(a,b)}
var uQ={};var Z={securityGroups:{}};
Z.securityGroups.v=["google"],Z.__v=function(a){var b=a.vtp_name;if(!b||!b.replace)return!1;var c=qQ(b.replace(/\\\./g,"."),a.vtp_dataLayerVersion||1);return c!==void 0?c:a.vtp_defaultValue},Z.__v.F="v",Z.__v.isVendorTemplate=!0,Z.__v.priorityOverride=0,Z.__v.isInfrastructure=!0,Z.__v["5"]=!0;
Z.securityGroups.rep=["google"],Z.__rep=function(a){var b=Gp(a.vtp_containerId,!0);if(b){var c,d;switch(b.prefix){case "AW":c=zJ;d=Qm.X.Da;break;case "DC":c=QJ;d=Qm.X.Da;break;case "GF":c=VJ;d=Qm.X.Fb;break;case "HA":c=aK;d=Qm.X.Fb;break;case "UA":c=yK;d=Qm.X.Fb;break;case "MC":c=yO(b,a.vtp_gtmEventId);d=Qm.X.Fc;break;default:B(a.vtp_gtmOnFailure);return}c?(B(a.vtp_gtmOnSuccess),F(185)?Fq(a.vtp_containerId,c,d,a.vtp_remoteConfig):(Fq(a.vtp_containerId,c,d),a.vtp_remoteConfig&&Lq(a.vtp_containerId,
a.vtp_remoteConfig||{}))):B(a.vtp_gtmOnFailure)}else B(a.vtp_gtmOnFailure)},Z.__rep.F="rep",Z.__rep.isVendorTemplate=!0,Z.__rep.priorityOverride=0,Z.__rep.isInfrastructure=!1,Z.__rep["5"]=!1;
Z.securityGroups.read_event_data=["google"],function(){function a(b,c){return{key:c}}(function(b){Z.__read_event_data=b;Z.__read_event_data.F="read_event_data";Z.__read_event_data.isVendorTemplate=!0;Z.__read_event_data.priorityOverride=0;Z.__read_event_data.isInfrastructure=!1;Z.__read_event_data["5"]=!1})(function(b){var c=b.vtp_eventDataAccess,d=b.vtp_keyPatterns||[],e=b.vtp_createPermissionError;return{assert:function(f,g){if(g!=null&&!kb(g))throw e(f,{key:g},"Key must be a string.");if(c!=="any"){try{if(c===
"specific"&&g!=null&&Jg(g,d))return}catch(h){throw e(f,{key:g},"Invalid key filter.");}throw e(f,{key:g},"Prohibited read from event data.");}},T:a}})}();







Z.securityGroups.detect_user_provided_data=["google"],function(){function a(b,c){return{dataSource:c}}(function(b){Z.__detect_user_provided_data=b;Z.__detect_user_provided_data.F="detect_user_provided_data";Z.__detect_user_provided_data.isVendorTemplate=!0;Z.__detect_user_provided_data.priorityOverride=0;Z.__detect_user_provided_data.isInfrastructure=!1;Z.__detect_user_provided_data["5"]=!1})(function(b){var c=b.vtp_createPermissionError;return{assert:function(d,e){if(e!=="auto"&&e!=="manual"&&e!==
"code")throw c(d,{},"Unknown user provided data source.");if(b.vtp_limitDataSources)if(e!=="auto"||b.vtp_allowAutoDataSources){if(e==="manual"&&!b.vtp_allowManualDataSources)throw c(d,{},"Detection of user provided data via manually specified CSS selectors is not allowed.");if(e==="code"&&!b.vtp_allowCodeDataSources)throw c(d,{},"Detection of user provided data from an in-page variable is not allowed.");}else throw c(d,{},"Automatic detection of user provided data is not allowed.");},T:a}})}();










Z.securityGroups.get=["google"],Z.__get=function(a){var b=a.vtp_settings,c=b.eventParameters||{},d=String(a.vtp_eventName),e={};e.eventId=a.vtp_gtmEventId;e.priorityId=a.vtp_gtmPriorityId;a.vtp_deferrable&&(e.deferrable=!0);var f=Nw(String(b.streamId),d,c);Qw(f,e.eventId,e);a.vtp_gtmOnSuccess()},Z.__get.F="get",Z.__get.isVendorTemplate=!0,Z.__get.priorityOverride=0,Z.__get.isInfrastructure=!1,Z.__get["5"]=!1;


Z.securityGroups.zone=[],function(){var a={},b=function(d){for(var e=0;e<d.length;e++)if(!d[e])return!1;return!0},c=function(d){var e=b(d.vtp_boundaries||[]);if(d.vtp_gtmTagId in a)iG(a[d.vtp_gtmTagId],d.vtp_gtmEventId,e);else if(e){var f=d.vtp_childContainers.map(function(n){return n.publicId}),g=d.vtp_enableTypeRestrictions?d.vtp_whitelistedTypes.map(function(n){return n.typeId}):null,h={};var m=kG(d.vtp_gtmEventId,f,g,h,TB(1,d.vtp_gtmEntityIndex,d.vtp_gtmEntityName),!!d.vtp_inheritParentConfig);a[d.vtp_gtmTagId]=m}B(d.vtp_gtmOnSuccess)};Z.__zone=c;Z.__zone.F="zone";Z.__zone.isVendorTemplate=!0;Z.__zone.priorityOverride=0;Z.__zone.isInfrastructure=
!1;Z.__zone["5"]=!0}();

var yp={dataLayer:vk,callback:function(a){jk.hasOwnProperty(a)&&jb(jk[a])&&jk[a]();delete jk[a]},bootstrap:0};
function vQ(){xp();Hm();OB();Cb(kk,Z.securityGroups);var a=Em(tm()),b,c=a==null?void 0:(b=a.context)==null?void 0:b.source;Xo(c,a==null?void 0:a.parent);c!==2&&c!==4&&c!==3||M(142);Pf={No:dg}}var wQ=!1;
function ho(){try{if(wQ||!Om()){Sj();Pj.P=Si(18,"");
Pj.qb="ad_storage|analytics_storage|ad_user_data|ad_personalization";Pj.Sa="ad_storage|analytics_storage|ad_user_data";Pj.Ba="5770";Pj.Ba="5770";if(F(109)){}Ia[7]=!0;var a=wp("debugGroupId",function(){return String(Math.floor(Number.MAX_SAFE_INTEGER*Math.random()))});dp(a);up();YE();jr();Ap();if(Im()){pG();EC().removeExternalRestrictions(Bm());}else{Nf();If=Z;Kf=IE;fg=new mg;eQ();vQ();Jr();fo||(eo=jo());
rp();TD();fD();zD=!1;z.readyState==="complete"?BD():Kc(x,"load",BD);$C();il&&(nq(Bq),x.setInterval(Aq,864E5),nq(ZE),nq(rC),nq(cA),nq(Eq),nq(dF),nq(CC),F(120)&&(nq(wC),nq(xC),nq(yC)),$E={},nq(aF),Vi());jl&&(Sn(),Up(),VD(),ZD(),XD(),In("bt",String(Pj.C?2:Pj.N?1:0)),In("ct",String(Pj.C?0:Pj.N?1:Ir()?2:3)),WD());yE();bo(1);qG();dE();ik=zb();yp.bootstrap=ik;Pj.ka&&SD();F(109)&&yA();F(134)&&(typeof x.name==="string"&&Eb(x.name,"web-pixel-sandbox-CUSTOM")&&ad()?hQ("dMDg0Yz"):x.Shopify&&(hQ("dN2ZkMj"),ad()&&hQ("dNTU0Yz")))}}}catch(b){bo(4),xq()}}
(function(a){function b(){n=z.documentElement.getAttribute("data-tag-assistant-present");Ko(n)&&(m=h.Xk)}function c(){m&&wc?g(m):a()}if(!x[Si(37,"__TAGGY_INSTALLED")]){var d=!1;if(z.referrer){var e=Uk(z.referrer);d=Qk(e,"host")===Si(38,"cct.google")}if(!d){var f=ls(Si(39,"googTaggyReferrer"));d=!(!f.length||!f[0].length)}d&&(x[Si(37,"__TAGGY_INSTALLED")]=!0,Fc(Si(40,"https://cct.google/taggy/agent.js")))}var g=function(u){var v="GTM",w="GTM";dk&&(v="OGT",w="GTAG");
var y=Si(23,"google.tagmanager.debugui2.queue"),A=x[y];A||(A=[],x[y]=A,Fc("https://"+Tj.vg+"/debug/bootstrap?id="+jg.ctid+"&src="+w+"&cond="+String(u)+"&gtm="+Nr()));var C={messageType:"CONTAINER_STARTING",data:{scriptSource:wc,containerProduct:v,debug:!1,id:jg.ctid,targetRef:{ctid:jg.ctid,isDestination:zm()},aliases:Cm(),destinations:Am()}};C.data.resume=function(){a()};Tj.Qm&&(C.data.initialPublish=!0);A.push(C)},h={ao:1,al:2,xl:3,Wj:4,Xk:5};h[h.ao]="GTM_DEBUG_LEGACY_PARAM";h[h.al]="GTM_DEBUG_PARAM";h[h.xl]="REFERRER";
h[h.Wj]="COOKIE";h[h.Xk]="EXTENSION_PARAM";var m=void 0,n=void 0,p=Ok(x.location,"query",!1,void 0,"gtm_debug");Ko(p)&&(m=h.al);if(!m&&z.referrer){var q=Uk(z.referrer);Qk(q,"host")===Si(24,"tagassistant.google.com")&&(m=h.xl)}if(!m){var r=ls("__TAG_ASSISTANT");r.length&&r[0].length&&(m=h.Wj)}m||b();if(!m&&Jo(n)){var t=!1;Kc(z,"TADebugSignal",function(){t||(t=!0,b(),c())},!1);x.setTimeout(function(){t||(t=!0,b(),c())},200)}else c()})(function(){F(83)&&wQ&&!jo()["0"]?go():ho()});

})()

