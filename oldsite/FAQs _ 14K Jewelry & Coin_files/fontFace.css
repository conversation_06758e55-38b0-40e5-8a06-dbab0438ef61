/* fonts helvetica */

/* Original old fonts */

@font-face {
    font-family: "Helvetica Neue";
    src: url("Fonts/b7693a83-b861-4aa6-85e0-9ecf676bc4d6.eot?#iefix") format("embedded-opentype"),
    url("Fonts/bcf54343-d033-41ee-bbd7-2b77df3fe7ba.woff") format("woff"),
    url("Fonts/b0ffdcf0-26da-47fd-8485-20e4a40d4b7d.ttf") format("truetype"),
    url("Fonts/da09f1f1-062a-45af-86e1-2bbdb3dd94f9.svg#da09f1f1-062a-45af-86e1-2bbdb3dd94f9") format("svg");
}

@font-face {
    font-family: "Helvetica Neue Thin";
    font-weight: 200;
    src: url("Fonts/56be84de-9d60-4089-8df0-0ea6ec786b84.eot?#iefix") format("embedded-opentype"),
    url("Fonts/50d35bbc-dfd4-48f1-af16-cf058f69421d.woff") format("woff"),
    url("Fonts/278bef59-6be1-4800-b5ac-1f769ab47430.ttf") format("truetype"),
    url("Fonts/2e309b1b-08b8-477f-bc9e-7067cf0af0b3.svg#2e309b1b-08b8-477f-bc9e-7067cf0af0b3") format("svg");
}

@font-face {
    font-family: "Helvetica Neue Medium";
    font-weight: bold;
    src: url("Fonts/07fe0fec-b63f-4963-8ee1-535528b67fdb.eot??#iefix") format("embedded-opentype"),
    url("Fonts/60be5c39-863e-40cb-9434-6ebafb62ab2b.woff") format("woff"),
    url("Fonts/4c6503c9-859b-4d3b-a1d5-2d42e1222415.ttf") format("truetype"),
    url("Fonts/36c182c6-ef98-4021-9b0d-d63122c2bbf5.svg#36c182c6-ef98-4021-9b0d-d63122c2bbf5") format("svg");
}

/* Kwazimodo temporary studio fonts */

@font-face{
    font-family:"Helvetica Neue Ultralight";
    src:url("Fonts/072d8516-5d40-44bc-b694-65b3c8bd8fa5.eot?#iefix");
    src:url("Fonts/072d8516-5d40-44bc-b694-65b3c8bd8fa5.eot?#iefix") format("eot"),
    url("Fonts/723fd1ed-5aad-454d-af88-3711c5caf1c7.woff") format("woff"),
    url("Fonts/2a81d94e-d365-4f5f-9957-7e96414d6a72.ttf") format("truetype"),
    url("Fonts/6be38bc3-96b3-47e3-a9ef-e1a184b54d64.svg#6be38bc3-96b3-47e3-a9ef-e1a184b54d64") format("svg");
}

@font-face {
    font-family: "Helvetica Neue Italic";
    font-style: italic;
    src: url("Fonts/58a5cbff-d570-4c18-a5e3-60868dc07ae8.eot?#iefix") format("embedded-opentype"),
    url("Fonts/2c056da8-4920-4e20-8c69-8a6b315458a9.woff") format("woff"),
    url("Fonts/2381d918-136d-444f-8391-db0cba6da388.ttf") format("truetype"),
    url("Fonts/d0697971-6f58-4597-942e-8beabd1adc87.svg#d0697971-6f58-4597-942e-8beabd1adc87") format("svg");
}

@font-face{
    font-family:"Helvetica Neue Light";
    src:url("Fonts/88fcd49a-13c7-4d0c-86b1-ad1e258bd75d.eot?#iefix");
    src:url("Fonts/88fcd49a-13c7-4d0c-86b1-ad1e258bd75d.eot?#iefix") format("eot"),
    url("Fonts/9a2e4855-380f-477f-950e-d98e8db54eac.woff") format("woff"),
    url("Fonts/fa82d0ee-4fbd-4cc9-bf9f-226ad1fcbae2.ttf") format("truetype"),
    url("Fonts/48d599a6-92b5-4d43-a4ac-8959f6971853.svg#48d599a6-92b5-4d43-a4ac-8959f6971853") format("svg");
}

@font-face{
    font-family:"Helvetica Neue Bold";
    src:url("Fonts/db853e0e-929b-4272-b420-c946c954cf3a.eot?#iefix");
    src:url("Fonts/db853e0e-929b-4272-b420-c946c954cf3a.eot?#iefix") format("eot"),
    url("Fonts/4a9c62ab-b359-4081-8383-a0d1cdebd111.woff") format("woff"),
    url("Fonts/db5f9ba6-05a4-433a-9461-0a6f257a0c3a.ttf") format("truetype"),
    url("Fonts/25e09910-ffc3-4fc4-b0d1-db9a95dface8.svg#25e09910-ffc3-4fc4-b0d1-db9a95dface8") format("svg");
}

@font-face{
    font-family:"Helvetica Neue Black";
    src:url("Fonts/c8ec8ade-129c-47df-86b4-f9f1cd69ac15.eot?#iefix");
    src:url("Fonts/c8ec8ade-129c-47df-86b4-f9f1cd69ac15.eot?#iefix") format("eot"),
    url("Fonts/6d5b06b3-f8db-4de6-aa46-2a6de9569b51.woff") format("woff"),
    url("Fonts/86438896-04f9-4558-a21a-e7a6d6061591.ttf") format("truetype"),
    url("Fonts/c486d95f-f220-435d-8a83-5d9497f04c49.svg#c486d95f-f220-435d-8a83-5d9497f04c49") format("svg");
}

/* New Studio fonts */

@font-face{
    font-family:"HelveticaNeueW01-UltLt";
    src:url("Fonts/41774233-b9da-44be-b252-6a7b612fb1c7.eot?#iefix");
    src:url("Fonts/41774233-b9da-44be-b252-6a7b612fb1c7.eot?#iefix") format("eot"),url("Fonts/4bff1fbb-b4bf-4d95-9c47-efcb14384e36.woff") format("woff"),url("Fonts/7f1f2a7d-3837-4c93-b373-f03c5da3f9a1.ttf") format("truetype"),url("Fonts/d9f2752a-8d82-4cf1-b82f-109c1105be7f.svg#d9f2752a-8d82-4cf1-b82f-109c1105be7f") format("svg");
}

@font-face{
    font-family:"HelveticaNeueW01-Thin";
    src:url("Fonts/56be84de-9d60-4089-8df0-0ea6ec786b84.eot?#iefix");
    src:url("Fonts/56be84de-9d60-4089-8df0-0ea6ec786b84.eot?#iefix") format("eot"),url("Fonts/50d35bbc-dfd4-48f1-af16-cf058f69421d.woff") format("woff"),url("Fonts/278bef59-6be1-4800-b5ac-1f769ab47430.ttf") format("truetype"),url("Fonts/2e309b1b-08b8-477f-bc9e-7067cf0af0b3.svg#2e309b1b-08b8-477f-bc9e-7067cf0af0b3") format("svg");
}

@font-face{
    font-family:"HelveticaNeueW02-UltLt";
    src:url("Fonts/787ee748-9cce-45a0-910b-6b5c6e87e327.eot?#iefix");
    src:url("Fonts/787ee748-9cce-45a0-910b-6b5c6e87e327.eot?#iefix") format("eot"),url("Fonts/84558c76-9f1b-44d2-ac62-d7937f43809b.woff") format("woff"),url("Fonts/411a88fe-e483-4fb8-af42-8369ebb1138d.ttf") format("truetype"),url("Fonts/6dfe33a4-0ad5-4c85-8e01-f48ecfe3c167.svg#6dfe33a4-0ad5-4c85-8e01-f48ecfe3c167") format("svg");
}

@font-face{
    font-family:"HelveticaNeueW02-Thin";
    src:url("Fonts/30b6ffc3-3b64-40dd-9ff8-a3a850daf535.eot?#iefix");
    src:url("Fonts/30b6ffc3-3b64-40dd-9ff8-a3a850daf535.eot?#iefix") format("eot"),url("Fonts/775a65da-14aa-4634-be95-6724c05fd522.woff") format("woff"),url("Fonts/988eaaa7-5565-4f65-bb17-146b650ce9e9.ttf") format("truetype"),url("Fonts/3503a1a6-91c3-4c42-8e66-2ea7b2b57541.svg#3503a1a6-91c3-4c42-8e66-2ea7b2b57541") format("svg");
}

@font-face{
    font-family:"HelveticaNeueW01-45Ligh";
    src:url("Fonts/ae1656aa-5f8f-4905-aed0-93e667bd6e4a.eot?#iefix");
    src:url("Fonts/ae1656aa-5f8f-4905-aed0-93e667bd6e4a.eot?#iefix") format("eot"),url("Fonts/530dee22-e3c1-4e9f-bf62-c31d510d9656.woff") format("woff"),url("Fonts/688ab72b-4deb-4e15-a088-89166978d469.ttf") format("truetype"),url("Fonts/7816f72f-f47e-4715-8cd7-960e3723846a.svg#7816f72f-f47e-4715-8cd7-960e3723846a") format("svg");
}

@font-face{
    font-family:"HelveticaNeueW01-55Roma";
    src:url("Fonts/b7693a83-b861-4aa6-85e0-9ecf676bc4d6.eot?#iefix");
    src:url("Fonts/b7693a83-b861-4aa6-85e0-9ecf676bc4d6.eot?#iefix") format("eot"),url("Fonts/bcf54343-d033-41ee-bbd7-2b77df3fe7ba.woff") format("woff"),url("Fonts/b0ffdcf0-26da-47fd-8485-20e4a40d4b7d.ttf") format("truetype"),url("Fonts/da09f1f1-062a-45af-86e1-2bbdb3dd94f9.svg#da09f1f1-062a-45af-86e1-2bbdb3dd94f9") format("svg");
}

@font-face{
    font-family:"HelveticaNeueW01-65Medi";
    font-weight: bold;
    src:url("Fonts/07fe0fec-b63f-4963-8ee1-535528b67fdb.eot?#iefix");
    src:url("Fonts/07fe0fec-b63f-4963-8ee1-535528b67fdb.eot?#iefix") format("eot"),url("Fonts/60be5c39-863e-40cb-9434-6ebafb62ab2b.woff") format("woff"),url("Fonts/4c6503c9-859b-4d3b-a1d5-2d42e1222415.ttf") format("truetype"),url("Fonts/36c182c6-ef98-4021-9b0d-d63122c2bbf5.svg#36c182c6-ef98-4021-9b0d-d63122c2bbf5") format("svg");
}

@font-face{
    font-family:"HelveticaNeueW01-75Bold";
    src:url("Fonts/c07fef9e-a934-42d7-92ad-69205f2b8a00.eot?#iefix");
    src:url("Fonts/c07fef9e-a934-42d7-92ad-69205f2b8a00.eot?#iefix") format("eot"),url("Fonts/14ff6081-326d-4dae-b778-d7afa66166fc.woff") format("woff"),url("Fonts/8fda1e47-19be-46c7-8d83-8d4fb35572f0.ttf") format("truetype"),url("Fonts/f751c8ae-1057-46d9-8d74-62592e002568.svg#f751c8ae-1057-46d9-8d74-62592e002568") format("svg");
}

@font-face{
    font-family:"HelveticaNeueW01-95Blac";
    src:url("Fonts/b26ccb58-f2e3-47aa-a83a-02861bf54862.eot?#iefix");
    src:url("Fonts/b26ccb58-f2e3-47aa-a83a-02861bf54862.eot?#iefix") format("eot"),url("Fonts/74649485-cd74-443e-9d54-331ccd448900.woff") format("woff"),url("Fonts/feb77f4f-9e6b-4f88-909a-66199fd402ed.ttf") format("truetype"),url("Fonts/2e490192-e531-4236-9c55-90daaad4a34e.svg#2e490192-e531-4236-9c55-90daaad4a34e") format("svg");
}

@font-face{
    font-family:"HelveticaNeueW02-45Ligh";
    src:url("Fonts/88fcd49a-13c7-4d0c-86b1-ad1e258bd75d.eot?#iefix");
    src:url("Fonts/88fcd49a-13c7-4d0c-86b1-ad1e258bd75d.eot?#iefix") format("eot"),url("Fonts/9a2e4855-380f-477f-950e-d98e8db54eac.woff") format("woff"),url("Fonts/fa82d0ee-4fbd-4cc9-bf9f-226ad1fcbae2.ttf") format("truetype"),url("Fonts/48d599a6-92b5-4d43-a4ac-8959f6971853.svg#48d599a6-92b5-4d43-a4ac-8959f6971853") format("svg");
}

@font-face{
    font-family:"HelveticaNeueW02-55Roma";
    src:url("Fonts/0b3a3fca-0fad-402b-bd38-fdcbad1ef776.eot?#iefix");
    src:url("Fonts/0b3a3fca-0fad-402b-bd38-fdcbad1ef776.eot?#iefix") format("eot"),url("Fonts/d5af76d8-a90b-4527-b3a3-182207cc3250.woff") format("woff"),url("Fonts/1d238354-d156-4dde-89ea-4770ef04b9f9.ttf") format("truetype"),url("Fonts/b68875cb-14a9-472e-8177-0247605124d7.svg#b68875cb-14a9-472e-8177-0247605124d7") format("svg");
}

@font-face{
    font-family:"HelveticaNeueW02-65Medi";
    font-weight: bold;
    src:url("Fonts/55f60419-09c3-42bd-b81f-1983ff093852.eot?#iefix");
    src:url("Fonts/55f60419-09c3-42bd-b81f-1983ff093852.eot?#iefix") format("eot"),url("Fonts/5b4a262e-3342-44e2-8ad7-719998a68134.woff") format("woff"),url("Fonts/4a3ef5d8-cfd9-4b96-bd67-90215512f1e5.ttf") format("truetype"),url("Fonts/58ab5075-53ea-46e6-9783-cbb335665f88.svg#58ab5075-53ea-46e6-9783-cbb335665f88") format("svg");
}

@font-face{
    font-family:"HelveticaNeueW02-75Bold";
    src:url("Fonts/db853e0e-929b-4272-b420-c946c954cf3a.eot?#iefix");
    src:url("Fonts/db853e0e-929b-4272-b420-c946c954cf3a.eot?#iefix") format("eot"),url("Fonts/4a9c62ab-b359-4081-8383-a0d1cdebd111.woff") format("woff"),url("Fonts/db5f9ba6-05a4-433a-9461-0a6f257a0c3a.ttf") format("truetype"),url("Fonts/25e09910-ffc3-4fc4-b0d1-db9a95dface8.svg#25e09910-ffc3-4fc4-b0d1-db9a95dface8") format("svg");
}

@font-face{
    font-family:"HelveticaNeueW02-95Blac";
    src:url("Fonts/c8ec8ade-129c-47df-86b4-f9f1cd69ac15.eot?#iefix");
    src:url("Fonts/c8ec8ade-129c-47df-86b4-f9f1cd69ac15.eot?#iefix") format("eot"),url("Fonts/6d5b06b3-f8db-4de6-aa46-2a6de9569b51.woff") format("woff"),url("Fonts/86438896-04f9-4558-a21a-e7a6d6061591.ttf") format("truetype"),url("Fonts/c486d95f-f220-435d-8a83-5d9497f04c49.svg#c486d95f-f220-435d-8a83-5d9497f04c49") format("svg");
}

@font-face{
    font-family:"HelveticaNeueW10-45Ligh";
    src:url("Fonts/5b85c7cc-6ad4-4226-83f5-9d19e2974123.eot?#iefix");
    src:url("Fonts/5b85c7cc-6ad4-4226-83f5-9d19e2974123.eot?#iefix") format("eot"),url("Fonts/835e7b4f-b524-4374-b57b-9a8fc555fd4e.woff") format("woff"),url("Fonts/2c694ef6-9615-473e-8cf4-d8d00c6bd973.ttf") format("truetype"),url("Fonts/3fc84193-a13f-4fe8-87f7-238748a4ac54.svg#3fc84193-a13f-4fe8-87f7-238748a4ac54") format("svg");
}

@font-face{
    font-family:"HelveticaNeueW10-65Medi";
    font-weight: bold;
    src:url("Fonts/7092fdcc-7036-48ce-ae23-cfbc9be2e3b0.eot?#iefix");
    src:url("Fonts/7092fdcc-7036-48ce-ae23-cfbc9be2e3b0.eot?#iefix") format("eot"),url("Fonts/5b29e833-1b7a-40ab-82a5-cfd69c8650f4.woff") format("woff"),url("Fonts/b0298148-2d59-44d1-9ec9-1ca6bb097603.ttf") format("truetype"),url("Fonts/ae1dea8c-a953-4845-b616-74a257ba72e6.svg#ae1dea8c-a953-4845-b616-74a257ba72e6") format("svg");
}

@font-face{
    font-family:"HelveticaNeueW10-75Bold";
    src:url("Fonts/41fb73ed-90c8-456d-838e-254f4dfda106.eot?#iefix");
    src:url("Fonts/41fb73ed-90c8-456d-838e-254f4dfda106.eot?#iefix") format("eot"),url("Fonts/d85949a1-c37a-43f7-9d09-fb056acf0c27.woff") format("woff"),url("Fonts/5289fb0d-053f-4fac-9c67-2d02365d6d05.ttf") format("truetype"),url("Fonts/1376b116-8954-4534-8045-eabe8e2fcaa4.svg#1376b116-8954-4534-8045-eabe8e2fcaa4") format("svg");
}

@font-face{
    font-family:"HelveticaNeueW10-95Blac";
    src:url("Fonts/35f91c73-f2f1-4dd3-83ee-3649dc7ff4ed.eot?#iefix");
    src:url("Fonts/35f91c73-f2f1-4dd3-83ee-3649dc7ff4ed.eot?#iefix") format("eot"),url("Fonts/b1d7b778-bdf9-4ff3-b4e8-8cdabf234863.woff") format("woff"),url("Fonts/27bdcc15-be32-4f6c-a7df-12494376e947.ttf") format("truetype"),url("Fonts/3af50121-f334-4e73-b155-b105e0029509.svg#3af50121-f334-4e73-b155-b105e0029509") format("svg");
}

@font-face{
    font-family:"HelveticaNeueW10-35Thin";
    src:url("Fonts/93b6bf6a-418e-4a8f-8f79-cb9c99ef3e32.eot?#iefix");
    src:url("Fonts/93b6bf6a-418e-4a8f-8f79-cb9c99ef3e32.eot?#iefix") format("eot"),url("Fonts/c881c21b-4148-4a11-a65d-f35e42999bc8.woff") format("woff"),url("Fonts/03634cf1-a9c9-4e13-b049-c90d830423d4.ttf") format("truetype"),url("Fonts/1bc99c0a-298b-46f9-b325-18b5e5169795.svg#1bc99c0a-298b-46f9-b325-18b5e5169795") format("svg");
}

@font-face{
    font-family:"HelveticaNeueW10-25UltL";
    src:url("Fonts/072d8516-5d40-44bc-b694-65b3c8bd8fa5.eot?#iefix");
    src:url("Fonts/072d8516-5d40-44bc-b694-65b3c8bd8fa5.eot?#iefix") format("eot"),url("Fonts/723fd1ed-5aad-454d-af88-3711c5caf1c7.woff") format("woff"),url("Fonts/2a81d94e-d365-4f5f-9957-7e96414d6a72.ttf") format("truetype"),url("Fonts/6be38bc3-96b3-47e3-a9ef-e1a184b54d64.svg#6be38bc3-96b3-47e3-a9ef-e1a184b54d64") format("svg");
}

@font-face{
    font-family:"HelveticaNeueW10-55Roma";
    src:url("Fonts/f1feaed7-6bce-400a-a07e-a893ae43a680.eot?#iefix");
    src:url("Fonts/f1feaed7-6bce-400a-a07e-a893ae43a680.eot?#iefix") format("eot"),url("Fonts/8ac9e38d-29c6-41ea-8e47-4ae4d2b1a4e1.woff") format("woff"),url("Fonts/4bd09087-655e-4abb-844c-dccdeb68003d.ttf") format("truetype"),url("Fonts/df234d87-eada-4058-aa80-5871e7fbe1c3.svg#df234d87-eada-4058-aa80-5871e7fbe1c3") format("svg");
}


/*
This CSS resource incorporates links to font software which is
the valuable copyrighted property of Monotype Imaging and/or
its suppliers. You may not attempt to copy, install, redistribute, convert,
modify or reverse engineer this font software. Please contact Monotype Imaging
with any questions regarding Web Fonts:  http://webfonts.fonts.com
*/

/* END fonts helvetica */
