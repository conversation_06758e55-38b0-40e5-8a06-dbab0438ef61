!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("react")):"function"==typeof define&&define.amd?define("rb_wixui.thunderbolt[FiveGridLine_SolidLine]",["react"],t):"object"==typeof exports?exports["rb_wixui.thunderbolt[FiveGridLine_SolidLine]"]=t(require("react")):e["rb_wixui.thunderbolt[FiveGridLine_SolidLine]"]=t(e.React)}("undefined"!=typeof self?self:this,(function(e){return function(){var t={5329:function(t){"use strict";t.exports=e},448:function(e){function t(){return e.exports=t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,t.apply(null,arguments)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}},r={};function n(e){var o=r[e];if(void 0!==o)return o.exports;var i=r[e]={exports:{}};return t[e](i,i.exports,n),i.exports}n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,{a:t}),t},n.d=function(e,t){for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var o={};return function(){"use strict";n.r(o),n.d(o,{components:function(){return x}});var e=n(448),t=n.n(e),r=n(5329),i=n.n(r);function u(e){var t,r,n="";if("string"==typeof e||"number"==typeof e)n+=e;else if("object"==typeof e)if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(r=u(e[t]))&&(n&&(n+=" "),n+=r);else for(t in e)e[t]&&(n&&(n+=" "),n+=t);return n}var s=function(){for(var e,t,r=0,n="";r<arguments.length;)(e=arguments[r++])&&(t=u(e))&&(n&&(n+=" "),n+=t);return n};const a="wixui-",c=(e,...t)=>{const r=[];return e&&r.push(`${a}${e}`),t.forEach((e=>{e&&(r.push(`${a}${e}`),r.push(e))})),r.join(" ")};const f=13,l=27;function d(e){return t=>{t.keyCode===e&&(t.preventDefault(),t.stopPropagation(),t.currentTarget.click())}}d(32),d(f),d(l);var p={root:"horizontal-line"};const v=e=>{const{id:n,children:o,className:i,customClassNames:u=[],onMouseEnter:a,onMouseLeave:f}=e;return r.createElement("div",t()({id:n,className:s(i,c(p.root,...u))},(e=>Object.entries(e).reduce(((e,[t,r])=>(t.includes("data-")&&(e[t]=r),e)),{}))(e),((e={})=>{const t=e.tabIndex??e.tabindex??void 0;return void 0!==t?{tabIndex:Number(t)}:{}})(e.a11y),{onMouseEnter:a,onMouseLeave:f}),o)};var b="aVng1S";const x={FiveGridLine_SolidLine:{component:e=>{let{children:r,...n}=e;return i().createElement(v,t()({},n,{className:s(n.className,b)}))}}}}(),o}()}));
//# sourceMappingURL=https://static.parastorage.com/services/editor-elements-library/dist/thunderbolt/rb_wixui.thunderbolt[FiveGridLine_SolidLine].b91bad1d.bundle.min.js.map