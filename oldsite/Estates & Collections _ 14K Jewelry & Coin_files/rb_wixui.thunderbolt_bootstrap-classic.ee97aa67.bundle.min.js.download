!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("react")):"function"==typeof define&&define.amd?define("rb_wixui.thunderbolt_bootstrap-classic",["react"],t):"object"==typeof exports?exports["rb_wixui.thunderbolt_bootstrap-classic"]=t(require("react")):e["rb_wixui.thunderbolt_bootstrap-classic"]=t(e.React)}("undefined"!=typeof self?self:this,(function(e){return function(){var t={96114:function(e,t,a){var n;!function(t){"use strict";var i=function(){},r=t.requestAnimationFrame||t.webkitRequestAnimationFrame||t.mozRequestAnimationFrame||t.msRequestAnimationFrame||function(e){return setTimeout(e,16)};function o(){var e=this;e.reads=[],e.writes=[],e.raf=r.bind(t),i("initialized",e)}function s(e){e.scheduled||(e.scheduled=!0,e.raf(c.bind(null,e)),i("flush scheduled"))}function c(e){i("flush");var t,a=e.writes,n=e.reads;try{i("flushing reads",n.length),e.runTasks(n),i("flushing writes",a.length),e.runTasks(a)}catch(e){t=e}if(e.scheduled=!1,(n.length||a.length)&&s(e),t){if(i("task errored",t.message),!e.catch)throw t;e.catch(t)}}function l(e,t){var a=e.indexOf(t);return!!~a&&!!e.splice(a,1)}o.prototype={constructor:o,runTasks:function(e){var t;for(i("run tasks");t=e.shift();)t()},measure:function(e,t){i("measure");var a=t?e.bind(t):e;return this.reads.push(a),s(this),a},mutate:function(e,t){i("mutate");var a=t?e.bind(t):e;return this.writes.push(a),s(this),a},clear:function(e){return i("clear",e),l(this.reads,e)||l(this.writes,e)},extend:function(e){if(i("extend",e),"object"!=typeof e)throw new Error("expected object");var t=Object.create(this);return function(e,t){for(var a in t)t.hasOwnProperty(a)&&(e[a]=t[a])}(t,e),t.fastdom=this,t.initialize&&t.initialize(),t},catch:null};var d=t.fastdom=t.fastdom||new o;void 0===(n=function(){return d}.call(d,a,d,e))||(e.exports=n)}("undefined"!=typeof window?window:void 0!==this?this:globalThis)},5329:function(t){"use strict";t.exports=e},448:function(e){function t(){return e.exports=t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)({}).hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,t.apply(null,arguments)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}},a={};function n(e){var i=a[e];if(void 0!==i)return i.exports;var r=a[e]={exports:{}};return t[e].call(r.exports,r,r.exports,n),r.exports}n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,{a:t}),t},n.d=function(e,t){for(var a in t)n.o(t,a)&&!n.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var i={};return function(){"use strict";n.r(i),n.d(i,{components:function(){return Zi}});var e={};n.r(e),n.d(e,{STATIC_MEDIA_URL:function(){return Vt},ph:function(){return Yt}});var t=n(448),a=n.n(t),r=n(5329),o=n.n(r);const s=13,c=27;function l(e){return t=>{t.keyCode===e&&(t.preventDefault(),t.stopPropagation(),t.currentTarget.click())}}const d=l(32),u=l(s),h=e=>{u(e),d(e)},m=(l(c),["aria-id","aria-metadata","aria-type"]),g=(e,t)=>Object.entries(e).reduce(((e,[a,n])=>(t.includes(a)||(e[a]=n),e)),{}),p=e=>{const{role:t,tabIndex:a,tabindex:n,screenReader:i,lang:r,ariaAttributes:o={}}=e,s=Object.entries(o).reduce(((e,[t,a])=>({...e,[`aria-${t}`.toLowerCase()]:a})),{});return{role:t,tabIndex:a??n,screenReader:i,ariaAttributes:g(s,m),lang:r}},f=({reportBiOnClick:e,onClick:t})=>(0,r.useCallback)((a=>{e?.(a),t?.(a)}),[e,t]),E=()=>"undefined"!=typeof window,I=e=>Object.entries(e).reduce(((e,[t,a])=>(t.includes("data-")&&(e[t]=a),e)),{});const v=(e,t)=>e?{"data-comp":t,"data-aid":t}:{},_={root:"linkElement"},b=(e,t)=>{const{href:n,role:i,target:o,rel:s,className:c="",children:l,linkPopupId:m,anchorDataId:g,anchorCompId:p,tabIndex:f,dataTestId:E=_.root,title:v,onClick:b,onDoubleClick:T,onMouseEnter:y,onMouseLeave:L,onFocus:w,onFocusCapture:A,onBlurCapture:C,"aria-live":M,"aria-disabled":k,"aria-label":N,"aria-labelledby":O,"aria-pressed":S,"aria-expanded":P,"aria-describedby":R,"aria-haspopup":x,"aria-current":F,dataPreview:G,dataPart:B}=e,H=void 0!==e.activateByKey?e.activateByKey:(e=>e?"SpaceOrEnter":"Space")(m);let D;switch(H){case"Enter":D=u;break;case"Space":D=d;break;case"SpaceOrEnter":D=h;break;default:D=void 0}return void 0!==n||m?r.createElement("a",a()({},I(e),{"data-testid":E,"data-popupid":m,"data-anchor":g,"data-anchor-comp-id":p,"data-preview":G,"data-part":B,href:n||void 0,target:o,role:m?"button":i,rel:s,className:c,onKeyDown:D,"aria-live":M,"aria-disabled":k,"aria-label":N,"aria-labelledby":O,"aria-pressed":S,"aria-expanded":P,"aria-haspopup":x,"aria-describedby":R,"aria-current":F,title:v,onClick:b,onMouseEnter:y,onMouseLeave:L,onDoubleClick:T,onFocus:w,onFocusCapture:A,onBlurCapture:C,ref:t,tabIndex:m?0:f}),l):r.createElement("div",a()({},I(e),{"data-testid":E,"data-preview":G,"data-part":B,className:c,tabIndex:f,"aria-label":N,"aria-labelledby":O,"aria-haspopup":x,"aria-disabled":k,"aria-expanded":P,title:v,role:i,onClick:b,onDoubleClick:T,onMouseEnter:y,onMouseLeave:L,ref:t}),l)};var T=r.forwardRef(b);const y="buttonElement",L="linkElement";let w=function(e){return e.Link="Link",e.Button="Button",e}({});const A=()=>{},C=(e,t)=>e===w.Link&&!(e=>Boolean(e&&(e.href||e.linkPopupId)))(t),M=(e,t)=>e===w.Link&&t,k=(e,t)=>{var a,n,i,o;const{id:s,className:c,customClassNames:l=[],autoFocus:d,label:h="",skin:m,hasPlatformClickHandler:g=!1,link:E,ariaLabel:_,isQaMode:b,fullNameCompType:T,reportBiOnClick:y,onFocus:L,onBlur:k,onClick:N=A,onDblClick:O=A,onMouseEnter:S=A,onMouseLeave:P=A,ariaAttributes:R,a11y:x={},lang:F,shouldFixKeyboardBehavior:G}=e;let{isDisabled:B=!1}=e;!0!==B&&(B=!1);const H=g?w.Button:w.Link,{tabIndex:D,ariaAttributes:U}=p({ariaAttributes:{...R,...x,disabled:null!=(a=x.disabled)?a:B,label:null!=(n=null!=(i=null!=(o=null==R?void 0:R.label)?o:x.label)?i:_)?n:h}}),Y=((e,t,a,n)=>a?-1:e===w.Button?n:C(e,t)?null!=n?n:0:n)(H,E,B,D),$=((e,t,a)=>C(e,t)||M(e,a)?"button":void 0)(H,E,B),z=E&&{href:B?void 0:E.href,target:E.target,rel:E.rel,linkPopupId:E.linkPopupId,anchorDataId:E.anchorDataId,anchorCompId:E.anchorCompId,activateByKey:"Enter"},V=f({reportBiOnClick:y,onClick:B?A:N}),j=G&&H===w.Link?{onKeyDown:u}:{};return r.createElement(m,{wrapperProps:{...I(e),className:c,id:s,role:$,tabIndex:Y,"aria-disabled":U["aria-disabled"],lang:F,onClick:V,onDoubleClick:B?A:O,onMouseEnter:S,onMouseLeave:P,...j,...v(b,T)},autoFocus:d,elementType:H,linkProps:z,a11yProps:U,label:h,onFocus:B?void 0:L,onBlur:B?void 0:k,ref:t,customClassNames:l})};var N=r.forwardRef(k);const O="wixui-",S=(e,...t)=>{const a=[];return e&&a.push(`${O}${e}`),t.forEach((e=>{e&&(a.push(`${O}${e}`),a.push(e))})),a.join(" ")};function P(e){var t,a,n="";if("string"==typeof e||"number"==typeof e)n+=e;else if("object"==typeof e)if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(a=P(e[t]))&&(n&&(n+=" "),n+=a);else for(t in e)e[t]&&(n&&(n+=" "),n+=t);return n}var R=function(){for(var e,t,a=0,n="";a<arguments.length;)(e=arguments[a++])&&(t=P(e))&&(n&&(n+=" "),n+=t);return n};var x={root:"button",buttonLabel:"button__label"},F="PlZyDq";const G=(e,t)=>{let{elementType:n,linkProps:i,a11yProps:r,className:s,children:c,autoFocus:l,onBlur:d,onFocus:u,disabled:m}=e;const g=o().useRef(null);o().useImperativeHandle(t,(()=>({focus:()=>{var e;return null==(e=g.current)?void 0:e.focus()},blur:()=>{var e;return null==(e=g.current)?void 0:e.blur()}})));const p=R(s,F);switch(n){case w.Link:return o().createElement(T,a()({},i||{},r,{className:p,ref:g,"data-testid":L,onFocusCapture:u,onBlurCapture:d}),c);case w.Button:return o().createElement("button",a()({},r,{ref:g,"data-testid":y,className:p,autoFocus:l,onFocus:u,onBlur:d,disabled:m,onKeyDown:h}),c);default:return null}};var B=o().forwardRef(G);const H=(e,t)=>{let{wrapperProps:n,linkProps:i,a11yProps:o,elementType:s,skinsStyle:c,label:l,autoFocus:d,customClassNames:u=[],onFocus:h,onBlur:m}=e;return r.createElement("div",a()({},n,{className:R(n.className,c.root)}),r.createElement(B,{disabled:!!o["aria-disabled"]||void 0,linkProps:i,a11yProps:o,elementType:s,className:R(c.link,S(x.root,...u)),autoFocus:d,onFocus:h,onBlur:m,ref:t},r.createElement("span",{className:R(c.label,S(x.buttonLabel))},l)))};var D=r.forwardRef(H),U={link:"uDW_Qe",root:"FubTgk",label:"l7_2fn"};const Y=r.forwardRef(((e,t)=>r.createElement(D,a()({},e,{skinsStyle:U,ref:t})))),$=(e,t)=>r.createElement(N,a()({},e,{skin:Y,ref:t}));var z=r.forwardRef($);const V=e=>e.replace(/([A-Z])/g,(e=>`-${e.toLowerCase()}`)),j=e=>({useComponentProps:(t,a,n)=>{const i=(e=>({...e,updateStyles:t=>{const a=Object.entries(t).reduce(((e,[t,a])=>{return{...e,[(n=t,n.startsWith("--")?t:V(t))]:void 0===a?null:a};var n}),{});e.updateStyles(a)}}))(n);return e({mapperProps:t,stateValues:a,controllerUtils:i})}}),W=e=>"linkPopupId"in e,q=(e,t)=>{if(W(e))return e.linkPopupId;{const{pagesMap:a,mainPageId:n}=t||{};if(!a)return;const i=new URL(e.href??"");let r=Object.values(a).find((({pageUriSEO:e})=>!!e&&i.pathname?.includes(e)));return r||(r=n?a[n]:void 0),r?.pageId}},Z=e=>{if(void 0!==e)return null===e?"None":e.type},K=(e,t)=>{if(!e?.type)return;const{type:a}=e;switch(a){case"AnchorLink":return e.anchorDataId;case"DocumentLink":return e.docInfo?.name;case"PageLink":const a=q(e,t);return a&&t?.pagesMap?.[a]?.title;default:return e.href}},J=(e,t,a)=>{const{link:n,value:i,details:r,actionName:o,elementType:s,trackClicksAnalytics:c,pagesMetadata:l,...d}=t;if(!c)return;const u=l&&{...l,pagesMap:window.viewerModel?.siteFeaturesConfigs?.router?.pagesMap},h=((e,t)=>{if(!e?.type)return;const{type:a}=e;switch(a){case"AnchorLink":return(e=>"anchorDataId"in e&&("SCROLL_TO_TOP"===e.anchorDataId||"SCROLL_TO_BOTTOM"===e.anchorDataId))(e)?void 0:{id:e.anchorDataId};case"DocumentLink":return{id:e.docInfo?.docId};case"PageLink":return{id:q(e,t),isLightbox:W(e)};default:return}})(n,u),m=r||h?JSON.stringify({...h,...r}):void 0;e({src:76,evid:1113,...{...d,bl:navigator.language,url:window.location.href,details:m,elementType:s??"Unknown",actionName:o??Z(n),value:i??K(n,u)}},{endpoint:"pa",...a})};var X;!function(e){e.Text="Text",e.Menu="Menu",e.Image="Image",e.Input="Input",e.Login="Login",e.Button="Button",e.Social="Social",e.Gallery="Gallery",e.Community="Community",e.Decorative="Decorative",e.MenuAndSearch="MenuAndSearch",e.MenuAndAnchor="MenuAndAnchor"}(X||(X={}));const Q=(e,t)=>!0===e[t]||"true"===e[t]||"new"===e[t];var ee=j((e=>{let{mapperProps:t,stateValues:a}=e;const{trackClicksAnalytics:n,compId:i,language:r,mainPageId:o,...s}=t,{experiments:c={}}=a,l=Q(c,"specs.thunderbolt.siteButtonKeyboardBehavior");return{...s,reportBiOnClick:e=>{const{fullNameCompType:t,label:c,link:l,isDisabled:d}=s,{reportBi:u}=a;J(u,{link:l,language:r,trackClicksAnalytics:n,elementTitle:c,elementType:t,pagesMetadata:{mainPageId:o},elementGroup:X.Button,details:{isDisabled:null!=d&&d},element_id:null!=i?i:e.currentTarget.id})},shouldFixKeyboardBehavior:l}})),te={link:"uUxqWY",root:"Vq4wYb",label:"wJVzSK"};const ae=r.forwardRef(((e,t)=>r.createElement(D,a()({},e,{skinsStyle:te,ref:t})))),ne=(e,t)=>r.createElement(N,a()({},e,{skin:ae,ref:t}));var ie=r.forwardRef(ne);function re(e,t,a){const n=o().useRef(null),i=o().useRef(null);return t?i.current||(i.current={play:()=>n.current?.play(),load:()=>n.current?.load(),pause:()=>n.current?.pause(),stop:()=>{n.current&&(n.current.pause(),n.current.currentTime=0,a&&a(n.current))}}):i.current=null,o().useImperativeHandle(e,(()=>i.current||{load(){},stop(){}})),n}var oe="jhxvbR";const se="v1",ce=2,le=1920,de=1920,ue=1e3,he=1e3,me={SCALE_TO_FILL:"fill",SCALE_TO_FIT:"fit",STRETCH:"stretch",ORIGINAL_SIZE:"original_size",TILE:"tile",TILE_HORIZONTAL:"tile_horizontal",TILE_VERTICAL:"tile_vertical",FIT_AND_TILE:"fit_and_tile",LEGACY_STRIP_TILE:"legacy_strip_tile",LEGACY_STRIP_TILE_HORIZONTAL:"legacy_strip_tile_horizontal",LEGACY_STRIP_TILE_VERTICAL:"legacy_strip_tile_vertical",LEGACY_STRIP_SCALE_TO_FILL:"legacy_strip_fill",LEGACY_STRIP_SCALE_TO_FIT:"legacy_strip_fit",LEGACY_STRIP_FIT_AND_TILE:"legacy_strip_fit_and_tile",LEGACY_STRIP_ORIGINAL_SIZE:"legacy_strip_original_size",LEGACY_ORIGINAL_SIZE:"actual_size",LEGACY_FIT_WIDTH:"fitWidth",LEGACY_FIT_HEIGHT:"fitHeight",LEGACY_FULL:"full",LEGACY_BG_FIT_AND_TILE:"legacy_tile",LEGACY_BG_FIT_AND_TILE_HORIZONTAL:"legacy_tile_horizontal",LEGACY_BG_FIT_AND_TILE_VERTICAL:"legacy_tile_vertical",LEGACY_BG_NORMAL:"legacy_normal"},ge={FIT:"fit",FILL:"fill",FILL_FOCAL:"fill_focal",CROP:"crop",LEGACY_CROP:"legacy_crop",LEGACY_FILL:"legacy_fill"},pe={CENTER:"center",TOP:"top",TOP_LEFT:"top_left",TOP_RIGHT:"top_right",BOTTOM:"bottom",BOTTOM_LEFT:"bottom_left",BOTTOM_RIGHT:"bottom_right",LEFT:"left",RIGHT:"right"},fe={[pe.CENTER]:{x:.5,y:.5},[pe.TOP_LEFT]:{x:0,y:0},[pe.TOP_RIGHT]:{x:1,y:0},[pe.TOP]:{x:.5,y:0},[pe.BOTTOM_LEFT]:{x:0,y:1},[pe.BOTTOM_RIGHT]:{x:1,y:1},[pe.BOTTOM]:{x:.5,y:1},[pe.RIGHT]:{x:1,y:.5},[pe.LEFT]:{x:0,y:.5}},Ee={center:"c",top:"t",top_left:"tl",top_right:"tr",bottom:"b",bottom_left:"bl",bottom_right:"br",left:"l",right:"r"},Ie={BG:"bg",IMG:"img",SVG:"svg"},ve={AUTO:"auto",CLASSIC:"classic",SUPER:"super"},_e={classic:1,super:2},be={radius:"0.66",amount:"1.00",threshold:"0.01"},Te={uri:"",css:{img:{},container:{}},attr:{img:{},container:{}},transformed:!1},ye=25e6,Le=[1.5,2,4],we={HIGH:{size:196e4,quality:90,maxUpscale:1},MEDIUM:{size:36e4,quality:85,maxUpscale:1},LOW:{size:16e4,quality:80,maxUpscale:1.2},TINY:{size:0,quality:80,maxUpscale:1.4}},Ae={HIGH:"HIGH",MEDIUM:"MEDIUM",LOW:"LOW",TINY:"TINY"},Ce={CONTRAST:"contrast",BRIGHTNESS:"brightness",SATURATION:"saturation",HUE:"hue",BLUR:"blur"},Me={JPG:"jpg",JPEG:"jpeg",JPE:"jpe",PNG:"png",WEBP:"webp",WIX_ICO_MP:"wix_ico_mp",WIX_MP:"wix_mp",GIF:"gif",SVG:"svg",AVIF:"avif",UNRECOGNIZED:"unrecognized"},ke={AVIF:"AVIF",PAVIF:"PAVIF"};Me.JPG,Me.JPEG,Me.JPE,Me.PNG,Me.GIF,Me.WEBP;function Ne(e,...t){return function(...a){const n=a[a.length-1]||{},i=[e[0]];return t.forEach((function(t,r){const o=Number.isInteger(t)?a[t]:n[t];i.push(o,e[r+1])})),i.join("")}}function Oe(e){return e[e.length-1]}const Se=[Me.PNG,Me.JPEG,Me.JPG,Me.JPE,Me.WIX_ICO_MP,Me.WIX_MP,Me.WEBP,Me.AVIF],Pe=[Me.JPEG,Me.JPG,Me.JPE];function Re(e,t,a){return a&&t&&!(!(n=t.id)||!n.trim()||"none"===n.toLowerCase())&&Object.values(me).includes(e);var n}function xe(e,t,a){return function(e,t,a=!1){return!((Ge(e)||He(e))&&t&&!a)}(e,t,a)&&(function(e){return Se.includes(ze(e))}(e)||function(e,t=!1){return Be(e)&&t}(e,a))&&!/(^https?)|(^data)|(^\/\/)/.test(e)}function Fe(e){return ze(e)===Me.PNG}function Ge(e){return ze(e)===Me.WEBP}function Be(e){return ze(e)===Me.GIF}function He(e){return ze(e)===Me.AVIF}const De=["/","\\","?","<",">","|","\u201c",":",'"'].map(encodeURIComponent),Ue=["\\.","\\*"],Ye="_";function $e(e){return function(e){return Pe.includes(ze(e))}(e)?Me.JPG:Fe(e)?Me.PNG:Ge(e)?Me.WEBP:Be(e)?Me.GIF:He(e)?Me.AVIF:Me.UNRECOGNIZED}function ze(e){return(/[.]([^.]+)$/.exec(e)&&/[.]([^.]+)$/.exec(e)[1]||"").toLowerCase()}function Ve(e,t,a,n,i){let r;return r=i===ge.FILL?function(e,t,a,n){return Math.max(a/e,n/t)}(e,t,a,n):i===ge.FIT?function(e,t,a,n){return Math.min(a/e,n/t)}(e,t,a,n):1,r}function je(e,t,a,n,i,r){e=e||n.width,t=t||n.height;const{scaleFactor:o,width:s,height:c}=function(e,t,a,n,i){let r,o=a,s=n;if(r=Ve(e,t,a,n,i),i===ge.FIT&&(o=e*r,s=t*r),o&&s&&o*s>ye){const a=Math.sqrt(ye/(o*s));o*=a,s*=a,r=Ve(e,t,o,s,i)}return{scaleFactor:r,width:o,height:s}}(e,t,n.width*i,n.height*i,a);return function(e,t,a,n,i,r,o){const{optimizedScaleFactor:s,upscaleMethodValue:c,forceUSM:l}=function(e,t,a,n){if("auto"===n)return function(e,t){const a=Ke(e,t);return{optimizedScaleFactor:we[a].maxUpscale,upscaleMethodValue:_e.classic,forceUSM:!1}}(e,t);if("super"===n)return function(e){return{optimizedScaleFactor:Oe(Le),upscaleMethodValue:_e.super,forceUSM:!(Le.includes(e)||e>Oe(Le))}}(a);return function(e,t){const a=Ke(e,t);return{optimizedScaleFactor:we[a].maxUpscale,upscaleMethodValue:_e.classic,forceUSM:!1}}(e,t)}(e,t,r,i);let d=a,u=n;if(r<=s)return{width:d,height:u,scaleFactor:r,upscaleMethodValue:c,forceUSM:l,cssUpscaleNeeded:!1};switch(o){case ge.FILL:d=a*(s/r),u=n*(s/r);break;case ge.FIT:d=e*s,u=t*s}return{width:d,height:u,scaleFactor:s,upscaleMethodValue:c,forceUSM:l,cssUpscaleNeeded:!0}}(e,t,s,c,r,o,a)}function We(e,t,a,n){const i=Ze(a)||function(e=pe.CENTER){return fe[e]}(n);return{x:Math.max(0,Math.min(e.width-t.width,i.x*e.width-t.width/2)),y:Math.max(0,Math.min(e.height-t.height,i.y*e.height-t.height/2)),width:Math.min(e.width,t.width),height:Math.min(e.height,t.height)}}function qe(e){return e.alignment&&Ee[e.alignment]||Ee[pe.CENTER]}function Ze(e){let t;return!e||"number"!=typeof e.x||isNaN(e.x)||"number"!=typeof e.y||isNaN(e.y)||(t={x:Je(Math.max(0,Math.min(100,e.x))/100,2),y:Je(Math.max(0,Math.min(100,e.y))/100,2)}),t}function Ke(e,t){const a=e*t;return a>we[Ae.HIGH].size?Ae.HIGH:a>we[Ae.MEDIUM].size?Ae.MEDIUM:a>we[Ae.LOW].size?Ae.LOW:Ae.TINY}function Je(e,t){const a=Math.pow(10,t||0);return(e*a/a).toFixed(t)}function Xe(e){return e&&e.upscaleMethod&&ve[e.upscaleMethod.toUpperCase()]||ve.AUTO}function Qe(e,t){const a=Ge(e)||He(e);return ze(e)===Me.GIF||a&&t}const et={isMobile:!1},tt=function(e){return et[e]};function at(){if("undefined"!=typeof window&&"undefined"!=typeof navigator){const t=window.matchMedia&&window.matchMedia("(max-width: 767px)").matches,a=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);e=t&&a,et["isMobile"]=e}var e}function nt(e,t){const a={css:{container:{}}},{css:n}=a,{fittingType:i}=e;switch(i){case me.ORIGINAL_SIZE:case me.LEGACY_ORIGINAL_SIZE:case me.LEGACY_STRIP_ORIGINAL_SIZE:n.container.backgroundSize="auto",n.container.backgroundRepeat="no-repeat";break;case me.SCALE_TO_FIT:case me.LEGACY_STRIP_SCALE_TO_FIT:n.container.backgroundSize="contain",n.container.backgroundRepeat="no-repeat";break;case me.STRETCH:n.container.backgroundSize="100% 100%",n.container.backgroundRepeat="no-repeat";break;case me.SCALE_TO_FILL:case me.LEGACY_STRIP_SCALE_TO_FILL:n.container.backgroundSize="cover",n.container.backgroundRepeat="no-repeat";break;case me.TILE_HORIZONTAL:case me.LEGACY_STRIP_TILE_HORIZONTAL:n.container.backgroundSize="auto",n.container.backgroundRepeat="repeat-x";break;case me.TILE_VERTICAL:case me.LEGACY_STRIP_TILE_VERTICAL:n.container.backgroundSize="auto",n.container.backgroundRepeat="repeat-y";break;case me.TILE:case me.LEGACY_STRIP_TILE:n.container.backgroundSize="auto",n.container.backgroundRepeat="repeat";break;case me.LEGACY_STRIP_FIT_AND_TILE:n.container.backgroundSize="contain",n.container.backgroundRepeat="repeat";break;case me.FIT_AND_TILE:case me.LEGACY_BG_FIT_AND_TILE:n.container.backgroundSize="auto",n.container.backgroundRepeat="repeat";break;case me.LEGACY_BG_FIT_AND_TILE_HORIZONTAL:n.container.backgroundSize="auto",n.container.backgroundRepeat="repeat-x";break;case me.LEGACY_BG_FIT_AND_TILE_VERTICAL:n.container.backgroundSize="auto",n.container.backgroundRepeat="repeat-y";break;case me.LEGACY_BG_NORMAL:n.container.backgroundSize="auto",n.container.backgroundRepeat="no-repeat"}switch(t.alignment){case pe.CENTER:n.container.backgroundPosition="center center";break;case pe.LEFT:n.container.backgroundPosition="left center";break;case pe.RIGHT:n.container.backgroundPosition="right center";break;case pe.TOP:n.container.backgroundPosition="center top";break;case pe.BOTTOM:n.container.backgroundPosition="center bottom";break;case pe.TOP_RIGHT:n.container.backgroundPosition="right top";break;case pe.TOP_LEFT:n.container.backgroundPosition="left top";break;case pe.BOTTOM_RIGHT:n.container.backgroundPosition="right bottom";break;case pe.BOTTOM_LEFT:n.container.backgroundPosition="left bottom"}return a}const it={[pe.CENTER]:"center",[pe.TOP]:"top",[pe.TOP_LEFT]:"top left",[pe.TOP_RIGHT]:"top right",[pe.BOTTOM]:"bottom",[pe.BOTTOM_LEFT]:"bottom left",[pe.BOTTOM_RIGHT]:"bottom right",[pe.LEFT]:"left",[pe.RIGHT]:"right"},rt={position:"absolute",top:"auto",right:"auto",bottom:"auto",left:"auto"};function ot(e,t){const a={css:{container:{},img:{}}},{css:n}=a,{fittingType:i}=e,r=t.alignment;switch(n.container.position="relative",i){case me.ORIGINAL_SIZE:case me.LEGACY_ORIGINAL_SIZE:e.parts&&e.parts.length?(n.img.width=e.parts[0].width,n.img.height=e.parts[0].height):(n.img.width=e.src.width,n.img.height=e.src.height);break;case me.SCALE_TO_FIT:case me.LEGACY_FIT_WIDTH:case me.LEGACY_FIT_HEIGHT:case me.LEGACY_FULL:n.img.width=t.width,n.img.height=t.height,n.img.objectFit="contain",n.img.objectPosition=it[r]||"unset";break;case me.LEGACY_BG_NORMAL:n.img.width="100%",n.img.height="100%",n.img.objectFit="none",n.img.objectPosition=it[r]||"unset";break;case me.STRETCH:n.img.width=t.width,n.img.height=t.height,n.img.objectFit="fill";break;case me.SCALE_TO_FILL:n.img.width=t.width,n.img.height=t.height,n.img.objectFit="cover"}if("number"==typeof n.img.width&&"number"==typeof n.img.height&&(n.img.width!==t.width||n.img.height!==t.height)){const e=Math.round((t.height-n.img.height)/2),a=Math.round((t.width-n.img.width)/2);Object.assign(n.img,rt,function(e,t,a){return{[pe.TOP_LEFT]:{top:0,left:0},[pe.TOP_RIGHT]:{top:0,right:0},[pe.TOP]:{top:0,left:t},[pe.BOTTOM_LEFT]:{bottom:0,left:0},[pe.BOTTOM_RIGHT]:{bottom:0,right:0},[pe.BOTTOM]:{bottom:0,left:t},[pe.RIGHT]:{top:e,right:0},[pe.LEFT]:{top:e,left:0},[pe.CENTER]:{width:a.width,height:a.height,objectFit:"none"}}}(e,a,t)[r])}return a}function st(e,t){const a={css:{container:{}},attr:{container:{},img:{}}},{css:n,attr:i}=a,{fittingType:r}=e,o=t.alignment,{width:s,height:c}=e.src;let l;switch(n.container.position="relative",r){case me.ORIGINAL_SIZE:case me.LEGACY_ORIGINAL_SIZE:case me.TILE:e.parts&&e.parts.length?(i.img.width=e.parts[0].width,i.img.height=e.parts[0].height):(i.img.width=s,i.img.height=c),i.img.preserveAspectRatio="xMidYMid slice";break;case me.SCALE_TO_FIT:case me.LEGACY_FIT_WIDTH:case me.LEGACY_FIT_HEIGHT:case me.LEGACY_FULL:i.img.width="100%",i.img.height="100%",i.img.transform="",i.img.preserveAspectRatio="";break;case me.STRETCH:i.img.width=t.width,i.img.height=t.height,i.img.x=0,i.img.y=0,i.img.transform="",i.img.preserveAspectRatio="none";break;case me.SCALE_TO_FILL:xe(e.src.id)?(i.img.width=t.width,i.img.height=t.height):(l=function(e,t,a,n,i){const r=Ve(e,t,a,n,i);return{width:Math.round(e*r),height:Math.round(t*r)}}(s,c,t.width,t.height,ge.FILL),i.img.width=l.width,i.img.height=l.height),i.img.x=0,i.img.y=0,i.img.transform="",i.img.preserveAspectRatio="xMidYMid slice"}if("number"==typeof i.img.width&&"number"==typeof i.img.height&&(i.img.width!==t.width||i.img.height!==t.height)){let e,a,n=0,s=0;r===me.TILE?(e=t.width%i.img.width,a=t.height%i.img.height):(e=t.width-i.img.width,a=t.height-i.img.height);const c=Math.round(e/2),l=Math.round(a/2);switch(o){case pe.TOP_LEFT:n=0,s=0;break;case pe.TOP:n=c,s=0;break;case pe.TOP_RIGHT:n=e,s=0;break;case pe.LEFT:n=0,s=l;break;case pe.CENTER:n=c,s=l;break;case pe.RIGHT:n=e,s=l;break;case pe.BOTTOM_LEFT:n=0,s=a;break;case pe.BOTTOM:n=c,s=a;break;case pe.BOTTOM_RIGHT:n=e,s=a}i.img.x=n,i.img.y=s}return i.container.width=t.width,i.container.height=t.height,i.container.viewBox=[0,0,t.width,t.height].join(" "),a}function ct(e,t,a){let n;switch(t.crop&&(n=function(e,t){const a=Math.max(0,Math.min(e.width,t.x+t.width)-Math.max(0,t.x)),n=Math.max(0,Math.min(e.height,t.y+t.height)-Math.max(0,t.y));return a&&n&&(e.width!==a||e.height!==n)?{x:Math.max(0,t.x),y:Math.max(0,t.y),width:a,height:n}:null}(t,t.crop),n&&(e.src.width=n.width,e.src.height=n.height,e.src.isCropped=!0,e.parts.push(dt(n)))),e.fittingType){case me.SCALE_TO_FIT:case me.LEGACY_FIT_WIDTH:case me.LEGACY_FIT_HEIGHT:case me.LEGACY_FULL:case me.FIT_AND_TILE:case me.LEGACY_BG_FIT_AND_TILE:case me.LEGACY_BG_FIT_AND_TILE_HORIZONTAL:case me.LEGACY_BG_FIT_AND_TILE_VERTICAL:case me.LEGACY_BG_NORMAL:e.parts.push(lt(e,a));break;case me.SCALE_TO_FILL:e.parts.push(function(e,t){const a=je(e.src.width,e.src.height,ge.FILL,t,e.devicePixelRatio,e.upscaleMethod),n=Ze(e.focalPoint);return{transformType:n?ge.FILL_FOCAL:ge.FILL,width:Math.round(a.width),height:Math.round(a.height),alignment:qe(t),focalPointX:n&&n.x,focalPointY:n&&n.y,upscale:a.scaleFactor>1,forceUSM:a.forceUSM,scaleFactor:a.scaleFactor,cssUpscaleNeeded:a.cssUpscaleNeeded,upscaleMethodValue:a.upscaleMethodValue}}(e,a));break;case me.STRETCH:e.parts.push(function(e,t){const a=Ve(e.src.width,e.src.height,t.width,t.height,ge.FILL),n={...t};return n.width=e.src.width*a,n.height=e.src.height*a,lt(e,n)}(e,a));break;case me.TILE_HORIZONTAL:case me.TILE_VERTICAL:case me.TILE:case me.LEGACY_ORIGINAL_SIZE:case me.ORIGINAL_SIZE:n=We(e.src,a,e.focalPoint,a.alignment),e.src.isCropped?(Object.assign(e.parts[0],n),e.src.width=n.width,e.src.height=n.height):e.parts.push(dt(n));break;case me.LEGACY_STRIP_TILE_HORIZONTAL:case me.LEGACY_STRIP_TILE_VERTICAL:case me.LEGACY_STRIP_TILE:case me.LEGACY_STRIP_ORIGINAL_SIZE:e.parts.push(function(e){return{transformType:ge.LEGACY_CROP,width:Math.round(e.width),height:Math.round(e.height),alignment:qe(e),upscale:!1,forceUSM:!1,scaleFactor:1,cssUpscaleNeeded:!1}}(a));break;case me.LEGACY_STRIP_SCALE_TO_FIT:case me.LEGACY_STRIP_FIT_AND_TILE:e.parts.push(function(e){return{transformType:ge.FIT,width:Math.round(e.width),height:Math.round(e.height),upscale:!1,forceUSM:!0,scaleFactor:1,cssUpscaleNeeded:!1}}(a));break;case me.LEGACY_STRIP_SCALE_TO_FILL:e.parts.push(function(e){return{transformType:ge.LEGACY_FILL,width:Math.round(e.width),height:Math.round(e.height),alignment:qe(e),upscale:!1,forceUSM:!0,scaleFactor:1,cssUpscaleNeeded:!1}}(a))}}function lt(e,t){const a=je(e.src.width,e.src.height,ge.FIT,t,e.devicePixelRatio,e.upscaleMethod);return{transformType:!e.src.width||!e.src.height?ge.FIT:ge.FILL,width:Math.round(a.width),height:Math.round(a.height),alignment:Ee.center,upscale:a.scaleFactor>1,forceUSM:a.forceUSM,scaleFactor:a.scaleFactor,cssUpscaleNeeded:a.cssUpscaleNeeded,upscaleMethodValue:a.upscaleMethodValue}}function dt(e){return{transformType:ge.CROP,x:Math.round(e.x),y:Math.round(e.y),width:Math.round(e.width),height:Math.round(e.height),upscale:!1,forceUSM:!1,scaleFactor:1,cssUpscaleNeeded:!1}}function ut(e,t){t=t||{},e.quality=function(e,t){const a=e.fileType===Me.PNG,n=e.fileType===Me.JPG,i=e.fileType===Me.WEBP,r=e.fileType===Me.AVIF,o=n||a||i||r;if(o){const n=Oe(e.parts),i=(s=n.width,c=n.height,we[Ke(s,c)].quality);let r=t.quality&&t.quality>=5&&t.quality<=90?t.quality:i;return r=a?r+5:r,r}var s,c;return 0}(e,t),e.progressive=function(e){return!1!==e.progressive}(t),e.watermark=function(e){return e.watermark}(t),e.autoEncode=t.autoEncode??!0,e.encoding=t?.encoding,e.unsharpMask=function(e,t){if(function(e){const t="number"==typeof(e=e||{}).radius&&!isNaN(e.radius)&&e.radius>=.1&&e.radius<=500,a="number"==typeof e.amount&&!isNaN(e.amount)&&e.amount>=0&&e.amount<=10,n="number"==typeof e.threshold&&!isNaN(e.threshold)&&e.threshold>=0&&e.threshold<=255;return t&&a&&n}(t.unsharpMask))return{radius:Je(t.unsharpMask?.radius,2),amount:Je(t.unsharpMask?.amount,2),threshold:Je(t.unsharpMask?.threshold,2)};if(("number"!=typeof(a=(a=t.unsharpMask)||{}).radius||isNaN(a.radius)||0!==a.radius||"number"!=typeof a.amount||isNaN(a.amount)||0!==a.amount||"number"!=typeof a.threshold||isNaN(a.threshold)||0!==a.threshold)&&function(e){const t=Oe(e.parts);return!(t.scaleFactor>=1)||t.forceUSM||t.transformType===ge.FIT}(e))return be;var a;return}(e,t),e.filters=function(e){const t=e.filters||{},a={};ht(t[Ce.CONTRAST],-100,100)&&(a[Ce.CONTRAST]=t[Ce.CONTRAST]);ht(t[Ce.BRIGHTNESS],-100,100)&&(a[Ce.BRIGHTNESS]=t[Ce.BRIGHTNESS]);ht(t[Ce.SATURATION],-100,100)&&(a[Ce.SATURATION]=t[Ce.SATURATION]);ht(t[Ce.HUE],-180,180)&&(a[Ce.HUE]=t[Ce.HUE]);ht(t[Ce.BLUR],0,100)&&(a[Ce.BLUR]=t[Ce.BLUR]);return a}(t)}function ht(e,t,a){return"number"==typeof e&&!isNaN(e)&&0!==e&&e>=t&&e<=a}function mt(e,t,a,n){const i=function(e){return e?.isSEOBot??!1}(n),r=$e(t.id),o=function(e,t){const a=/\.([^.]*)$/,n=new RegExp(`(${De.concat(Ue).join("|")})`,"g");if(t&&t.length){let e=t;const i=t.match(a);return i&&Se.includes(i[1])&&(e=t.replace(a,"")),encodeURIComponent(e).replace(n,Ye)}const i=e.match(/\/(.*?)$/);return(i?i[1]:e).replace(a,"")}(t.id,t.name),s=i?1:function(e){return Math.min(e.pixelAspectRatio||1,ce)}(a),c=ze(t.id),l=c,d=xe(t.id,n?.hasAnimation,n?.allowAnimatedTransform),u={fileName:o,fileExtension:c,fileType:r,fittingType:e,preferredExtension:l,src:{id:t.id,width:t.width,height:t.height,isCropped:!1,isAnimated:Qe(t.id,n?.hasAnimation)},focalPoint:{x:t.focalPoint&&t.focalPoint.x,y:t.focalPoint&&t.focalPoint.y},parts:[],devicePixelRatio:s,quality:0,upscaleMethod:Xe(n),progressive:!0,watermark:"",unsharpMask:{},filters:{},transformed:d};return d&&(ct(u,t,a),ut(u,n)),u}function gt(e,t,a){const n={...a},i=tt("isMobile");switch(e){case me.LEGACY_BG_FIT_AND_TILE:case me.LEGACY_BG_FIT_AND_TILE_HORIZONTAL:case me.LEGACY_BG_FIT_AND_TILE_VERTICAL:case me.LEGACY_BG_NORMAL:const e=i?ue:le,a=i?he:de;n.width=Math.min(e,t.width),n.height=Math.min(a,Math.round(n.width/(t.width/t.height))),n.pixelAspectRatio=1}return n}const pt=Ne`fit/w_${"width"},h_${"height"}`,ft=Ne`fill/w_${"width"},h_${"height"},al_${"alignment"}`,Et=Ne`fill/w_${"width"},h_${"height"},fp_${"focalPointX"}_${"focalPointY"}`,It=Ne`crop/x_${"x"},y_${"y"},w_${"width"},h_${"height"}`,vt=Ne`crop/w_${"width"},h_${"height"},al_${"alignment"}`,_t=Ne`fill/w_${"width"},h_${"height"},al_${"alignment"}`,bt=Ne`,lg_${"upscaleMethodValue"}`,Tt=Ne`,q_${"quality"}`,yt=Ne`,quality_auto`,Lt=Ne`,usm_${"radius"}_${"amount"}_${"threshold"}`,wt=Ne`,bl`,At=Ne`,wm_${"watermark"}`,Ct={[Ce.CONTRAST]:Ne`,con_${"contrast"}`,[Ce.BRIGHTNESS]:Ne`,br_${"brightness"}`,[Ce.SATURATION]:Ne`,sat_${"saturation"}`,[Ce.HUE]:Ne`,hue_${"hue"}`,[Ce.BLUR]:Ne`,blur_${"blur"}`},Mt=Ne`,enc_auto`,kt=Ne`,enc_avif`,Nt=Ne`,enc_pavif`,Ot=Ne`,pstr`;function St(e,t,a,n={},i){if(xe(t.id,n?.hasAnimation,n?.allowAnimatedTransform)){if(Ge(t.id)||He(t.id)){const{alignment:r,...o}=a;t.focalPoint={x:void 0,y:void 0},delete t?.crop,i=mt(e,t,o,n)}else i=i||mt(e,t,a,n);return function(e){const t=[];e.parts.forEach((e=>{switch(e.transformType){case ge.CROP:t.push(It(e));break;case ge.LEGACY_CROP:t.push(vt(e));break;case ge.LEGACY_FILL:let a=_t(e);e.upscale&&(a+=bt(e)),t.push(a);break;case ge.FIT:let n=pt(e);e.upscale&&(n+=bt(e)),t.push(n);break;case ge.FILL:let i=ft(e);e.upscale&&(i+=bt(e)),t.push(i);break;case ge.FILL_FOCAL:let r=Et(e);e.upscale&&(r+=bt(e)),t.push(r)}}));let a=t.join("/");return e.quality&&(a+=Tt(e)),e.unsharpMask&&(a+=Lt(e.unsharpMask)),e.progressive||(a+=wt(e)),e.watermark&&(a+=At(e)),e.filters&&(a+=Object.keys(e.filters).map((t=>Ct[t](e.filters))).join("")),e.fileType!==Me.GIF&&(e.encoding===ke.AVIF?(a+=kt(e),a+=yt(e)):e.encoding===ke.PAVIF?(a+=Nt(e),a+=yt(e)):e.autoEncode&&(a+=Mt(e))),e.src?.isAnimated&&e.transformed&&(a+=Ot(e)),`${e.src.id}/${se}/${a}/${e.fileName}.${e.preferredExtension}`}(i)}return t.id}const Pt={[pe.CENTER]:"50% 50%",[pe.TOP_LEFT]:"0% 0%",[pe.TOP_RIGHT]:"100% 0%",[pe.TOP]:"50% 0%",[pe.BOTTOM_LEFT]:"0% 100%",[pe.BOTTOM_RIGHT]:"100% 100%",[pe.BOTTOM]:"50% 100%",[pe.RIGHT]:"100% 50%",[pe.LEFT]:"0% 50%"},Rt=Object.entries(Pt).reduce(((e,[t,a])=>(e[a]=t,e)),{}),xt=[me.TILE,me.TILE_HORIZONTAL,me.TILE_VERTICAL,me.LEGACY_BG_FIT_AND_TILE,me.LEGACY_BG_FIT_AND_TILE_HORIZONTAL,me.LEGACY_BG_FIT_AND_TILE_VERTICAL],Ft=[me.LEGACY_ORIGINAL_SIZE,me.ORIGINAL_SIZE,me.LEGACY_BG_NORMAL];function Gt(e,t,{width:a,height:n}){return e===me.TILE&&t.width>a&&t.height>n}function Bt(e,{width:t,height:a}){if(!t||!a){const n=t||Math.min(980,e.width),i=n/e.width;return{width:n,height:a||e.height*i}}return{width:t,height:a}}function Ht(e,t,a,n="center"){const i={img:{},container:{}};if(e===me.SCALE_TO_FILL){const e=t.focalPoint&&function(e){const t=`${e.x}% ${e.y}%`;return Rt[t]||""}(t.focalPoint),r=e||n;t.focalPoint&&!e?i.img={objectPosition:Dt(t,a,t.focalPoint)}:i.img={objectPosition:Pt[r]}}else[me.LEGACY_ORIGINAL_SIZE,me.ORIGINAL_SIZE].includes(e)?i.img={objectFit:"none",top:"auto",left:"auto",right:"auto",bottom:"auto"}:xt.includes(e)&&(i.container={backgroundSize:`${t.width}px ${t.height}px`});return i}function Dt(e,t,a){const{width:n,height:i}=e,{width:r,height:o}=t,{x:s,y:c}=a;if(!r||!o)return`${s}% ${c}%`;const l=Math.max(r/n,o/i),d=n*l,u=i*l,h=Math.max(0,Math.min(d-r,d*(s/100)-r/2)),m=Math.max(0,Math.min(u-o,u*(c/100)-o/2));return`${h&&Math.floor(h/(d-r)*100)}% ${m&&Math.floor(m/(u-o)*100)}%`}const Ut={width:"100%",height:"100%"};function Yt(e,t,a,n={}){const{autoEncode:i=!0,isSEOBot:r,shouldLoadHQImage:o,hasAnimation:s,allowAnimatedTransform:c,encoding:l}=n;if(!Re(e,t,a))return Te;const d=void 0===c||c,u=xe(t.id,s,d);if(!u||o)return $t(e,t,a,{...n,autoEncode:i,useSrcset:u});const h={...a,...Bt(t,a)},{alignment:m,htmlTag:g}=h,p=Gt(e,t,h),f=function(e,t,{width:a,height:n},i=!1){if(i)return{width:a,height:n};const r=!Ft.includes(e),o=Gt(e,t,{width:a,height:n}),s=!o&&xt.includes(e),c=s?t.width:a,l=s?t.height:n,d=r?function(e,t){return e>900?t?.05:.15:e>500?t?.1:.18:e>200?.25:1}(c,Fe(t.id)):1;return{width:o?1920:c*d,height:l*d}}(e,t,h,r),E=function(e,t,a){return a?0:xt.includes(t)?1:e>200?2:3}(h.width,e,r),I=function(e,t){const a=xt.includes(e)&&!t;return e===me.SCALE_TO_FILL||a?me.SCALE_TO_FIT:e}(e,p),v=Ht(e,t,a,m),{uri:_}=$t(I,t,{...f,alignment:m,htmlTag:g},{autoEncode:i,filters:E?{blur:E}:{},hasAnimation:s,allowAnimatedTransform:d,encoding:l}),{attr:b={},css:T}=$t(e,t,{...h,alignment:m,htmlTag:g},{});return T.img=T.img||{},T.container=T.container||{},Object.assign(T.img,v.img,Ut),Object.assign(T.container,v.container),{uri:_,css:T,attr:b,transformed:!0}}function $t(e,t,a,n){let i={};if(Re(e,t,a)){const r=gt(e,t,a),o=mt(e,t,r,n);i.uri=St(e,t,r,n,o),n?.useSrcset&&(i.srcset=function(e,t,a,n,i){const r=a.pixelAspectRatio||1;return{dpr:[`${1===r?i.uri:St(e,t,{...a,pixelAspectRatio:1},n)} 1x`,`${2===r?i.uri:St(e,t,{...a,pixelAspectRatio:2},n)} 2x`]}}(e,t,r,n,i)),Object.assign(i,function(e,t){let a;return a=t.htmlTag===Ie.BG?nt:t.htmlTag===Ie.SVG?st:ot,a(e,t)}(o,r),{transformed:o.transformed})}else i=Te;return i}const zt="https://static.wixstatic.com/media/";"undefined"!=typeof window&&window.devicePixelRatio;at();at();const Vt=zt,{STATIC_MEDIA_URL:jt}=e,Wt=({fittingType:e,src:t,target:a,options:n})=>{const i=Yt(e,t,a,{...n,autoEncode:!0});return i?.uri&&!/^[a-z]+:/.test(i.uri)&&(i.uri=`${jt}${i.uri}`),i},qt=/^[a-z]+:/,Zt=e=>{const{id:t,containerId:a,uri:n,alt:i,name:o="",role:s,width:c,height:l,displayMode:d,devicePixelRatio:u,quality:h,alignType:m,bgEffectName:g="",focalPoint:p,upscaleMethod:f,className:E="",crop:I,imageStyles:v={},targetWidth:_,targetHeight:b,targetScale:T,onLoad:y=()=>{},onError:L=()=>{},shouldUseLQIP:w,containerWidth:A,containerHeight:C,getPlaceholder:M,isInFirstFold:k,placeholderTransition:N,socialAttrs:O,isSEOBot:S,skipMeasure:P,hasAnimation:R,encoding:x}=e,F=r.useRef(null);let G="";const B="blur"===N,H=r.useRef(null);if(!H.current)if(M||w||k||S){const e={upscaleMethod:f,...h||{},shouldLoadHQImage:k,isSEOBot:S,hasAnimation:R,encoding:x};H.current=(M||Wt)({fittingType:d,src:{id:n,width:c,height:l,crop:I,name:o,focalPoint:p},target:{width:A,height:C,alignment:m,htmlTag:"img"},options:e}),G=!H.current.transformed||k||S?"":"true"}else H.current={uri:void 0,css:{img:{}},attr:{img:{},container:{}},transformed:!1};const D=!S&&(M||w)&&!k&&H.current.transformed,U=r.useMemo((()=>JSON.stringify({containerId:a,...a&&{containerId:a},...m&&{alignType:m},...P&&{skipMeasure:!0},displayMode:d,...A&&{targetWidth:A},...C&&{targetHeight:C},..._&&{targetWidth:_},...b&&{targetHeight:b},...T&&{targetScale:T},isLQIP:D,isSEOBot:S,lqipTransition:N,encoding:x,imageData:{width:c,height:l,uri:n,name:o,displayMode:d,hasAnimation:R,...h&&{quality:h},...u&&{devicePixelRatio:u},...p&&{focalPoint:p},...I&&{crop:I},...f&&{upscaleMethod:f}}})),[a,m,P,d,A,C,_,b,T,D,S,N,x,c,l,n,o,R,h,u,p,I,f]),Y=H.current,$=Y?.uri,z=Y?.srcset,V=Y.css?.img,j=`${oe} ${E}`;r.useEffect((()=>{const e=F.current;y&&e?.currentSrc&&e?.complete&&y({target:e})}),[]);const W=Y&&!Y?.transformed?`max(${c}px, 100%)`:_?`${_}px`:null;return r.createElement("wow-image",{id:t,class:j,"data-image-info":U,"data-motion-part":`BG_IMG ${a}`,"data-bg-effect-name":g,"data-has-ssr-src":G,"data-animate-blur":!S&&D&&B?"":void 0,style:W?{"--wix-img-max-width":W}:{}},r.createElement("img",{src:$,ref:F,alt:i||"",role:s,style:{...V,...v},onLoad:y,onError:L,width:A||void 0,height:C||void 0,...O,srcSet:k?z?.dpr?.map((e=>qt.test(e)?e:`${jt}${e}`)).join(", "):void 0,fetchpriority:k?"high":void 0,loading:!1===k?"lazy":void 0,suppressHydrationWarning:!0}))};var Kt="mtrorN";var Jt=e=>{const{id:t,alt:a,role:n,className:i,imageStyles:o={},targetWidth:s,targetHeight:c,onLoad:l,onError:d,containerWidth:u,containerHeight:h,isInFirstFold:m,socialAttrs:g,skipMeasure:p,responsiveImageProps:f,zoomedImageResponsiveOverride:E,displayMode:I}=e,v=s||u,_=c||h,{fallbackSrc:b,srcset:T,sources:y,css:L}=f||{},{width:w,height:A,...C}=f?.css?.img||{},M="original_size"===I?f?.css?.img:C;return b&&T&&L?r.createElement("img",{fetchpriority:m?"high":void 0,loading:!1===m?"lazy":void 0,sizes:`${v}px`,srcSet:p?E?.srcset:f?.srcset,id:t,src:b,alt:a||"",role:n,style:{...o,...p?{...E?.css?.img}:{...M}},onLoad:l,onError:d,className:R(i,Kt),width:v,height:_,...g}):b&&y&&L?r.createElement("picture",null,y.map((({srcset:e,media:t,sizes:a})=>r.createElement("source",{key:t,srcSet:e,media:t,sizes:a}))),r.createElement("img",{fetchpriority:m?"high":void 0,loading:!1===m?"lazy":void 0,id:t,src:y[0].fallbackSrc,alt:a||"",role:n,style:{...o,objectFit:y[0].imgStyle.objectFit,objectPosition:y[0].imgStyle.objectPosition},onLoad:l,onError:d,className:R(i,Kt),width:v,height:_,...g})):r.createElement(Zt,{...e})};var Xt=e=>{const{className:t,customIdPrefix:a,getPlaceholder:n,hasAnimation:i,...o}=e,s=r.useMemo((()=>JSON.stringify({containerId:o.containerId,alignType:o.alignType,fittingType:o.displayMode,hasAnimation:i,imageData:{width:o.width,height:o.height,uri:o.uri,name:o.name,...o.quality&&{quality:o.quality},displayMode:o.displayMode}})),[o,i]),c=r.useRef(null);c.current||(c.current=n?n({fittingType:o.displayMode,src:{id:o.uri,width:o.width,height:o.height,name:o.name},target:{width:o.containerWidth,height:o.containerHeight,alignment:o.alignType,htmlTag:"bg"},options:{hasAnimation:i,allowAnimatedTransform:!1}}):{uri:void 0,css:{img:{}},attr:{img:{},container:{}}});const l=c.current,d=l?.uri??"",u=l.css?.container??{},h=Object.assign(d?{backgroundImage:`url(${d})`}:{},u);return r.createElement("wix-bg-image",{id:`${a||"bgImg_"}${o.containerId}`,class:t,style:h,"data-tiled-image-info":s,"data-has-bg-scroll-effect":o.hasBgScrollEffect||"","data-bg-effect-name":o.bgEffectName||"","data-motion-part":`BG_IMG ${o.containerId}`})};const Qt=new RegExp("<%= compId %>","g"),ea=(e,t)=>e.replace(Qt,t);var ta=e=>e?.replace(":hover",""),aa="_uqPqy",na="eKyYhK",ia="x0mqQS",ra="pnCr6P",oa="blf7sp";const sa={quality:{unsharpMask:{radius:.33,amount:1,threshold:0}},devicePixelRatio:1};var ca=e=>{const{id:t,videoRef:a,videoInfo:n,posterImageInfo:i,muted:o,preload:s,loop:c,alt:l,isVideoEnabled:d,getPlaceholder:u,extraClassName:h=""}=e;n.containerId=ta(n.containerId);const m=r.useMemo((()=>JSON.stringify(n)),[n]),g=r.createElement(r.Fragment,null,i.filterEffectSvgString&&r.createElement("svg",{id:`svg_${n.containerId}`,className:oa},r.createElement("defs",{dangerouslySetInnerHTML:{__html:ea(i.filterEffectSvgString,n.containerId)}})),r.createElement(Jt,{key:`${n.videoId}_img`,id:`${i.containerId}_img`,className:R(na,ia,"bgVideoposter",h),imageStyles:{width:"100%",height:"100%"},...i,...sa,getPlaceholder:u}));return d?r.createElement("wix-video",{id:t,"data-video-info":m,"data-motion-part":`BG_IMG ${n.containerId}`,class:R(aa,"bgVideo",h)},r.createElement("video",{key:`${n.videoId}_video`,ref:a,id:`${n.containerId}_video`,className:ra,crossOrigin:"anonymous","aria-label":l,playsInline:!0,preload:s,muted:o,loop:c}),g):g},la="rWP3Gv";var da=e=>{const{id:t,containerId:a,pageId:n,children:i,bgEffectName:o="",containerSize:s}=e;return r.createElement("wix-bg-media",{id:t,class:la,"data-container-id":a,"data-container-size":`${s?.width||0}, ${s?.height||0}`,"data-page-id":n,"data-bg-effect-name":o,"data-motion-part":`BG_MEDIA ${a}`},i)};const ua="bgOverlay";var ha="Tr4n3d",ma="wRqk6s";var ga=e=>{const{imageOverlay:t}=e;return r.createElement("div",{"data-testid":ua,className:ha},t&&r.createElement(Xt,{customIdPrefix:"bgImgOverlay_",className:ma,...t}))};const pa="bgLayers",fa="colorUnderlay",Ea="mediaPadding",Ia="canvas";var va="if7Vw2",_a="f0uTJH",ba="i1tH8h",Ta="DXi4PB",ya="wG8dni",La="tcElKx",wa="Ybjs9b",Aa="ImALHf",Ca="UWmm3w",Ma="Yjj1af",ka="KCM6zk";const Na="bgImage";var Oa=e=>{const{videoRef:t,canvasRef:a,hasBgFullscreenScrollEffect:n,image:i,backgroundImage:o,backgroundMedia:s,video:c,backgroundOverlay:l,shouldPadMedia:d,extraClass:u="",shouldRenderUnderlay:h=!c,reducedMotion:m=!1,getPlaceholder:g,hasCanvasAnimation:p,useWixMediaCanvas:f,onClick:E}=e,{onImageLoad:I}=(({onReady:e,image:t})=>((0,r.useEffect)((()=>{e&&!t&&e()}),[e,t]),{onImageLoad:a=>{t?.onLoad&&t.onLoad(a),e&&e()}}))(e),v=ta(e.containerId),_=`img_${ta(v)}`,b=i&&r.createElement(Jt,{id:_,className:R(ba,Ta,Ma,Na),imageStyles:{width:"100%",height:"100%"},getPlaceholder:g,...i,onLoad:I}),T=o&&r.createElement(Xt,{...o,containerId:v,className:R(ba,Ta,Ma,Na),getPlaceholder:g}),y=c&&r.createElement(ca,{id:`videoContainer_${v}`,...c,extraClassName:wa,reducedMotion:m,videoRef:t,getPlaceholder:g}),L=f&&a||p?r.createElement("wix-media-canvas",{"data-container-id":v,class:p?ka:""},b,T,y,r.createElement("canvas",{id:`${v}webglcanvas`,className:R(Aa,"webglcanvas"),"aria-label":c?.alt||"",role:"presentation","data-testid":Ia})):r.createElement(r.Fragment,null,b,T,y,a&&r.createElement("canvas",{id:`${v}webglcanvas`,ref:a,className:R(Aa,"webglcanvas"),"aria-label":c?.alt||"",role:"presentation","data-testid":Ia})),w=s?r.createElement(da,{id:`bgMedia_${v}`,...s},L):r.createElement("div",{id:`bgMedia_${v}`,"data-motion-part":`BG_MEDIA ${v}`,className:ya},L),A=l&&r.createElement(ga,{...l});return r.createElement("div",{id:`${pa}_${v}`,"data-hook":pa,"data-motion-part":`BG_LAYER ${v}`,className:R(va,u,{[_a]:n}),onClick:E},h&&r.createElement("div",{"data-testid":fa,className:R(La,ba)}),d?r.createElement("div",{"data-testid":Ea,className:Ca},w,A):r.createElement(r.Fragment,null,w,A))},Sa="VXAmO2",Pa="dy3w_9",Ra="UORcXs",xa="Io4VUz";const Fa=(e,t,a)=>{const n=((e,t)=>e?[...Array(1+(t||0)).keys()].reverse().map((e=>r.createElement("div",{key:`divider-layer-${e}`,style:{"--divider-layer-i":e},className:Pa,"data-testid":`divider-layer-${e}`,"data-divider-layer":e}))):null)(!!t,a);return t?r.createElement("div",{className:R(Sa,{[Ra]:"top"===e,[xa]:"bottom"===e}),"data-testid":`${e}-divider`},n):null};var Ga=e=>{const t=r.useMemo((()=>Fa("top",e?.hasTopDivider,e?.topLayers?.size)),[e?.hasTopDivider,e?.topLayers?.size]),a=r.useMemo((()=>Fa("bottom",e?.hasBottomDivider,e?.bottomLayers?.size)),[e?.hasBottomDivider,e?.bottomLayers?.size]);return r.createElement(r.Fragment,null,t,a)};const Ba="columns";var Ha={root:"column-strip",column:"column-strip__column"},Da="CohWsy",Ua="V5AUxf",Ya="LIhNy3";const $a=(e,t)=>{const{id:n,className:i,customClassNames:o=[],fillLayers:s,children:c,onMouseEnter:l,onMouseLeave:d,onClick:u,onDblClick:h,getPlaceholder:m,a11y:g={},onStop:p,dividers:f,onReady:E}=e,v={onMouseEnter:l,onMouseLeave:d,onClick:u,onDoubleClick:h},_=s.hasBgFullscreenScrollEffect,b=re(t,!!s.video,p),{tabindex:T,...y}=g;return r.createElement("section",a()({id:n},I(e),v,y,((e={})=>{const t=e.tabIndex??e.tabindex??void 0;return void 0!==t?{tabIndex:Number(t)}:{}})(g),{className:R(i,Da,S(Ha.root,...o))}),r.createElement(Oa,a()({},s,{getPlaceholder:m,onReady:E,videoRef:b})),f&&r.createElement(Ga,f),r.createElement("div",{"data-testid":Ba,className:R(Ua,{[Ya]:_})},c()))};var za=r.forwardRef($a);var Va={root:"repeater",repeaterItem:"repeater__item"};const ja="mesh-container-content",Wa="inline-content",qa=e=>o().createElement("div",{key:e.props.id+"-rotated-wrapper","data-mesh-id":e.props.id+"-rotated-wrapper"},e),Za=(e,t)=>{const{id:n,className:i,wedges:r=[],rotatedComponents:s=[],children:c,fixedComponents:l=[],extraClassName:d="",renderRotatedComponents:u=qa}=e,h=o().Children.toArray(c()),m=[],g=[];h.forEach((e=>l.includes(e.props.id)?m.push(e):g.push(e)));const p=(e=>{const{wedges:t,rotatedComponents:a,childrenArray:n,renderRotatedComponents:i}=e,r=a.reduce(((e,t)=>({...e,[t]:!0})),{});return[...n.map((e=>{return r[(t=e,t.props.id.split("__")[0])]?i(e):e;var t})),...t.map((e=>o().createElement("div",{key:e,"data-mesh-id":e})))]})({childrenArray:g,rotatedComponents:s,wedges:r,renderRotatedComponents:u});return o().createElement("div",a()({},I(e),{"data-mesh-id":n+"inlineContent","data-testid":Wa,className:R(i,d),ref:t}),o().createElement("div",{"data-mesh-id":n+"inlineContent-gridContainer","data-testid":ja},p),m)};var Ka=o().forwardRef(Za),Ja="Tj01hh";var Xa=e=>{var t,n;const{id:i,alt:o,role:s,className:c,imageStyles:l={},targetWidth:d,targetHeight:u,onLoad:h,onError:m,containerWidth:g,containerHeight:p,isInFirstFold:f,socialAttrs:E,skipMeasure:I,responsiveImageProps:v,zoomedImageResponsiveOverride:_,displayMode:b}=e,T=d||g,y=u||p,{fallbackSrc:L,srcset:w,sources:A,css:C}=v||{},{width:M,height:k,...N}=(null==v||null==(t=v.css)?void 0:t.img)||{},O="original_size"===b?null==v||null==(n=v.css)?void 0:n.img:N;var S;return L&&w&&C?r.createElement("img",a()({fetchpriority:f?"high":void 0,loading:!1===f?"lazy":void 0,sizes:T+"px",srcSet:I?null==_?void 0:_.srcset:null==v?void 0:v.srcset,id:i,src:L,alt:o||"",role:s,style:{...l,...I?{...null==_||null==(S=_.css)?void 0:S.img}:{...O}},onLoad:h,onError:m,className:R(c,Ja),width:T,height:y},E)):L&&A&&C?r.createElement("picture",null,A.map((e=>{let{srcset:t,media:a,sizes:n}=e;return r.createElement("source",{key:a,srcSet:t,media:a,sizes:n})})),r.createElement("img",a()({fetchpriority:f?"high":void 0,loading:!1===f?"lazy":void 0,id:i,src:A[0].fallbackSrc,alt:o||"",role:s,style:{...l,objectFit:A[0].imgStyle.objectFit,objectPosition:A[0].imgStyle.objectPosition},onLoad:h,onError:m,className:R(c,Ja),width:T,height:y},E))):r.createElement(Zt,e)};var Qa=e=>{var t,a,n;const{className:i,customIdPrefix:o,getPlaceholder:s,hasAnimation:c,...l}=e,d=r.useMemo((()=>JSON.stringify({containerId:l.containerId,alignType:l.alignType,fittingType:l.displayMode,hasAnimation:c,imageData:{width:l.width,height:l.height,uri:l.uri,name:l.name,...l.quality&&{quality:l.quality},displayMode:l.displayMode}})),[l,c]),u=r.useRef(null);u.current||(u.current=s?s({fittingType:l.displayMode,src:{id:l.uri,width:l.width,height:l.height,name:l.name},target:{width:l.containerWidth,height:l.containerHeight,alignment:l.alignType,htmlTag:"bg"},options:{hasAnimation:c,allowAnimatedTransform:!1}}):{uri:void 0,css:{img:{}},attr:{img:{},container:{}}});const h=u.current,m=null!=(t=null==h?void 0:h.uri)?t:"",g=null!=(a=null==(n=h.css)?void 0:n.container)?a:{},p=Object.assign(m?{backgroundImage:"url("+m+")"}:{},g);return r.createElement("wix-bg-image",{id:""+(o||"bgImg_")+l.containerId,class:i,style:p,"data-tiled-image-info":d,"data-has-bg-scroll-effect":l.hasBgScrollEffect||"","data-bg-effect-name":l.bgEffectName||"","data-motion-part":"BG_IMG "+l.containerId})};var en=e=>null==e?void 0:e.replace(":hover",""),tn="bX9O_S",an="Z_wCwr",nn="Jxk_UL",rn="K8MSra",on="YTb3b4";const sn={quality:{unsharpMask:{radius:.33,amount:1,threshold:0}},devicePixelRatio:1};var cn=e=>{const{id:t,videoRef:n,videoInfo:i,posterImageInfo:o,muted:s,preload:c,loop:l,alt:d,isVideoEnabled:u,getPlaceholder:h,extraClassName:m=""}=e;i.containerId=en(i.containerId);const g=r.useMemo((()=>JSON.stringify(i)),[i]),p=r.createElement(r.Fragment,null,o.filterEffectSvgString&&r.createElement("svg",{id:"svg_"+i.containerId,className:on},r.createElement("defs",{dangerouslySetInnerHTML:{__html:ea(o.filterEffectSvgString,i.containerId)}})),r.createElement(Xa,a()({key:i.videoId+"_img",id:o.containerId+"_img",className:R(an,nn,"bgVideoposter",m),imageStyles:{width:"100%",height:"100%"}},o,sn,{getPlaceholder:h})));return u?r.createElement("wix-video",{id:t,"data-video-info":g,"data-motion-part":"BG_IMG "+i.containerId,class:R(tn,"bgVideo",m)},r.createElement("video",{key:i.videoId+"_video",ref:n,id:i.containerId+"_video",className:rn,crossOrigin:"anonymous","aria-label":d,playsInline:!0,preload:c,muted:s,loop:l}),p):p},ln="SUz0WK";var dn=e=>{const{id:t,containerId:a,pageId:n,children:i,bgEffectName:o="",containerSize:s}=e;return r.createElement("wix-bg-media",{id:t,class:ln,"data-container-id":a,"data-container-size":((null==s?void 0:s.width)||0)+", "+((null==s?void 0:s.height)||0),"data-page-id":n,"data-bg-effect-name":o,"data-motion-part":"BG_MEDIA "+a},i)};const un="bgOverlay";var hn="m4khSP",mn="FNxOn5";var gn=e=>{const{imageOverlay:t}=e;return r.createElement("div",{"data-testid":un,className:hn},t&&r.createElement(Qa,a()({customIdPrefix:"bgImgOverlay_",className:mn},t)))};const pn="bgLayers",fn="colorUnderlay",En="mediaPadding",In="canvas";var vn="MW5IWV",_n="N3eg0s",bn="Kv1aVt",Tn="dLPlxY",yn="VgO9Yg",Ln="LWbAav",wn="yK6aSC",An="K_YxMd",Cn="NGjcJN",Mn="mNGsUM",kn="I8xA4L";const Nn="bgImage";var On=e=>{const{videoRef:t,canvasRef:n,hasBgFullscreenScrollEffect:i,image:o,backgroundImage:s,backgroundMedia:c,video:l,backgroundOverlay:d,shouldPadMedia:u,extraClass:h="",shouldRenderUnderlay:m=!l,reducedMotion:g=!1,getPlaceholder:p,hasCanvasAnimation:f,useWixMediaCanvas:E,onClick:I}=e,{onImageLoad:v}=(e=>{let{onReady:t,image:a}=e;return(0,r.useEffect)((()=>{t&&!a&&t()}),[t,a]),{onImageLoad:e=>{null!=a&&a.onLoad&&a.onLoad(e),t&&t()}}})(e),_=en(e.containerId),b="img_"+en(_),T=o&&r.createElement(Xa,a()({id:b,className:R(bn,Tn,Mn,Nn),imageStyles:{width:"100%",height:"100%"},getPlaceholder:p},o,{onLoad:v})),y=s&&r.createElement(Qa,a()({},s,{containerId:_,className:R(bn,Tn,Mn,Nn),getPlaceholder:p})),L=l&&r.createElement(cn,a()({id:"videoContainer_"+_},l,{extraClassName:wn,reducedMotion:g,videoRef:t,getPlaceholder:p})),w=E&&n||f?r.createElement("wix-media-canvas",{"data-container-id":_,class:f?kn:""},T,y,L,r.createElement("canvas",{id:_+"webglcanvas",className:R(An,"webglcanvas"),"aria-label":(null==l?void 0:l.alt)||"",role:"presentation","data-testid":In})):r.createElement(r.Fragment,null,T,y,L,n&&r.createElement("canvas",{id:_+"webglcanvas",ref:n,className:R(An,"webglcanvas"),"aria-label":(null==l?void 0:l.alt)||"",role:"presentation","data-testid":In})),A=c?r.createElement(dn,a()({id:"bgMedia_"+_},c),w):r.createElement("div",{id:"bgMedia_"+_,"data-motion-part":"BG_MEDIA "+_,className:yn},w),C=d&&r.createElement(gn,d);return r.createElement("div",{id:pn+"_"+_,"data-hook":pn,"data-motion-part":"BG_LAYER "+_,className:R(vn,h,{[_n]:i}),onClick:I},m&&r.createElement("div",{"data-testid":fn,className:R(Ln,bn)}),u?r.createElement("div",{"data-testid":En,className:Cn},A,C):r.createElement(r.Fragment,null,A,C))},Sn="YzqVVZ",Pn="mwF7X1",Rn="YGilLk";var xn=e=>{let{id:t,fillLayers:n,children:i,meshProps:r,videoRef:s,getPlaceholder:c,onReady:l}=e;const d=n.hasBgFullscreenScrollEffect;return o().createElement(o().Fragment,null,o().createElement(On,a()({},n,{containerId:t,onReady:l,getPlaceholder:c,videoRef:s})),o().createElement(Ka,a()({id:t},r,{extraClassName:R({[Pn]:d})}),i))};const Fn="column-strip__column",Gn=(e,t)=>{const{id:n,className:i,customClassNames:r=[],children:s,onClick:c,onDblClick:l,onMouseEnter:d,onMouseLeave:u,shouldAddTabIndex0:h,hasPlatformClickHandler:m,translations:g,fillLayers:p,onStop:f,isRepeaterItem:E,columnOverrides:v}=e,_=R(i,Sn,S(E?Va.repeaterItem:v?Fn:"",...r),{[Rn]:m}),b=h?{tabIndex:0,role:"region","aria-label":(null==g?void 0:g.ariaLabel)||"Interactive element, focus to trigger content change"}:{},T=re(t,!!p.video,f);return o().createElement("div",a()({id:n},I(e),{className:_,onClick:c,onDoubleClick:l,onMouseEnter:d,onMouseLeave:u},b),o().createElement(xn,a()({},e,{videoRef:T}),s))};var Bn=o().forwardRef(Gn);const Hn="screenWidthContainerBg",Dn="screenWidthContainerBgCenter";var Un=e=>{let{id:t,className:n,skinClassName:i,tagName:r="div",transition:s,transitionEnded:c,eventHandlers:l,skinStyles:d,children:u,tabIndex:h,lang:m}=e;const g=r;return o().createElement(g,a()({id:t,className:R(i,s&&d[s],c&&d.transitionEnded,n),tabIndex:h,lang:m},l),u)},Yn={screenWidthBackground:"_C0cVf",HeaderHideToTop:"hFwGTD",headerHideToTop:"hFwGTD",HeaderHideToTopReverse:"IQgXoP",headerHideToTopReverse:"IQgXoP",HeaderFadeOut:"Nr3Nid",headerFadeOut:"Nr3Nid",transitionEnded:"l4oO6c",HeaderFadeOutReverse:"iQuoC4",headerFadeOutReverse:"iQuoC4",inlineContent:"CJF7A2",centeredContent:"U4Bvut",centeredContentBg:"G5K6X8",DefaultScreen:"xU8fqS",defaultScreen:"xU8fqS",bg:"_4XcTfy","bg-center":"gUbusX",bgCenter:"gUbusX"};var $n=e=>{let{wrapperProps:t,children:n}=e;return o().createElement(Un,a()({},t,{skinClassName:Yn.DefaultScreen,skinStyles:Yn}),o().createElement("div",{className:Yn.screenWidthBackground},o().createElement("div",{className:Yn.bg,"data-testid":Hn})),o().createElement("div",{className:Yn.centeredContent},o().createElement("div",{className:Yn.centeredContentBg},o().createElement("div",{className:Yn.bgCenter,"data-testid":Dn})),o().createElement("div",{className:Yn.inlineContent},n)))};var zn={root:"footer"};var Vn=e=>{const{id:t,className:n,customClassNames:i=[],skin:o,children:s,meshProps:c,fillLayers:l,lang:d}=e,u={onMouseEnter:e.onMouseEnter,onMouseLeave:e.onMouseLeave,onClick:e.onClick,onDoubleClick:e.onDblClick},h=R(n,S(zn.root,...i));return r.createElement(o,{wrapperProps:{...I(e),lang:d,id:t,tagName:"footer",eventHandlers:u,tabIndex:"-1",className:h},fillLayers:l,"data-block-level-container":"FooterContainer"},r.createElement(Ka,a()({id:t},c),s))};var jn=e=>o().createElement(Vn,a()({},e,{skin:$n})),Wn="SPY_vo";var qn=e=>{const{id:t,className:n,meshProps:i,children:r,onClick:s,onDblClick:c,onMouseEnter:l,onMouseLeave:d}=e;return o().createElement("div",a()({id:t},I(e),{onClick:s,onDoubleClick:c,onMouseEnter:l,onMouseLeave:d,className:R(n,Wn)}),o().createElement(Ka,a()({id:t},i),r))},Zn=n(96114),Kn=n.n(Zn);function Jn(){if(!E())return{x:0,y:0,isAtPageBottom:!1};const{left:e,top:t}=document.body.getBoundingClientRect();return{x:e,y:t,isAtPageBottom:window.innerHeight+window.scrollY===document.body.scrollHeight}}var Xn={root:"header"};const Qn="Reverse",ei="up",ti="down";var ai=e=>{const{id:t,skin:n,children:i,animations:s,meshProps:c,className:l,customClassNames:d=[],fillLayers:u,lang:h}=e,[m,g]=(0,r.useState)(""),[p,f]=(0,r.useState)(!1),I=e=>{g(e),f(!1)};(0,r.useEffect)((()=>{window.TransitionEvent||setTimeout((()=>f(!0)),200)}),[m]);const v=m&&!(e=>e.endsWith(Qn))(m),_=()=>{const e=(e=>""+e+Qn)(m);I(e)},b={onMouseEnter:e.onMouseEnter,onMouseLeave:e.onMouseLeave,onClick:e.onClick,onDoubleClick:e.onDblClick,onFocus:v?_:void 0,onTransitionEnd:()=>f(!0)};let T=ti,y=0;!function(e,t,a){void 0===a&&(a={}),a={waitFor:100,disabled:!1,...a};const n=(0,r.useRef)(Jn());let i=null;const o=()=>{Kn().measure((()=>{const t=Jn(),a=n.current;n.current=t,i=null,Kn().mutate((()=>e({prevPos:a,currPos:t})))}))};(E()?r.useLayoutEffect:r.useEffect)((()=>{if(!E())return;const e=()=>{null===i&&(i=window.setTimeout(o,a.waitFor))};return a.disabled?()=>{}:(window.addEventListener("scroll",e),()=>{window.removeEventListener("scroll",e),i&&window.clearTimeout(i)})}),t)}((e=>{var t,a;let{currPos:n,prevPos:i}=e;const r=n.y&&-1*n.y,o=i.y&&-1*i.y,c=s[s.length-1],l=null==(t=c.params)||null==(t=t.animations)?void 0:t[c.params.animations.length-1];if(!l)return;const d="mobile"===(null==(a=c.viewMode)?void 0:a.toLowerCase())?1:(e=>{switch(e){case"HeaderFadeOut":return 200;case"HeaderHideToTop":return 400;default:return null}})(l.name);d&&(((e,t)=>{T===ti&&e<t?(y=t,T=ei):T===ei&&e>t&&e>=0&&t>=0&&(y=t,T=ti)})(r,o),v?(T===ei&&r+d<y||0===n.y)&&_():T===ti&&r-y>=d&&I(l.name))}),[m,s],{disabled:!s||!s.length});const L=R(l,S(Xn.root,...d));return o().createElement(n,{wrapperProps:{id:t,tagName:"header",eventHandlers:b,className:L,transition:m,transitionEnded:p,tabIndex:"-1",lang:h},"data-block-level-container":"HeaderContainer",fillLayers:u},o().createElement(Ka,a()({id:t},c,{children:i})))};var ni=e=>o().createElement(ai,a()({},e,{skin:$n}));var ii=j((e=>{let{mapperProps:t,controllerUtils:a}=e;const{updateStyles:n}=a,{compId:i,marginTop:o,isMobileView:s,isFixed:c,...l}=t;var d;return d=()=>{var e;const t=((null==(e=window.document.getElementById(i))?void 0:e.clientHeight)||0)>=window.document.body.clientHeight/2;s&&c&&t&&n({position:"relative !important",marginTop:o,top:0})},(0,r.useEffect)(d,[]),l})),ri="BmZ5pC";const oi=(e,t)=>{const{id:n,className:i,fillLayers:o,isMediaPositionOverride:s,mediaHeightOverrideType:c,getPlaceholder:l,onStop:d}=e,u=re(t,!!o.video,d);return r.createElement("div",a()({id:n},I(e),{"data-media-height-override-type":c,"data-media-position-override":s,className:R(i,ri)}),r.createElement(On,a()({},o,{getPlaceholder:l,videoRef:u})))};var si=r.forwardRef(oi);const ci="expandButton",li="zoomButton";var di=e=>{const{skin:t,id:n,uri:i,alt:o,name:s,width:c,height:l,displayMode:d,focalPoint:u,filterEffectSvgUrl:h,quality:m,crop:g,onSizeChange:p,onLoad:f,getPlaceholder:E,containerWidth:I,containerHeight:v,isInFirstFold:_,hasAnimation:b,responsiveImageProps:T,zoomedImageResponsiveOverride:y,encoding:L}=e,w={containerId:n,uri:i||"data:image/gif;base64,R0lGODlhAQABAIAAAP///wAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==",alt:o,name:s,width:c,height:l,displayMode:d,focalPoint:u,quality:m,crop:g,onLoad:f&&(A=i,/(^https?)|(^data)|(^blob)|(^\/\/)/.test(A))?e=>{const{naturalWidth:t,naturalHeight:a}=e.target;t&&a&&(t!==c||a!==l)&&(p(t,a),null==f||f())}:void 0,getPlaceholder:E,containerWidth:I,containerHeight:v,isInFirstFold:_,hasAnimation:b,responsiveImageProps:T,zoomedImageResponsiveOverride:y,encoding:L};var A;const C=h?ea(h,n):void 0;return r.createElement(t,a()({},e,{imageProps:w,filterEffectSvgUrl:C}))},ui={link:"j7pOnl",image:"BI8PVQ",root:"MazNVa"},hi="rYiAuL",mi="gSXewE";const gi=e=>{const{id:t,children:n,className:i,title:o,onClick:s,onDblClick:c,withOnClickHandler:l,onMouseEnter:d,onMouseLeave:u,filterEffectSvgString:h,filterEffectSvgUrl:m,shouldHideTooltip:g}=e,p=l?hi:"",f=m?{style:{"--filter-effect-svg-url":m}}:{},E=h?r.createElement("svg",{id:"svg_"+t,className:mi},r.createElement("defs",{dangerouslySetInnerHTML:{__html:ea(h,t)}})):null;return r.createElement("div",a()({id:t},I(e),{className:R(i,p),title:g?void 0:o,onClick:s,onDoubleClick:c,onMouseEnter:d,onMouseLeave:u},f),E,n)};var pi,fi,Ei,Ii={root:"image"},vi="nTOEE9",_i="sqUyGm",bi="C_JY0G",Ti="RZQnmg";function yi(){return yi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)({}).hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e},yi.apply(null,arguments)}var Li,wi,Ai,Ci=function(e){return r.createElement("svg",yi({width:40,height:40,viewBox:"0 0 40 40",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),pi||(pi=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M16.611 23.389A5.464 5.464 0 0 1 15 19.5c0-1.468.572-2.849 1.611-3.888A5.465 5.465 0 0 1 20.5 14c1.469 0 2.85.573 3.889 1.612A5.463 5.463 0 0 1 26 19.5c0 1.469-.572 2.85-1.611 3.889A5.464 5.464 0 0 1 20.5 25a5.464 5.464 0 0 1-3.889-1.611Zm13.392 4.907-4.57-4.571A6.446 6.446 0 0 0 27 19.5a6.453 6.453 0 0 0-1.904-4.595A6.456 6.456 0 0 0 20.5 13a6.458 6.458 0 0 0-4.596 1.905A6.453 6.453 0 0 0 14 19.5c0 1.737.676 3.368 1.904 4.596A6.457 6.457 0 0 0 20.5 26a6.446 6.446 0 0 0 4.226-1.568l4.57 4.571.707-.707Z",fill:"#000624"})),fi||(fi=r.createElement("path",{fill:"#000",d:"M20 17h1v5h-1z"})),Ei||(Ei=r.createElement("path",{fill:"#000",d:"M18 20v-1h5v1z"})))};function Mi(){return Mi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)({}).hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e},Mi.apply(null,arguments)}var ki=function(e){return r.createElement("svg",Mi({width:40,height:40,viewBox:"0 0 40 40",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),Li||(Li=r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M16.611 23.389A5.464 5.464 0 0 1 15 19.5c0-1.468.572-2.849 1.611-3.888A5.465 5.465 0 0 1 20.5 14c1.469 0 2.85.573 3.889 1.612A5.463 5.463 0 0 1 26 19.5c0 1.469-.572 2.85-1.611 3.889A5.464 5.464 0 0 1 20.5 25a5.464 5.464 0 0 1-3.889-1.611Zm13.392 4.907-4.57-4.571A6.446 6.446 0 0 0 27 19.5a6.453 6.453 0 0 0-1.904-4.595A6.456 6.456 0 0 0 20.5 13a6.458 6.458 0 0 0-4.596 1.905A6.453 6.453 0 0 0 14 19.5c0 1.737.676 3.368 1.904 4.596A6.457 6.457 0 0 0 20.5 26a6.446 6.446 0 0 0 4.226-1.568l4.57 4.571.707-.707Z",fill:"#000624"})),wi||(wi=r.createElement("path",{d:"M18 20v-1h5v1z"})),Ai||(Ai=r.createElement("path",{d:"m20 4-3 3h6l-3-3ZM20 36l-3-3h6l-3 3ZM4 19l3-3v6l-3-3ZM36 19l-3-3v6l3-3Z",fill:"#000624"})))};const Ni=(e,t,a)=>{t(!e),e||a({x:0,y:0})};let Oi,Si,Pi,Ri=!1;var xi=e=>{const{width:t,height:n,className:i,magnifyKeyboardOperabilityEnabled:o,translations:s,zoomedImageResponsiveOverride:c}=e,[l,d]=r.useState(!1),[u,h]=r.useState({x:0,y:0}),m=r.useRef(null),g=r.useRef(null),[p,f]=r.useState(!1),E=e=>{const t=e.nativeEvent;if(l)d(!1);else if(!l&&t&&"click"===t.type){d(!0);const{offsetX:e,offsetY:a}=v(t.clientX,t.clientY),{x:n,y:i}=I(e,a);h({x:n,y:i})}},I=(e,a)=>{const i=m.current.offsetWidth,r=m.current.offsetHeight;return{x:e/i*(t-i),y:a/r*(n-r)}},v=(e,t)=>{const a=m.current.getBoundingClientRect();return{offsetX:e-a.left,offsetY:t-a.top}},_=()=>{h({x:Oi,y:Si}),Ri=!1},b={onMouseLeave:e=>{Pi=setTimeout((()=>E(e)),1200)},onMouseEnter:()=>{clearTimeout(Pi)},onMouseMove:e=>{const t=e.nativeEvent,{offsetX:a,offsetY:n}=v(t.clientX,t.clientY);({x:Oi,y:Si}=I(a,n)),Ri||requestAnimationFrame(_),Ri=!0}};let T={};l&&!p?T={transform:"translate(-"+u.x+"px, -"+u.y+"px)",transitionTimingFunction:"ease-out",transitionDuration:"0.2s",willChange:"transform"}:p&&!l&&(T={transform:"translate("+u.x+"px, "+u.y+"px)",transition:"transform 0.3s ease",willChange:"transform"});const y=l?{onClick:E,...b}:{onClick:E},L=l||p?{targetWidth:e.width,targetHeight:e.height,skipMeasure:!0,zoomedImageResponsiveOverride:c}:{skipMeasure:!1};return r.createElement("div",a()({},y,{className:R(vi,""+(l?bi:_i)),ref:m,onKeyDown:e=>((e,t,a,n,i)=>{if(!t)return;["ArrowRight","ArrowLeft","ArrowUp","ArrowDown"].includes(e.key)&&e.preventDefault();const r=n.current.getBoundingClientRect(),o=r.width,s=r.height,c=i.width,l=i.height,d=l/20,u=c/20,h=e.key;a((e=>{const{x:t,y:a}=e,n={ArrowUp:{x:t,y:Math.min(a+d,0)},ArrowDown:{x:t,y:Math.max(a-d,s-l)},ArrowLeft:{x:Math.min(t+u,0),y:a},ArrowRight:{x:Math.max(t-u,o-c),y:a}}[h];return null!=n?n:e}))})(e,p,h,m,{width:t,height:n}),onBlur:()=>f(!1),tabIndex:0}),r.createElement(Xa,a()({},e,{className:i,imageStyles:T},L)),o&&r.createElement("button",{className:Ti,"data-testid":li,"aria-label":null==s?void 0:s.zoomInButtonAriaLabel,"aria-pressed":p,onClick:()=>Ni(p,f,h),onKeyDown:e=>((e,t,a)=>{"Enter"===e.key||" "===e.key?(e.preventDefault(),t()):"Escape"===e.key&&a()})(e,(()=>Ni(p,f,h)),(()=>f(!1))),ref:g},p?r.createElement(ki,null):r.createElement(Ci,null)))};var Fi="OJQ_3L";var Gi=e=>{const{skinsStyle:t,id:n,className:i,customClassNames:o=[],link:s,imageProps:c,title:l,onClick:d,hasPlatformClickHandler:u=!1,onClickBehavior:h,onDblClick:m,onMouseEnter:g,onMouseLeave:p,reportBiOnClick:E,filterEffectSvgString:v,filterEffectSvgUrl:_,magnifyKeyboardOperabilityEnabled:b,popupA11yExperimentEnabled:y,translations:L,shouldHideTooltip:w}=e,A="zoomAndPanMode"===h?xi:Xa;const C="zoomMode"===h;let M;C&&(M=s);var k;const N=!(!(k=s)||0===Object.keys(k).length&&k.constructor===Object)||u||C,O=f({onClick:d,reportBiOnClick:E});let P;(N||Boolean(d))&&(P=O);const x=C&&y,F=(e=>{let{onClickBehavior:t,className:a,link:n}=e;const i={className:a};return"zoomMode"===t||"zoomAndPanMode"===t?i:{...n,...i}})({onClickBehavior:h,className:t.link,link:s});return r.createElement(gi,a()({id:n},I(e),{className:R(t.root,i,S(Ii.root,...o)),title:l,onClick:P,onDblClick:m,onMouseEnter:g,onMouseLeave:p,withOnClickHandler:N,filterEffectSvgString:v,filterEffectSvgUrl:_,shouldHideTooltip:w}),r.createElement(T,F,r.createElement(A,a()({id:"img_"+n},c,{className:t.image,magnifyKeyboardOperabilityEnabled:b,translations:L,link:M}))),x&&r.createElement("button",{"data-testid":ci,onClick:P,className:Fi,"aria-haspopup":"dialog","aria-label":null==L?void 0:L.expandAriaLabel,"aria-describedby":c.alt?c.containerId:void 0},r.createElement("svg",{width:"24px",height:"24px",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},r.createElement("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M12 4V5H5.5C5.224 5 5 5.225 5 5.5V18.5C5 18.775 5.224 19 5.5 19H18.5C18.776 19 19 18.775 19 18.5V12H20V18.5C20 19.327 19.327 20 18.5 20H5.5C4.673 20 4 19.327 4 18.5V5.5C4 4.673 4.673 4 5.5 4H12ZM20 4V9H19V5.707L12.71 11.997L12.003 11.29L18.293 5H15V4H20Z"}))))};const Bi=e=>r.createElement(Gi,a()({},e,{skinsStyle:ui}));var Hi=e=>r.createElement(di,a()({},e,{skin:Bi})),Di=j((e=>{let{controllerUtils:t,mapperProps:a,stateValues:n}=e;const{reportBi:i}=n,{compId:r,language:o,mainPageId:s,fullNameCompType:c,trackClicksAnalytics:l,...d}=a,u=Q(n.experiments,"specs.thunderbolt.magnifyKeyboardOperability"),h=Q(n.experiments,"specs.thunderbolt.hideWPhotoTooltip");return{...d,reportBiOnClick:()=>{const{link:e,title:t,uri:a}=d;J(i,{link:e,language:o,trackClicksAnalytics:l,details:{uri:a},element_id:r,elementTitle:t,elementType:c,pagesMetadata:{mainPageId:s},elementGroup:X.Image})},onSizeChange:(e,a)=>{t.updateProps({width:e,height:a})},magnifyKeyboardOperabilityEnabled:u,shouldHideTooltip:h}}));var Ui=e=>{const{id:t,className:n,customClassNames:i=[],skin:o,pageDidMount:s,onClick:c=()=>{},onDblClick:l=()=>{},onMouseEnter:d,onMouseLeave:u,children:h}=e;return r.createElement(o,a()({id:t,className:n,customClassNames:i},I(e),{pageDidMount:s,onClick:c,onDblClick:l,onMouseEnter:d,onMouseLeave:u}),h)},Yi={bg:"c7cMWz",inlineContent:"FVGvCX",pageWrapper:"zK7MhX",root:"fEm0Bo"};const $i="page-bg";var zi={root:"page"};var Vi=e=>{let{id:t,className:a,customClassNames:n=[],pageDidMount:i,onClick:r,onDblClick:s,children:c,skinsStyle:l,onMouseEnter:d,onMouseLeave:u}=e;const h=R(l.root,l.pageWrapper,a);return o().createElement("div",{id:t,className:h,ref:i,onClick:r,onDoubleClick:s,onMouseEnter:d,onMouseLeave:u},o().createElement("div",{className:R(l.bg,S(zi.root,...n)),"data-testid":$i}),o().createElement("div",{className:l.inlineContent},c()))};const ji=e=>o().createElement(Vi,a()({},e,{skinsStyle:Yi}));var Wi={bg:"PFkO7r",inlineContent:"HT5ybB",pageWrapper:"dBAkHi"};const qi=e=>o().createElement(Vi,a()({},e,{skinsStyle:Wi}));const Zi={SiteButton_BasicButton:{component:z,controller:ee},SiteButton_TextOnlyButtonSkin:{component:ie,controller:ee},StripColumnsContainer:{component:za},Column:{component:Bn},Column_DefaultColumn:{component:Bn},FooterContainer_DefaultScreen:{component:jn},Group:{component:qn},HeaderContainer_DefaultScreen:{component:ni,controller:ii},MediaContainer:{component:Bn},PageBackground:{component:si},WPhoto_NoSkinPhoto:{component:Hi,controller:Di},ContainerWrapper:{component:e=>{const{id:t,children:n,tagName:i,className:o}=e,s=i;return r.createElement(s,a()({},I(e),{className:o,tabIndex:-1,id:t}),n())}},Page_BasicPageSkin:{component:e=>o().createElement(Ui,a()({},e,{skin:ji}))},Page_TransparentPageSkin:{component:e=>o().createElement(Ui,a()({},e,{skin:qi}))}}}(),i}()}));
//# sourceMappingURL=https://static.parastorage.com/services/editor-elements-library/dist/thunderbolt/rb_wixui.thunderbolt_bootstrap-classic.ee97aa67.bundle.min.js.map