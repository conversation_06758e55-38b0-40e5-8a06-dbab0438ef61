/*! @sentry/browser 7.120.3 (5a833b4) | https://github.com/getsentry/sentry-javascript */
!function(r){var t={};function n(r){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&r[t],o=0;if(n)return n.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&o>=r.length&&(r=void 0),{value:r&&r[o++],done:!r}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function o(r,t){var n="function"==typeof Symbol&&r[Symbol.iterator];if(!n)return r;var o,f,i=n.call(r),e=[];try{for(;(void 0===t||t-- >0)&&!(o=i.next()).done;)e.push(o.value)}catch(r){f={error:r}}finally{try{o&&!o.done&&(n=i.return)&&n.call(i)}finally{if(f)throw f.error}}return e}function f(r,t,n){if(n||2===arguments.length)for(var o,f=0,i=t.length;f<i;f++)!o&&f in t||(o||(o=Array.prototype.slice.call(t,0,f)),o[f]=t[f]);return r.concat(o||Array.prototype.slice.call(t))}function i(r){return r&&r.Math==Math?r:void 0}var e="object"==typeof globalThis&&i(globalThis)||"object"==typeof window&&i(window)||"object"==typeof self&&i(self)||"object"==typeof global&&i(global)||function(){return this}()||{};var a=new Map,c=new Set;function l(r,t){return function(r){var t,o,f,i;if(e._sentryModuleMetadata)try{for(var l=n(Object.keys(e._sentryModuleMetadata)),y=l.next();!y.done;y=l.next()){var u=y.value,v=e._sentryModuleMetadata[u];if(!c.has(u)){c.add(u);var b=r(u);try{for(var h=(f=void 0,n(b.reverse())),d=h.next();!d.done;d=h.next()){var w=d.value;if(w.filename){a.set(w.filename,v);break}}}catch(r){f={error:r}}finally{try{d&&!d.done&&(i=h.return)&&i.call(h)}finally{if(f)throw f.error}}}}}catch(r){t={error:r}}finally{try{y&&!y.done&&(o=l.return)&&o.call(l)}finally{if(t)throw t.error}}}(r),a.get(t)}var y,u,v="ModuleMetadata",b=function(){return{name:v,setupOnce:function(){},setup:function(r){"function"==typeof r.on&&r.on("beforeEnvelope",(function(r){!function(r,t){var o,f,i=r[1];try{for(var e=n(i),a=e.next();!a.done;a=e.next()){var c=a.value;if(t(c,c[0].type))return!0}}catch(r){o={error:r}}finally{try{a&&!a.done&&(f=e.return)&&f.call(e)}finally{if(o)throw o.error}}}(r,(function(r,t){if("event"===t){var o=Array.isArray(r)?r[1]:void 0;o&&(!function(r){try{r.exception.values.forEach((function(r){var t,o;if(r.stacktrace)try{for(var f=n(r.stacktrace.frames||[]),i=f.next();!i.done;i=f.next())delete i.value.module_metadata}catch(r){t={error:r}}finally{try{i&&!i.done&&(o=f.return)&&o.call(f)}finally{if(t)throw t.error}}}))}catch(r){}}(o),r[1]=o)}}))}))},processEvent:function(r,t,o){return function(r,t){try{t.exception.values.forEach((function(t){var o,f;if(t.stacktrace)try{for(var i=n(t.stacktrace.frames||[]),e=i.next();!e.done;e=i.next()){var a=e.value;if(a.filename){var c=l(r,a.filename);c&&(a.module_metadata=c)}}}catch(r){o={error:r}}finally{try{e&&!e.done&&(f=i.return)&&f.call(i)}finally{if(o)throw o.error}}}))}catch(r){}}(o.getOptions().stackParser,r),r}}},h=(y=v,u=b,Object.assign((function(){for(var r=[],t=0;t<arguments.length;t++)r[t]=arguments[t];return u.apply(void 0,f([],o(r),!1))}),{id:y}));for(var d in t.ModuleMetadata=h,t.moduleMetadataIntegration=b,r.Sentry=r.Sentry||{},r.Sentry.Integrations=r.Sentry.Integrations||{},t)Object.prototype.hasOwnProperty.call(t,d)&&(r.Sentry.Integrations[d]=t[d],r.Sentry[d]=t[d])}(window);
//# sourceMappingURL=modulemetadata.es5.min.js.map
