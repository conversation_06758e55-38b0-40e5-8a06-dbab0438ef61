/*! @sentry/browser & @sentry/tracing 7.120.3 (5a833b4) | https://github.com/getsentry/sentry-javascript */
var Sentry=function(t){t=window.Sentry||{};var n=function(t,r){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,n){t.__proto__=n}||function(t,n){for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])},n(t,r)};function r(t,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function e(){this.constructor=t}n(t,r),t.prototype=null===r?Object.create(r):(e.prototype=r.prototype,new e)}var e=function(){return e=Object.assign||function(t){for(var n,r=1,e=arguments.length;r<e;r++)for(var i in n=arguments[r])Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i]);return t},e.apply(this,arguments)};function i(t,n,r,e){return new(r||(r=Promise))((function(i,o){function u(t){try{c(e.next(t))}catch(t){o(t)}}function a(t){try{c(e.throw(t))}catch(t){o(t)}}function c(t){var n;t.done?i(t.value):(n=t.value,n instanceof r?n:new r((function(t){t(n)}))).then(u,a)}c((e=e.apply(t,n||[])).next())}))}function o(t,n){var r,e,i,o,u={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(a){return function(c){return function(a){if(r)throw new TypeError("Generator is already executing.");for(;o&&(o=0,a[0]&&(u=0)),u;)try{if(r=1,e&&(i=2&a[0]?e.return:a[0]?e.throw||((i=e.return)&&i.call(e),0):e.next)&&!(i=i.call(e,a[1])).done)return i;switch(e=0,i&&(a=[2&a[0],i.value]),a[0]){case 0:case 1:i=a;break;case 4:return u.label++,{value:a[1],done:!1};case 5:u.label++,e=a[1],a=[0];continue;case 7:a=u.ops.pop(),u.trys.pop();continue;default:if(!(i=u.trys,(i=i.length>0&&i[i.length-1])||6!==a[0]&&2!==a[0])){u=0;continue}if(3===a[0]&&(!i||a[1]>i[0]&&a[1]<i[3])){u.label=a[1];break}if(6===a[0]&&u.label<i[1]){u.label=i[1],i=a;break}if(i&&u.label<i[2]){u.label=i[2],u.ops.push(a);break}i[2]&&u.ops.pop(),u.trys.pop();continue}a=n.call(t,u)}catch(t){a=[6,t],e=0}finally{r=i=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,c])}}}function u(t){var n="function"==typeof Symbol&&Symbol.iterator,r=n&&t[n],e=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&e>=t.length&&(t=void 0),{value:t&&t[e++],done:!t}}};throw new TypeError(n?"Object is not iterable.":"Symbol.iterator is not defined.")}function a(t,n){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var e,i,o=r.call(t),u=[];try{for(;(void 0===n||n-- >0)&&!(e=o.next()).done;)u.push(e.value)}catch(t){i={error:t}}finally{try{e&&!e.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return u}function c(t,n,r){if(r||2===arguments.length)for(var e,i=0,o=n.length;i<o;i++)!e&&i in n||(e||(e=Array.prototype.slice.call(n,0,i)),e[i]=n[i]);return t.concat(e||Array.prototype.slice.call(n))}var f=Object.prototype.toString;function s(t){switch(f.call(t)){case"[object Error]":case"[object Exception]":case"[object DOMException]":return!0;default:return _(t,Error)}}function v(t,n){return f.call(t)==="[object ".concat(n,"]")}function h(t){return v(t,"ErrorEvent")}function d(t){return v(t,"DOMError")}function l(t){return v(t,"String")}function p(t){return"object"==typeof t&&null!==t&&"__sentry_template_string__"in t&&"__sentry_template_values__"in t}function m(t){return null===t||p(t)||"object"!=typeof t&&"function"!=typeof t}function y(t){return v(t,"Object")}function g(t){return"undefined"!=typeof Event&&_(t,Event)}function b(t){return Boolean(t&&t.then&&"function"==typeof t.then)}function w(t){return"number"==typeof t&&t!=t}function _(t,n){try{return t instanceof n}catch(t){return!1}}function E(t){return!("object"!=typeof t||null===t||!t.__isVue&&!t.t)}function S(t,n){return void 0===n&&(n=0),"string"!=typeof t||0===n||t.length<=n?t:"".concat(t.slice(0,n),"...")}function T(t,n){if(!Array.isArray(t))return"";for(var r=[],e=0;e<t.length;e++){var i=t[e];try{E(i)?r.push("[VueViewModel]"):r.push(String(i))}catch(t){r.push("[value cannot be serialized]")}}return r.join(n)}function O(t,n,r){return void 0===r&&(r=!1),!!l(t)&&(v(n,"RegExp")?n.test(t):!!l(n)&&(r?t===n:t.includes(n)))}function j(t,n,r){return void 0===n&&(n=[]),void 0===r&&(r=!1),n.some((function(n){return O(t,n,r)}))}function k(t,n,r,e,i,o,u){if(void 0===r&&(r=250),o.exception&&o.exception.values&&u&&_(u.originalException,Error)){var a,c,f=o.exception.values.length>0?o.exception.values[o.exception.values.length-1]:void 0;f&&(o.exception.values=(a=x(t,n,i,u.originalException,e,o.exception.values,f,0),c=r,a.map((function(t){return t.value&&(t.value=S(t.value,c)),t}))))}}function x(t,n,r,e,i,o,u,f){if(o.length>=r+1)return o;var s=c([],a(o),!1);if(_(e[i],Error)){I(u,f);var v=t(n,e[i]),h=s.length;C(v,i,h,f),s=x(t,n,r,e[i],i,c([v],a(s),!1),v,h)}return Array.isArray(e.errors)&&e.errors.forEach((function(e,o){if(_(e,Error)){I(u,f);var v=t(n,e),h=s.length;C(v,"errors[".concat(o,"]"),h,f),s=x(t,n,r,e,i,c([v],a(s),!1),v,h)}})),s}function I(t,n){t.mechanism=t.mechanism||{type:"generic",handled:!0},t.mechanism=e(e(e({},t.mechanism),"AggregateError"===t.type&&{is_exception_group:!0}),{exception_id:n})}function C(t,n,r,i){t.mechanism=t.mechanism||{type:"generic",handled:!0},t.mechanism=e(e({},t.mechanism),{type:"chained",source:n,exception_id:r,parent_id:i})}function R(t){return t&&t.Math==Math?t:void 0}var M="object"==typeof globalThis&&R(globalThis)||"object"==typeof window&&R(window)||"object"==typeof self&&R(self)||"object"==typeof global&&R(global)||function(){return this}()||{};function A(){return M}function D(t,n,r){var e=r||M,i=e.__SENTRY__=e.__SENTRY__||{};return i[t]||(i[t]=n())}var L=A();function N(t,n){if(void 0===n&&(n={}),!t)return"<unknown>";try{for(var r=t,e=[],i=0,o=0,u=" > ".length,a=void 0,c=Array.isArray(n)?n:n.keyAttrs,f=!Array.isArray(n)&&n.maxStringLength||80;r&&i++<5&&!("html"===(a=q(r,c))||i>1&&o+e.length*u+a.length>=f);)e.push(a),o+=a.length,r=r.parentNode;return e.reverse().join(" > ")}catch(t){return"<unknown>"}}function q(t,n){var r,e,i,o,u,a=t,c=[];if(!a||!a.tagName)return"";if(L.HTMLElement&&a instanceof HTMLElement&&a.dataset&&a.dataset.sentryComponent)return a.dataset.sentryComponent;c.push(a.tagName.toLowerCase());var f=n&&n.length?n.filter((function(t){return a.getAttribute(t)})).map((function(t){return[t,a.getAttribute(t)]})):null;if(f&&f.length)f.forEach((function(t){c.push("[".concat(t[0],'="').concat(t[1],'"]'))}));else if(a.id&&c.push("#".concat(a.id)),(r=a.className)&&l(r))for(e=r.split(/\s+/),u=0;u<e.length;u++)c.push(".".concat(e[u]));var s=["aria-label","type","name","title","alt"];for(u=0;u<s.length;u++)i=s[u],(o=a.getAttribute(i))&&c.push("[".concat(i,'="').concat(o,'"]'));return c.join("")}function P(t){if(!L.HTMLElement)return null;for(var n=t,r=0;r<5;r++){if(!n)return null;if(n instanceof HTMLElement&&n.dataset.sentryComponent)return n.dataset.sentryComponent;n=n.parentNode}return null}var H=["debug","info","warn","error","log","assert","trace"],U={};function F(t){if(!("console"in M))return t();var n=M.console,r={},e=Object.keys(U);e.forEach((function(t){var e=U[t];r[t]=n[t],n[t]=e}));try{return t()}finally{e.forEach((function(t){n[t]=r[t]}))}}var $=function(){var t=!1,n={enable:function(){t=!0},disable:function(){t=!1},isEnabled:function(){return t}};return H.forEach((function(t){n[t]=function(){}})),n}(),B=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+)?)?@)([\w.-]+)(?::(\d+))?\/(.+)/;function W(t,n){void 0===n&&(n=!1);var r=t.host,e=t.path,i=t.pass,o=t.port,u=t.projectId,a=t.protocol,c=t.publicKey;return"".concat(a,"://").concat(c).concat(n&&i?":".concat(i):"")+"@".concat(r).concat(o?":".concat(o):"","/").concat(e?"".concat(e,"/"):e).concat(u)}function z(t){return{protocol:t.protocol,publicKey:t.publicKey||"",pass:t.pass||"",host:t.host,port:t.port||"",path:t.path||"",projectId:t.projectId}}function X(t){var n="string"==typeof t?function(t){var n=B.exec(t);if(n){var r=a(n.slice(1),6),e=r[0],i=r[1],o=r[2],u=void 0===o?"":o,c=r[3],f=r[4],s=void 0===f?"":f,v="",h=r[5],d=h.split("/");if(d.length>1&&(v=d.slice(0,-1).join("/"),h=d.pop()),h){var l=h.match(/^\d+/);l&&(h=l[0])}return z({host:c,pass:u,path:v,projectId:h,port:s,protocol:e,publicKey:i})}F((function(){console.error("Invalid Sentry Dsn: ".concat(t))}))}(t):z(t);if(n)return n}var G=function(t){function n(n,r){void 0===r&&(r="warn");var e=this.constructor,i=t.call(this,n)||this;return i.message=n,i.name=e.prototype.constructor.name,Object.setPrototypeOf(i,e.prototype),i.logLevel=r,i}return r(n,t),n}(Error),J=/\(error: (.*)\)/,V=/captureMessage|captureException/;function Y(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var r=t.sort((function(t,n){return t[0]-n[0]})).map((function(t){return t[1]}));return function(t,n){var e,i;void 0===n&&(n=0);for(var o=[],a=t.split("\n"),c=n;c<a.length;c++){var f=a[c];if(!(f.length>1024)){var s=J.test(f)?f.replace(J,"$1"):f;if(!s.match(/\S*Error: /)){try{for(var v=(e=void 0,u(r)),h=v.next();!h.done;h=v.next()){var d=(0,h.value)(s);if(d){o.push(d);break}}}catch(t){e={error:t}}finally{try{h&&!h.done&&(i=v.return)&&i.call(v)}finally{if(e)throw e.error}}if(o.length>=50)break}}}return K(o)}}function K(t){if(!t.length)return[];var n=Array.from(t);return/sentryWrapped/.test(n[n.length-1].function||"")&&n.pop(),n.reverse(),V.test(n[n.length-1].function||"")&&(n.pop(),V.test(n[n.length-1].function||"")&&n.pop()),n.slice(0,50).map((function(t){return e(e({},t),{filename:t.filename||n[n.length-1].filename,function:t.function||"?"})}))}var Q="<anonymous>";function Z(t){try{return t&&"function"==typeof t&&t.name||Q}catch(t){return Q}}var tt={},nt={};function rt(t,n){tt[t]=tt[t]||[],tt[t].push(n)}function et(t,n){nt[t]||(n(),nt[t]=!0)}function it(t,n){var r,e,i=t&&tt[t];if(i)try{for(var o=u(i),a=o.next();!a.done;a=o.next()){var c=a.value;try{c(n)}catch(t){}}}catch(t){r={error:t}}finally{try{a&&!a.done&&(e=o.return)&&e.call(o)}finally{if(r)throw r.error}}}function ot(t,n,r){if(n in t){var e=t[n],i=r(e);"function"==typeof i&&at(i,e),t[n]=i}}function ut(t,n,r){try{Object.defineProperty(t,n,{value:r,writable:!0,configurable:!0})}catch(t){}}function at(t,n){try{var r=n.prototype||{};t.prototype=n.prototype=r,ut(t,"__sentry_original__",n)}catch(t){}}function ct(t){return t.__sentry_original__}function ft(t){if(s(t))return e({message:t.message,name:t.name,stack:t.stack},vt(t));if(g(t)){var n=e({type:t.type,target:st(t.target),currentTarget:st(t.currentTarget)},vt(t));return"undefined"!=typeof CustomEvent&&_(t,CustomEvent)&&(n.detail=t.detail),n}return t}function st(t){try{return n=t,"undefined"!=typeof Element&&_(n,Element)?N(t):Object.prototype.toString.call(t)}catch(t){return"<unknown>"}var n}function vt(t){if("object"==typeof t&&null!==t){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(n[r]=t[r]);return n}return{}}function ht(t){return dt(t,new Map)}function dt(t,n){var r,e;if(function(t){if(!y(t))return!1;try{var n=Object.getPrototypeOf(t).constructor.name;return!n||"Object"===n}catch(t){return!0}}(t)){if(void 0!==(f=n.get(t)))return f;var i={};n.set(t,i);try{for(var o=u(Object.keys(t)),a=o.next();!a.done;a=o.next()){var c=a.value;void 0!==t[c]&&(i[c]=dt(t[c],n))}}catch(t){r={error:t}}finally{try{a&&!a.done&&(e=o.return)&&e.call(o)}finally{if(r)throw r.error}}return i}if(Array.isArray(t)){var f;if(void 0!==(f=n.get(t)))return f;var s=[];return n.set(t,s),t.forEach((function(t){s.push(dt(t,n))})),s}return t}function lt(){"console"in M&&H.forEach((function(t){t in M.console&&ot(M.console,t,(function(n){return U[t]=n,function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];var e={args:n,level:t};it("console",e);var i=U[t];i&&i.apply(M.console,n)}}))}))}function pt(){var t=M,n=t.crypto||t.msCrypto,r=function(){return 16*Math.random()};try{if(n&&n.randomUUID)return n.randomUUID().replace(/-/g,"");n&&n.getRandomValues&&(r=function(){var t=new Uint8Array(1);return n.getRandomValues(t),t[0]})}catch(t){}return([1e7]+1e3+4e3+8e3+1e11).replace(/[018]/g,(function(t){return(t^(15&r())>>t/4).toString(16)}))}function mt(t){return t.exception&&t.exception.values?t.exception.values[0]:void 0}function yt(t){var n=t.message,r=t.event_id;if(n)return n;var e=mt(t);return e?e.type&&e.value?"".concat(e.type,": ").concat(e.value):e.type||e.value||r||"<unknown>":r||"<unknown>"}function gt(t,n,r){var e=t.exception=t.exception||{},i=e.values=e.values||[],o=i[0]=i[0]||{};o.value||(o.value=n||""),o.type||(o.type=r||"Error")}function bt(t,n){var r=mt(t);if(r){var i=r.mechanism;if(r.mechanism=e(e(e({},{type:"generic",handled:!0}),i),n),n&&"data"in n){var o=e(e({},i&&i.data),n.data);r.mechanism.data=o}}}function wt(t){if(t&&t.__sentry_captured__)return!0;try{ut(t,"__sentry_captured__",!0)}catch(t){}return!1}function _t(t){return Array.isArray(t)?t:[t]}var Et,St,Tt,Ot=M;function jt(){if(Ot.document){var t=it.bind(null,"dom"),n=kt(t,!0);Ot.document.addEventListener("click",n,!1),Ot.document.addEventListener("keypress",n,!1),["EventTarget","Node"].forEach((function(n){var r=Ot[n]&&Ot[n].prototype;r&&r.hasOwnProperty&&r.hasOwnProperty("addEventListener")&&(ot(r,"addEventListener",(function(n){return function(r,e,i){if("click"===r||"keypress"==r)try{var o=this.__sentry_instrumentation_handlers__=this.__sentry_instrumentation_handlers__||{},u=o[r]=o[r]||{refCount:0};if(!u.handler){var a=kt(t);u.handler=a,n.call(this,r,a,i)}u.refCount++}catch(t){}return n.call(this,r,e,i)}})),ot(r,"removeEventListener",(function(t){return function(n,r,e){if("click"===n||"keypress"==n)try{var i=this.__sentry_instrumentation_handlers__||{},o=i[n];o&&(o.refCount--,o.refCount<=0&&(t.call(this,n,o.handler,e),o.handler=void 0,delete i[n]),0===Object.keys(i).length&&delete this.__sentry_instrumentation_handlers__)}catch(t){}return t.call(this,n,r,e)}})))}))}}function kt(t,n){return void 0===n&&(n=!1),function(r){if(r&&!r._sentryCaptured){var e=function(t){try{return t.target}catch(t){return null}}(r);if(!function(t,n){return"keypress"===t&&(!n||!n.tagName||"INPUT"!==n.tagName&&"TEXTAREA"!==n.tagName&&!n.isContentEditable)}(r.type,e)){ut(r,"_sentryCaptured",!0),e&&!e._sentryId&&ut(e,"_sentryId",pt());var i="keypress"===r.type?"input":r.type;if(!function(t){if(t.type!==St)return!1;try{if(!t.target||t.target._sentryId!==Tt)return!1}catch(t){}return!0}(r))t({event:r,name:i,global:n}),St=r.type,Tt=e?e._sentryId:void 0;clearTimeout(Et),Et=Ot.setTimeout((function(){Tt=void 0,St=void 0}),1e3)}}}}var xt=A();var It=A();function Ct(){if(!("fetch"in It))return!1;try{return new Headers,new Request("http://www.example.com"),new Response,!0}catch(t){return!1}}function Rt(t){return t&&/^function fetch\(\)\s+\{\s+\[native code\]\s+\}$/.test(t.toString())}function Mt(t){var n="fetch";rt(n,t),et(n,At)}function At(){(function(){if("string"==typeof EdgeRuntime)return!0;if(!Ct())return!1;if(Rt(It.fetch))return!0;var t=!1,n=It.document;if(n&&"function"==typeof n.createElement)try{var r=n.createElement("iframe");r.hidden=!0,n.head.appendChild(r),r.contentWindow&&r.contentWindow.fetch&&(t=Rt(r.contentWindow.fetch)),n.head.removeChild(r)}catch(t){}return t})()&&ot(M,"fetch",(function(t){return function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];var i=Nt(n),o=i.method,u=i.url,a={args:n,fetchData:{method:o,url:u},startTimestamp:Date.now()};return it("fetch",e({},a)),t.apply(M,n).then((function(t){return it("fetch",e(e({},a),{endTimestamp:Date.now(),response:t})),t}),(function(t){throw it("fetch",e(e({},a),{endTimestamp:Date.now(),error:t})),t}))}}))}function Dt(t,n){return!!t&&"object"==typeof t&&!!t[n]}function Lt(t){return"string"==typeof t?t:t?Dt(t,"url")?t.url:t.toString?t.toString():"":""}function Nt(t){if(0===t.length)return{method:"GET",url:""};if(2===t.length){var n=a(t,2),r=n[0],e=n[1];return{url:Lt(r),method:Dt(e,"method")?String(e.method).toUpperCase():"GET"}}var i=t[0];return{url:Lt(i),method:Dt(i,"method")?String(i.method).toUpperCase():"GET"}}var qt=null;function Pt(t){var n="error";rt(n,t),et(n,Ht)}function Ht(){qt=M.onerror,M.onerror=function(t,n,r,e,i){var o={column:e,error:i,line:r,msg:t,url:n};return it("error",o),!(!qt||qt.__SENTRY_LOADER__)&&qt.apply(this,arguments)},M.onerror.__SENTRY_INSTRUMENTED__=!0}var Ut=null;function Ft(t){var n="unhandledrejection";rt(n,t),et(n,$t)}function $t(){Ut=M.onunhandledrejection,M.onunhandledrejection=function(t){var n=t;return it("unhandledrejection",n),!(Ut&&!Ut.__SENTRY_LOADER__)||Ut.apply(this,arguments)},M.onunhandledrejection.__SENTRY_INSTRUMENTED__=!0}var Bt,Wt=M;function zt(t){var n="history";rt(n,t),et(n,Xt)}function Xt(){if(t=xt.chrome,n=t&&t.app&&t.app.runtime,r="history"in xt&&!!xt.history.pushState&&!!xt.history.replaceState,!n&&r){var t,n,r,e=Wt.onpopstate;Wt.onpopstate=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var r=Wt.location.href,i=Bt;Bt=r;var o={from:i,to:r};if(it("history",o),e)try{return e.apply(this,t)}catch(t){}},ot(Wt.history,"pushState",i),ot(Wt.history,"replaceState",i)}function i(t){return function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];var e=n.length>2?n[2]:void 0;if(e){var i=Bt,o=String(e);Bt=o;var u={from:i,to:o};it("history",u)}return t.apply(this,n)}}}var Gt,Jt=M;function Vt(t){rt("xhr",t),et("xhr",Yt)}function Yt(){if(Jt.XMLHttpRequest){var t=XMLHttpRequest.prototype;ot(t,"open",(function(t){return function(){for(var n=this,r=[],e=0;e<arguments.length;e++)r[e]=arguments[e];var i=Date.now(),o=l(r[0])?r[0].toUpperCase():void 0,u=Kt(r[1]);if(!o||!u)return t.apply(this,r);this.__sentry_xhr_v3__={method:o,url:u,request_headers:{}},"POST"===o&&u.match(/sentry_key/)&&(this.__sentry_own_request__=!0);var c=function(){var t=n.__sentry_xhr_v3__;if(t&&4===n.readyState){try{t.status_code=n.status}catch(t){}it("xhr",{args:[o,u],endTimestamp:Date.now(),startTimestamp:i,xhr:n})}};return"onreadystatechange"in this&&"function"==typeof this.onreadystatechange?ot(this,"onreadystatechange",(function(t){return function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];return c(),t.apply(this,n)}})):this.addEventListener("readystatechange",c),ot(this,"setRequestHeader",(function(t){return function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];var e=a(n,2),i=e[0],o=e[1],u=this.__sentry_xhr_v3__;return u&&l(i)&&l(o)&&(u.request_headers[i.toLowerCase()]=o),t.apply(this,n)}})),t.apply(this,r)}})),ot(t,"send",(function(t){return function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];var e=this.__sentry_xhr_v3__;if(!e)return t.apply(this,n);void 0!==n[0]&&(e.body=n[0]);var i={args:[e.method,e.url],startTimestamp:Date.now(),xhr:this};return it("xhr",i),t.apply(this,n)}}))}}function Kt(t){if(l(t))return t;try{return t.toString()}catch(t){}}function Qt(t,n,r){void 0===n&&(n=100),void 0===r&&(r=1/0);try{return tn("",t,n,r)}catch(t){return{ERROR:"**non-serializable** (".concat(t,")")}}}function Zt(t,n,r){void 0===n&&(n=3),void 0===r&&(r=102400);var e,i=Qt(t,n);return e=i,function(t){return~-encodeURI(t).split(/%..|./).length}(JSON.stringify(e))>r?Zt(t,n-1,r):i}function tn(t,n,r,e,i){var o,u;void 0===r&&(r=1/0),void 0===e&&(e=1/0),void 0===i&&(o="function"==typeof WeakSet,u=o?new WeakSet:[],i=[function(t){if(o)return!!u.has(t)||(u.add(t),!1);for(var n=0;n<u.length;n++)if(u[n]===t)return!0;return u.push(t),!1},function(t){if(o)u.delete(t);else for(var n=0;n<u.length;n++)if(u[n]===t){u.splice(n,1);break}}]);var c=a(i,2),f=c[0],s=c[1];if(null==n||["number","boolean","string"].includes(typeof n)&&!w(n))return n;var v=function(t,n){try{if("domain"===t&&n&&"object"==typeof n&&n.i)return"[Domain]";if("domainEmitter"===t)return"[DomainEmitter]";if("undefined"!=typeof global&&n===global)return"[Global]";if("undefined"!=typeof window&&n===window)return"[Window]";if("undefined"!=typeof document&&n===document)return"[Document]";if(E(n))return"[VueViewModel]";if(y(e=n)&&"nativeEvent"in e&&"preventDefault"in e&&"stopPropagation"in e)return"[SyntheticEvent]";if("number"==typeof n&&n!=n)return"[NaN]";if("function"==typeof n)return"[Function: ".concat(Z(n),"]");if("symbol"==typeof n)return"[".concat(String(n),"]");if("bigint"==typeof n)return"[BigInt: ".concat(String(n),"]");var r=function(t){var n=Object.getPrototypeOf(t);return n?n.constructor.name:"null prototype"}(n);return/^HTML(\w*)Element$/.test(r)?"[HTMLElement: ".concat(r,"]"):"[object ".concat(r,"]")}catch(t){return"**non-serializable** (".concat(t,")")}var e}(t,n);if(!v.startsWith("[object "))return v;if(n.__sentry_skip_normalization__)return n;var h="number"==typeof n.__sentry_override_normalization_depth__?n.__sentry_override_normalization_depth__:r;if(0===h)return v.replace("object ","");if(f(n))return"[Circular ~]";var d=n;if(d&&"function"==typeof d.toJSON)try{return tn("",d.toJSON(),h-1,e,i)}catch(t){}var l=Array.isArray(n)?[]:{},p=0,m=ft(n);for(var g in m)if(Object.prototype.hasOwnProperty.call(m,g)){if(p>=e){l[g]="[MaxProperties ~]";break}var b=m[g];l[g]=tn(g,b,h-1,e,i),p++}return s(n),l}function nn(t){return new en((function(n){n(t)}))}function rn(t){return new en((function(n,r){r(t)}))}!function(t){t[t.PENDING=0]="PENDING",t[t.RESOLVED=1]="RESOLVED",t[t.REJECTED=2]="REJECTED"}(Gt||(Gt={}));var en=function(){function t(t){var n=this;this.o=function(t){n.u(Gt.RESOLVED,t)},this.v=function(t){n.u(Gt.REJECTED,t)},this.u=function(t,r){n.h===Gt.PENDING&&(b(r)?r.then(n.o,n.v):(n.h=t,n.l=r,n.p()))},this.p=function(){if(n.h!==Gt.PENDING){var t=n.m.slice();n.m=[],t.forEach((function(t){t[0]||(n.h===Gt.RESOLVED&&t[1](n.l),n.h===Gt.REJECTED&&t[2](n.l),t[0]=!0)}))}},this.h=Gt.PENDING,this.m=[];try{t(this.o,this.v)}catch(t){this.v(t)}}return t.prototype.then=function(n,r){var e=this;return new t((function(t,i){e.m.push([!1,function(r){if(n)try{t(n(r))}catch(t){i(t)}else t(r)},function(n){if(r)try{t(r(n))}catch(t){i(t)}else i(n)}]),e.p()}))},t.prototype.catch=function(t){return this.then((function(t){return t}),t)},t.prototype.finally=function(n){var r=this;return new t((function(t,e){var i,o;return r.then((function(t){o=!1,i=t,n&&n()}),(function(t){o=!0,i=t,n&&n()})).then((function(){o?e(i):t(i)}))}))},t}();function on(t){var n=[];function r(t){return n.splice(n.indexOf(t),1)[0]}return{$:n,add:function(e){if(!(void 0===t||n.length<t))return rn(new G("Not adding Promise because buffer limit was reached."));var i=e();return-1===n.indexOf(i)&&n.push(i),i.then((function(){return r(i)})).then(null,(function(){return r(i).then(null,(function(){}))})),i},drain:function(t){return new en((function(r,e){var i=n.length;if(!i)return r(!0);var o=setTimeout((function(){t&&t>0&&r(!1)}),t);n.forEach((function(t){nn(t).then((function(){--i||(clearTimeout(o),r(!0))}),e)}))}))}}}function un(t){if(!t)return{};var n=t.match(/^(([^:/?#]+):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/);if(!n)return{};var r=n[6]||"",e=n[8]||"";return{host:n[4],path:n[5],protocol:n[2],search:r,hash:e,relative:n[5]+r+e}}var an=["fatal","error","warning","log","info","debug"];function cn(t){return"warn"===t?"warning":an.includes(t)?t:"log"}function fn(){return Date.now()/1e3}var sn=function(){var t=M.performance;if(!t||!t.now)return fn;var n=Date.now()-t.now(),r=null==t.timeOrigin?n:t.timeOrigin;return function(){return(r+t.now())/1e3}}(),vn=function(){var t=M.performance;if(t&&t.now){var n=36e5,r=t.now(),e=Date.now(),i=t.timeOrigin?Math.abs(t.timeOrigin+r-e):n,o=i<n,u=t.timing&&t.timing.navigationStart,a="number"==typeof u?Math.abs(u+r-e):n;return o||a<n?i<=a?t.timeOrigin:u:e}}(),hn="baggage",dn="sentry-",ln=/^sentry-/;function pn(t){if(l(t)||Array.isArray(t)){var n={};if(Array.isArray(t))n=t.reduce((function(t,n){var r,e,i=yn(n);try{for(var o=u(Object.keys(i)),a=o.next();!a.done;a=o.next()){var c=a.value;t[c]=i[c]}}catch(t){r={error:t}}finally{try{a&&!a.done&&(e=o.return)&&e.call(o)}finally{if(r)throw r.error}}return t}),{});else{if(!t)return;n=yn(t)}var r=Object.entries(n).reduce((function(t,n){var r=a(n,2),e=r[0],i=r[1];e.match(ln)&&(t[e.slice(dn.length)]=i);return t}),{});return Object.keys(r).length>0?r:void 0}}function mn(t){if(t){var n=Object.entries(t).reduce((function(t,n){var r=a(n,2),e=r[0],i=r[1];return i&&(t["".concat(dn).concat(e)]=i),t}),{});return function(t){if(0===Object.keys(t).length)return;return Object.entries(t).reduce((function(t,n,r){var e=a(n,2),i=e[0],o=e[1],u="".concat(encodeURIComponent(i),"=").concat(encodeURIComponent(o)),c=0===r?u:"".concat(t,",").concat(u);return c.length>8192?t:c}),"")}(n)}}function yn(t){return t.split(",").map((function(t){return t.split("=").map((function(t){return decodeURIComponent(t.trim())}))})).reduce((function(t,n){var r=a(n,2),e=r[0],i=r[1];return t[e]=i,t}),{})}var gn=new RegExp("^[ \\t]*([0-9a-f]{32})?-?([0-9a-f]{16})?-?([01])?[ \\t]*$");function bn(t){if(t){var n,r=t.match(gn);if(r)return"1"===r[3]?n=!0:"0"===r[3]&&(n=!1),{traceId:r[1],parentSampled:n,parentSpanId:r[2]}}}function wn(t,n,r){void 0===t&&(t=pt()),void 0===n&&(n=pt().substring(16));var e="";return void 0!==r&&(e=r?"-1":"-0"),"".concat(t,"-").concat(n).concat(e)}function _n(t,n){return void 0===n&&(n=[]),[t,n]}function En(t,n){var r=a(t,2);return[r[0],c(c([],a(r[1]),!1),[n],!1)]}function Sn(t,n){var r,e,i=t[1];try{for(var o=u(i),a=o.next();!a.done;a=o.next()){var c=a.value;if(n(c,c[0].type))return!0}}catch(t){r={error:t}}finally{try{a&&!a.done&&(e=o.return)&&e.call(o)}finally{if(r)throw r.error}}return!1}function Tn(t,n){return(n||new TextEncoder).encode(t)}function On(t,n){var r,e,i=a(t,2),o=i[0],c=i[1],f=JSON.stringify(o);function s(t){"string"==typeof f?f="string"==typeof t?f+t:[Tn(f,n),t]:f.push("string"==typeof t?Tn(t,n):t)}try{for(var v=u(c),h=v.next();!h.done;h=v.next()){var d=a(h.value,2),l=d[0],p=d[1];if(s("\n".concat(JSON.stringify(l),"\n")),"string"==typeof p||p instanceof Uint8Array)s(p);else{var m=void 0;try{m=JSON.stringify(p)}catch(t){m=JSON.stringify(Qt(p))}s(m)}}}catch(t){r={error:t}}finally{try{h&&!h.done&&(e=v.return)&&e.call(v)}finally{if(r)throw r.error}}return"string"==typeof f?f:function(t){var n,r,e=t.reduce((function(t,n){return t+n.length}),0),i=new Uint8Array(e),o=0;try{for(var a=u(t),c=a.next();!c.done;c=a.next()){var f=c.value;i.set(f,o),o+=f.length}}catch(t){n={error:t}}finally{try{c&&!c.done&&(r=a.return)&&r.call(a)}finally{if(n)throw n.error}}return i}(f)}function jn(t,n){var r="string"==typeof t.data?Tn(t.data,n):t.data;return[ht({type:"attachment",length:r.length,filename:t.filename,content_type:t.contentType,attachment_type:t.attachmentType}),r]}var kn={session:"session",sessions:"session",attachment:"attachment",transaction:"transaction",event:"error",client_report:"internal",user_report:"default",profile:"profile",replay_event:"replay",replay_recording:"replay",check_in:"monitor",feedback:"feedback",span:"span",statsd:"metric_bucket"};function xn(t){return kn[t]}function In(t){if(t&&t.sdk){var n=t.sdk;return{name:n.name,version:n.version}}}function Cn(t,n,r){var i,o,c,f,s=n.statusCode,v=n.headers;void 0===r&&(r=Date.now());var h=e({},t),d=v&&v["x-sentry-rate-limits"],l=v&&v["retry-after"];if(d)try{for(var p=u(d.trim().split(",")),m=p.next();!m.done;m=p.next()){var y=a(m.value.split(":",5),5),g=y[0],b=y[1],w=y[4],_=parseInt(g,10),E=1e3*(isNaN(_)?60:_);if(b)try{for(var S=(c=void 0,u(b.split(";"))),T=S.next();!T.done;T=S.next()){var O=T.value;"metric_bucket"===O&&w&&!w.split(";").includes("custom")||(h[O]=r+E)}}catch(t){c={error:t}}finally{try{T&&!T.done&&(f=S.return)&&f.call(S)}finally{if(c)throw c.error}}else h.all=r+E}}catch(t){i={error:t}}finally{try{m&&!m.done&&(o=p.return)&&o.call(p)}finally{if(i)throw i.error}}else l?h.all=r+function(t,n){void 0===n&&(n=Date.now());var r=parseInt("".concat(t),10);if(!isNaN(r))return 1e3*r;var e=Date.parse("".concat(t));return isNaN(e)?6e4:e-n}(l,r):429===s&&(h.all=r+6e4);return h}function Rn(t,n){var r={type:n.name||n.constructor.name,value:n.message},e=function(t,n){return t(n.stack||"",1)}(t,n);return e.length&&(r.stacktrace={frames:e}),r}var Mn=function(){function t(n){this.name=t.id,F((function(){console.warn("You are using new Feedback() even though this bundle does not include Feedback.")}))}return t.prototype.setupOnce=function(){},t.prototype.openDialog=function(){},t.prototype.closeDialog=function(){},t.prototype.attachTo=function(){},t.prototype.createWidget=function(){},t.prototype.removeWidget=function(){},t.prototype.getWidget=function(){},t.prototype.remove=function(){},t.id="Feedback",t}();var An=function(){function t(n){this.name=t.id,F((function(){console.warn("You are using new Replay() even though this bundle does not include replay.")}))}return t.prototype.setupOnce=function(){},t.prototype.start=function(){},t.prototype.stop=function(){},t.prototype.flush=function(){},t.id="Replay",t}();var Dn="production";function Ln(){return D("globalEventProcessors",(function(){return[]}))}function Nn(t){Ln().push(t)}function qn(t,n,r,i){return void 0===i&&(i=0),new en((function(o,u){var a=t[i];if(null===n||"function"!=typeof a)o(n);else{var c=a(e({},n),r);b(c)?c.then((function(n){return qn(t,n,r,i+1).then(o)})).then(null,u):qn(t,c,r,i+1).then(o).then(null,u)}}))}function Pn(t){var n=sn(),r={sid:pt(),init:!0,timestamp:n,started:n,duration:0,status:"ok",errors:0,ignoreDuration:!1,toJSON:function(){return function(t){return ht({sid:"".concat(t.sid),init:t.init,started:new Date(1e3*t.started).toISOString(),timestamp:new Date(1e3*t.timestamp).toISOString(),status:t.status,errors:t.errors,did:"number"==typeof t.did||"string"==typeof t.did?"".concat(t.did):void 0,duration:t.duration,abnormal_mechanism:t.abnormal_mechanism,attrs:{release:t.release,environment:t.environment,ip_address:t.ipAddress,user_agent:t.userAgent}})}(r)}};return t&&Hn(r,t),r}function Hn(t,n){if(void 0===n&&(n={}),n.user&&(!t.ipAddress&&n.user.ip_address&&(t.ipAddress=n.user.ip_address),t.did||n.did||(t.did=n.user.id||n.user.email||n.user.username)),t.timestamp=n.timestamp||sn(),n.abnormal_mechanism&&(t.abnormal_mechanism=n.abnormal_mechanism),n.ignoreDuration&&(t.ignoreDuration=n.ignoreDuration),n.sid&&(t.sid=32===n.sid.length?n.sid:pt()),void 0!==n.init&&(t.init=n.init),!t.did&&n.did&&(t.did="".concat(n.did)),"number"==typeof n.started&&(t.started=n.started),t.ignoreDuration)t.duration=void 0;else if("number"==typeof n.duration)t.duration=n.duration;else{var r=t.timestamp-t.started;t.duration=r>=0?r:0}n.release&&(t.release=n.release),n.environment&&(t.environment=n.environment),!t.ipAddress&&n.ipAddress&&(t.ipAddress=n.ipAddress),!t.userAgent&&n.userAgent&&(t.userAgent=n.userAgent),"number"==typeof n.errors&&(t.errors=n.errors),n.status&&(t.status=n.status)}function Un(t,n){var r={};n?r={status:n}:"ok"===t.status&&(r={status:"exited"}),Hn(t,r)}function Fn(t){var n=t.spanContext(),r=n.spanId,e=n.traceId,i=zn(t);return ht({data:i.data,op:i.op,parent_span_id:i.parent_span_id,span_id:r,status:i.status,tags:i.tags,trace_id:e,origin:i.origin})}function $n(t){var n=t.spanContext();return wn(n.traceId,n.spanId,Xn(t))}function Bn(t){return"number"==typeof t?Wn(t):Array.isArray(t)?t[0]+t[1]/1e9:t instanceof Date?Wn(t.getTime()):sn()}function Wn(t){return t>9999999999?t/1e3:t}function zn(t){return function(t){return"function"==typeof t.getSpanJSON}(t)?t.getSpanJSON():"function"==typeof t.toJSON?t.toJSON():{}}function Xn(t){var n=t.spanContext().traceFlags;return Boolean(1&n)}function Gn(t,n,r,i,o,u){var f=t.normalizeDepth,s=void 0===f?3:f,v=t.normalizeMaxBreadth,h=void 0===v?1e3:v,d=e(e({},n),{event_id:n.event_id||r.event_id||pt(),timestamp:n.timestamp||fn()}),l=r.integrations||t.integrations.map((function(t){return t.name}));!function(t,n){var r=n.environment,e=n.release,i=n.dist,o=n.maxValueLength,u=void 0===o?250:o;"environment"in t||(t.environment="environment"in n?r:Dn);void 0===t.release&&void 0!==e&&(t.release=e);void 0===t.dist&&void 0!==i&&(t.dist=i);t.message&&(t.message=S(t.message,u));var a=t.exception&&t.exception.values&&t.exception.values[0];a&&a.value&&(a.value=S(a.value,u));var c=t.request;c&&c.url&&(c.url=S(c.url,u))}(d,t),function(t,n){n.length>0&&(t.sdk=t.sdk||{},t.sdk.integrations=c(c([],a(t.sdk.integrations||[]),!1),a(n),!1))}(d,l),void 0===n.type&&function(t,n){var r,e=M._sentryDebugIds;if(!e)return;var i=Jn.get(n);i?r=i:(r=new Map,Jn.set(n,r));var o=Object.keys(e).reduce((function(t,i){var o,u=r.get(i);u?o=u:(o=n(i),r.set(i,o));for(var a=o.length-1;a>=0;a--){var c=o[a];if(c.filename){t[c.filename]=e[i];break}}return t}),{});try{t.exception.values.forEach((function(t){t.stacktrace.frames.forEach((function(t){t.filename&&(t.debug_id=o[t.filename])}))}))}catch(t){}}(d,t.stackParser);var p=function(t,n){if(!n)return t;var r=t?t.clone():new dr;return r.update(n),r}(i,r.captureContext);r.mechanism&&bt(d,r.mechanism);var m=o&&o.getEventProcessors?o.getEventProcessors():[],y=function(){hr||(hr=new dr);return hr}().getScopeData();u&&sr(y,u.getScopeData());p&&sr(y,p.getScopeData());var g=c(c([],a(r.attachments||[]),!1),a(y.attachments),!1);return g.length&&(r.attachments=g),fr(d,y),qn(c(c(c([],a(m),!1),a(Ln()),!1),a(y.eventProcessors),!1),d,r).then((function(t){return t&&function(t){var n={};try{t.exception.values.forEach((function(t){t.stacktrace.frames.forEach((function(t){t.debug_id&&(t.abs_path?n[t.abs_path]=t.debug_id:t.filename&&(n[t.filename]=t.debug_id),delete t.debug_id)}))}))}catch(t){}if(0===Object.keys(n).length)return;t.debug_meta=t.debug_meta||{},t.debug_meta.images=t.debug_meta.images||[];var r=t.debug_meta.images;Object.keys(n).forEach((function(t){r.push({type:"sourcemap",code_file:t,debug_id:n[t]})}))}(t),"number"==typeof s&&s>0?function(t,n,r){if(!t)return null;var i=e(e(e(e(e({},t),t.breadcrumbs&&{breadcrumbs:t.breadcrumbs.map((function(t){return e(e({},t),t.data&&{data:Qt(t.data,n,r)})}))}),t.user&&{user:Qt(t.user,n,r)}),t.contexts&&{contexts:Qt(t.contexts,n,r)}),t.extra&&{extra:Qt(t.extra,n,r)});t.contexts&&t.contexts.trace&&i.contexts&&(i.contexts.trace=t.contexts.trace,t.contexts.trace.data&&(i.contexts.trace.data=Qt(t.contexts.trace.data,n,r)));t.spans&&(i.spans=t.spans.map((function(t){var e=zn(t).data;return e&&(t.data=Qt(e,n,r)),t})));return i}(t,s,h):t}))}var Jn=new WeakMap;function Vn(t){if(t)return function(t){return t instanceof dr||"function"==typeof t}(t)||function(t){return Object.keys(t).some((function(t){return Yn.includes(t)}))}(t)?{captureContext:t}:t}var Yn=["user","level","extra","contexts","tags","fingerprint","requestSession","propagationContext"];function captureException(t,n){return wr().captureException(t,Vn(n))}function Kn(t,n){return wr().captureEvent(t,n)}function Qn(t,n){wr().addBreadcrumb(t,n)}function Zn(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var r=wr();if(2===t.length){var e=a(t,2),i=e[0],o=e[1];return i?r.withScope((function(){return r.getStackTop().scope=i,o(i)})):r.withScope(o)}return r.withScope(t[0])}function tr(){return wr().getClient()}function nr(){return wr().getScope()}function rr(t){var n=tr(),r=_r(),i=nr(),o=n&&n.getOptions()||{},u=o.release,a=o.environment,c=void 0===a?Dn:a,f=(M.navigator||{}).userAgent,s=Pn(e(e({release:u,environment:c,user:i.getUser()||r.getUser()},f&&{userAgent:f}),t)),v=r.getSession();return v&&"ok"===v.status&&Hn(v,{status:"exited"}),er(),r.setSession(s),i.setSession(s),s}function er(){var t=_r(),n=nr(),r=n.getSession()||t.getSession();r&&Un(r),ir(),t.setSession(),n.setSession()}function ir(){var t=_r(),n=nr(),r=tr(),e=n.getSession()||t.getSession();e&&r&&r.captureSession&&r.captureSession(e)}function or(t){void 0===t&&(t=!1),t?er():ir()}function ur(t){return t.transaction}function ar(t,n,r){var e=n.getOptions(),i=(n.getDsn()||{}).publicKey,o=(r&&r.getUser()||{}).segment,u=ht({environment:e.environment||Dn,release:e.release,user_segment:o,public_key:i,trace_id:t});return n.emit&&n.emit("createDsc",u),u}function cr(t){var n=tr();if(!n)return{};var r=ar(zn(t).trace_id||"",n,nr()),e=ur(t);if(!e)return r;var i=e&&e._frozenDynamicSamplingContext;if(i)return i;var o=e.metadata,u=o.sampleRate,a=o.source;null!=u&&(r.sample_rate="".concat(u));var c=zn(e);return a&&"url"!==a&&(r.transaction=c.description),r.sampled=String(Xn(e)),n.emit&&n.emit("createDsc",r),r}function fr(t,n){var r=n.fingerprint,i=n.span,o=n.breadcrumbs,u=n.sdkProcessingMetadata;!function(t,n){var r=n.extra,i=n.tags,o=n.user,u=n.contexts,a=n.level,c=n.transactionName,f=ht(r);f&&Object.keys(f).length&&(t.extra=e(e({},f),t.extra));var s=ht(i);s&&Object.keys(s).length&&(t.tags=e(e({},s),t.tags));var v=ht(o);v&&Object.keys(v).length&&(t.user=e(e({},v),t.user));var h=ht(u);h&&Object.keys(h).length&&(t.contexts=e(e({},h),t.contexts));a&&(t.level=a);c&&(t.transaction=c)}(t,n),i&&function(t,n){t.contexts=e({trace:Fn(n)},t.contexts);var r=ur(n);if(r){t.sdkProcessingMetadata=e({dynamicSamplingContext:cr(n)},t.sdkProcessingMetadata);var i=zn(r).description;i&&(t.tags=e({transaction:i},t.tags))}}(t,i),function(t,n){t.fingerprint=t.fingerprint?_t(t.fingerprint):[],n&&(t.fingerprint=t.fingerprint.concat(n));t.fingerprint&&!t.fingerprint.length&&delete t.fingerprint}(t,r),function(t,n){var r=c(c([],a(t.breadcrumbs||[]),!1),a(n),!1);t.breadcrumbs=r.length?r:void 0}(t,o),function(t,n){t.sdkProcessingMetadata=e(e({},t.sdkProcessingMetadata),n)}(t,u)}function sr(t,n){var r=n.extra,i=n.tags,o=n.user,u=n.contexts,f=n.level,s=n.sdkProcessingMetadata,v=n.breadcrumbs,h=n.fingerprint,d=n.eventProcessors,l=n.attachments,p=n.propagationContext,m=n.transactionName,y=n.span;vr(t,"extra",r),vr(t,"tags",i),vr(t,"user",o),vr(t,"contexts",u),vr(t,"sdkProcessingMetadata",s),f&&(t.level=f),m&&(t.transactionName=m),y&&(t.span=y),v.length&&(t.breadcrumbs=c(c([],a(t.breadcrumbs),!1),a(v),!1)),h.length&&(t.fingerprint=c(c([],a(t.fingerprint),!1),a(h),!1)),d.length&&(t.eventProcessors=c(c([],a(t.eventProcessors),!1),a(d),!1)),l.length&&(t.attachments=c(c([],a(t.attachments),!1),a(l),!1)),t.propagationContext=e(e({},t.propagationContext),p)}function vr(t,n,r){if(r&&Object.keys(r).length)for(var i in t[n]=e({},t[n]),r)Object.prototype.hasOwnProperty.call(r,i)&&(t[n][i]=r[i])}var hr,dr=function(){function t(){this._=!1,this.S=[],this.T=[],this.O=[],this.j=[],this.k={},this.I={},this.C={},this.R={},this.M={},this.A=lr()}return t.clone=function(n){return n?n.clone():new t},t.prototype.clone=function(){var n=new t;return n.O=c([],a(this.O),!1),n.I=e({},this.I),n.C=e({},this.C),n.R=e({},this.R),n.k=this.k,n.D=this.D,n.L=this.L,n.N=this.N,n.q=this.q,n.P=this.P,n.T=c([],a(this.T),!1),n.H=this.H,n.j=c([],a(this.j),!1),n.M=e({},this.M),n.A=e({},this.A),n.U=this.U,n},t.prototype.setClient=function(t){this.U=t},t.prototype.getClient=function(){return this.U},t.prototype.addScopeListener=function(t){this.S.push(t)},t.prototype.addEventProcessor=function(t){return this.T.push(t),this},t.prototype.setUser=function(t){return this.k=t||{email:void 0,id:void 0,ip_address:void 0,segment:void 0,username:void 0},this.N&&Hn(this.N,{user:t}),this.F(),this},t.prototype.getUser=function(){return this.k},t.prototype.getRequestSession=function(){return this.H},t.prototype.setRequestSession=function(t){return this.H=t,this},t.prototype.setTags=function(t){return this.I=e(e({},this.I),t),this.F(),this},t.prototype.setTag=function(t,n){var r;return this.I=e(e({},this.I),((r={})[t]=n,r)),this.F(),this},t.prototype.setExtras=function(t){return this.C=e(e({},this.C),t),this.F(),this},t.prototype.setExtra=function(t,n){var r;return this.C=e(e({},this.C),((r={})[t]=n,r)),this.F(),this},t.prototype.setFingerprint=function(t){return this.P=t,this.F(),this},t.prototype.setLevel=function(t){return this.D=t,this.F(),this},t.prototype.setTransactionName=function(t){return this.q=t,this.F(),this},t.prototype.setContext=function(t,n){return null===n?delete this.R[t]:this.R[t]=n,this.F(),this},t.prototype.setSpan=function(t){return this.L=t,this.F(),this},t.prototype.getSpan=function(){return this.L},t.prototype.getTransaction=function(){var t=this.L;return t&&t.transaction},t.prototype.setSession=function(t){return t?this.N=t:delete this.N,this.F(),this},t.prototype.getSession=function(){return this.N},t.prototype.update=function(n){if(!n)return this;var r="function"==typeof n?n(this):n;if(r instanceof t){var i=r.getScopeData();this.I=e(e({},this.I),i.tags),this.C=e(e({},this.C),i.extra),this.R=e(e({},this.R),i.contexts),i.user&&Object.keys(i.user).length&&(this.k=i.user),i.level&&(this.D=i.level),i.fingerprint.length&&(this.P=i.fingerprint),r.getRequestSession()&&(this.H=r.getRequestSession()),i.propagationContext&&(this.A=i.propagationContext)}else if(y(r)){var o=n;this.I=e(e({},this.I),o.tags),this.C=e(e({},this.C),o.extra),this.R=e(e({},this.R),o.contexts),o.user&&(this.k=o.user),o.level&&(this.D=o.level),o.fingerprint&&(this.P=o.fingerprint),o.requestSession&&(this.H=o.requestSession),o.propagationContext&&(this.A=o.propagationContext)}return this},t.prototype.clear=function(){return this.O=[],this.I={},this.C={},this.k={},this.R={},this.D=void 0,this.q=void 0,this.P=void 0,this.H=void 0,this.L=void 0,this.N=void 0,this.F(),this.j=[],this.A=lr(),this},t.prototype.addBreadcrumb=function(t,n){var r="number"==typeof n?n:100;if(r<=0)return this;var i=e({timestamp:fn()},t),o=this.O;return o.push(i),this.O=o.length>r?o.slice(-r):o,this.F(),this},t.prototype.getLastBreadcrumb=function(){return this.O[this.O.length-1]},t.prototype.clearBreadcrumbs=function(){return this.O=[],this.F(),this},t.prototype.addAttachment=function(t){return this.j.push(t),this},t.prototype.getAttachments=function(){return this.getScopeData().attachments},t.prototype.clearAttachments=function(){return this.j=[],this},t.prototype.getScopeData=function(){var t=this;return{breadcrumbs:t.O,attachments:t.j,contexts:t.R,tags:t.I,extra:t.C,user:t.k,level:t.D,fingerprint:t.P||[],eventProcessors:t.T,propagationContext:t.A,sdkProcessingMetadata:t.M,transactionName:t.q,span:t.L}},t.prototype.applyToEvent=function(t,n,r){return void 0===n&&(n={}),void 0===r&&(r=[]),fr(t,this.getScopeData()),qn(c(c(c([],a(r),!1),a(Ln()),!1),a(this.T),!1),t,n)},t.prototype.setSDKProcessingMetadata=function(t){return this.M=e(e({},this.M),t),this},t.prototype.setPropagationContext=function(t){return this.A=t,this},t.prototype.getPropagationContext=function(){return this.A},t.prototype.captureException=function(t,n){var r=n&&n.event_id?n.event_id:pt();if(!this.U)return $.warn("No client configured on scope - will not capture exception!"),r;var i=new Error("Sentry syntheticException");return this.U.captureException(t,e(e({originalException:t,syntheticException:i},n),{event_id:r}),this),r},t.prototype.captureMessage=function(t,n,r){var i=r&&r.event_id?r.event_id:pt();if(!this.U)return $.warn("No client configured on scope - will not capture message!"),i;var o=new Error(t);return this.U.captureMessage(t,n,e(e({originalException:t,syntheticException:o},r),{event_id:i}),this),i},t.prototype.captureEvent=function(t,n){var r=n&&n.event_id?n.event_id:pt();return this.U?(this.U.captureEvent(t,e(e({},n),{event_id:r}),this),r):($.warn("No client configured on scope - will not capture event!"),r)},t.prototype.F=function(){var t=this;this._||(this._=!0,this.S.forEach((function(n){n(t)})),this._=!1)},t}();function lr(){return{traceId:pt(),spanId:pt().substring(16)}}var pr="7.120.3",mr=parseFloat(pr),yr=function(){function t(t,n,r,e){var i,o;void 0===e&&(e=mr),this.B=e,n?i=n:(i=new dr).setClient(t),r?o=r:(o=new dr).setClient(t),this.W=[{scope:i}],t&&this.bindClient(t),this.X=o}return t.prototype.isOlderThan=function(t){return this.B<t},t.prototype.bindClient=function(t){var n=this.getStackTop();n.client=t,n.scope.setClient(t),t&&t.setupIntegrations&&t.setupIntegrations()},t.prototype.pushScope=function(){var t=this.getScope().clone();return this.getStack().push({client:this.getClient(),scope:t}),t},t.prototype.popScope=function(){return!(this.getStack().length<=1)&&!!this.getStack().pop()},t.prototype.withScope=function(t){var n,r=this,e=this.pushScope();try{n=t(e)}catch(t){throw this.popScope(),t}return b(n)?n.then((function(t){return r.popScope(),t}),(function(t){throw r.popScope(),t})):(this.popScope(),n)},t.prototype.getClient=function(){return this.getStackTop().client},t.prototype.getScope=function(){return this.getStackTop().scope},t.prototype.getIsolationScope=function(){return this.X},t.prototype.getStack=function(){return this.W},t.prototype.getStackTop=function(){return this.W[this.W.length-1]},t.prototype.captureException=function(t,n){var r=this.G=n&&n.event_id?n.event_id:pt(),i=new Error("Sentry syntheticException");return this.getScope().captureException(t,e(e({originalException:t,syntheticException:i},n),{event_id:r})),r},t.prototype.captureMessage=function(t,n,r){var i=this.G=r&&r.event_id?r.event_id:pt(),o=new Error(t);return this.getScope().captureMessage(t,n,e(e({originalException:t,syntheticException:o},r),{event_id:i})),i},t.prototype.captureEvent=function(t,n){var r=n&&n.event_id?n.event_id:pt();return t.type||(this.G=r),this.getScope().captureEvent(t,e(e({},n),{event_id:r})),r},t.prototype.lastEventId=function(){return this.G},t.prototype.addBreadcrumb=function(t,n){var r=this.getStackTop(),i=r.scope,o=r.client;if(o){var u=o.getOptions&&o.getOptions()||{},a=u.beforeBreadcrumb,c=void 0===a?null:a,f=u.maxBreadcrumbs,s=void 0===f?100:f;if(!(s<=0)){var v=fn(),h=e({timestamp:v},t),d=c?F((function(){return c(h,n)})):h;null!==d&&(o.emit&&o.emit("beforeAddBreadcrumb",d,n),i.addBreadcrumb(d,s))}}},t.prototype.setUser=function(t){this.getScope().setUser(t),this.getIsolationScope().setUser(t)},t.prototype.setTags=function(t){this.getScope().setTags(t),this.getIsolationScope().setTags(t)},t.prototype.setExtras=function(t){this.getScope().setExtras(t),this.getIsolationScope().setExtras(t)},t.prototype.setTag=function(t,n){this.getScope().setTag(t,n),this.getIsolationScope().setTag(t,n)},t.prototype.setExtra=function(t,n){this.getScope().setExtra(t,n),this.getIsolationScope().setExtra(t,n)},t.prototype.setContext=function(t,n){this.getScope().setContext(t,n),this.getIsolationScope().setContext(t,n)},t.prototype.configureScope=function(t){var n=this.getStackTop(),r=n.scope;n.client&&t(r)},t.prototype.run=function(t){var n=br(this);try{t(this)}finally{br(n)}},t.prototype.getIntegration=function(t){var n=this.getClient();if(!n)return null;try{return n.getIntegration(t)}catch(t){return null}},t.prototype.startTransaction=function(t,n){var r=this.J("startTransaction",t,n);return r},t.prototype.traceHeaders=function(){return this.J("traceHeaders")},t.prototype.captureSession=function(t){if(void 0===t&&(t=!1),t)return this.endSession();this.V()},t.prototype.endSession=function(){var t=this.getStackTop().scope,n=t.getSession();n&&Un(n),this.V(),t.setSession()},t.prototype.startSession=function(t){var n=this.getStackTop(),r=n.scope,i=n.client,o=i&&i.getOptions()||{},u=o.release,a=o.environment,c=void 0===a?Dn:a,f=(M.navigator||{}).userAgent,s=Pn(e(e({release:u,environment:c,user:r.getUser()},f&&{userAgent:f}),t)),v=r.getSession&&r.getSession();return v&&"ok"===v.status&&Hn(v,{status:"exited"}),this.endSession(),r.setSession(s),s},t.prototype.shouldSendDefaultPii=function(){var t=this.getClient(),n=t&&t.getOptions();return Boolean(n&&n.sendDefaultPii)},t.prototype.V=function(){var t=this.getStackTop(),n=t.scope,r=t.client,e=n.getSession();e&&r&&r.captureSession&&r.captureSession(e)},t.prototype.J=function(t){for(var n=[],r=1;r<arguments.length;r++)n[r-1]=arguments[r];var e=gr(),i=e.__SENTRY__;if(i&&i.extensions&&"function"==typeof i.extensions[t])return i.extensions[t].apply(this,n)},t}();function gr(){return M.__SENTRY__=M.__SENTRY__||{extensions:{},hub:void 0},M}function br(t){var n=gr(),r=Sr(n);return Tr(n,t),r}function wr(){var t=gr();if(t.__SENTRY__&&t.__SENTRY__.acs){var n=t.__SENTRY__.acs.getCurrentHub();if(n)return n}return function(t){void 0===t&&(t=gr());n=t,n&&n.__SENTRY__&&n.__SENTRY__.hub&&!Sr(t).isOlderThan(mr)||Tr(t,new yr);var n;return Sr(t)}(t)}function _r(){return wr().getIsolationScope()}function Er(t,n){void 0===n&&(n={});var r=gr();return r.__SENTRY__&&r.__SENTRY__.acs?r.__SENTRY__.acs.runWithAsyncContext(t,n):t()}function Sr(t){return D("hub",(function(){return new yr}),t)}function Tr(t,n){return!!t&&((t.__SENTRY__=t.__SENTRY__||{}).hub=n,!0)}function Or(t){return(t||wr()).getScope().getTransaction()}var jr,kr=!1;function xr(){var t=Or();if(t){t.setStatus("internal_error")}}function Ir(t){return jr?jr.get(t):void 0}function Cr(t){var n,r,e=Ir(t);if(e){var i={};try{for(var o=u(e),c=o.next();!c.done;c=o.next()){var f=a(c.value,2),s=a(f[1],2),v=s[0],h=s[1];i[v]||(i[v]=[]),i[v].push(ht(h))}}catch(t){n={error:t}}finally{try{c&&!c.done&&(r=o.return)&&r.call(o)}finally{if(n)throw n.error}}return i}}xr.tag="sentry_tracingErrorCallback";var Rr,Mr="sentry.source",Ar="sentry.sample_rate",Dr="sentry.op",Lr="sentry.origin";function Nr(t,n){t.setTag("http.status_code",String(n)),t.setData("http.response.status_code",n);var r=function(t){if(t<400&&t>=100)return"ok";if(t>=400&&t<500)switch(t){case 401:return"unauthenticated";case 403:return"permission_denied";case 404:return"not_found";case 409:return"already_exists";case 413:return"failed_precondition";case 429:return"resource_exhausted";default:return"invalid_argument"}if(t>=500&&t<600)switch(t){case 501:return"unimplemented";case 503:return"unavailable";case 504:return"deadline_exceeded";default:return"internal_error"}return"unknown_error"}(n);"unknown_error"!==r&&t.setStatus(r)}!function(t){t.Ok="ok",t.DeadlineExceeded="deadline_exceeded",t.Unauthenticated="unauthenticated",t.PermissionDenied="permission_denied",t.NotFound="not_found",t.ResourceExhausted="resource_exhausted",t.InvalidArgument="invalid_argument",t.Unimplemented="unimplemented",t.Unavailable="unavailable",t.InternalError="internal_error",t.UnknownError="unknown_error",t.Cancelled="cancelled",t.AlreadyExists="already_exists",t.FailedPrecondition="failed_precondition",t.Aborted="aborted",t.OutOfRange="out_of_range",t.DataLoss="data_loss"}(Rr||(Rr={}));var qr=function(){function t(t){void 0===t&&(t=1e3),this.Y=t,this.spans=[]}return t.prototype.add=function(t){this.spans.length>this.Y?t.spanRecorder=void 0:this.spans.push(t)},t}(),Pr=function(){function t(t){var n;void 0===t&&(t={}),this.K=t.traceId||pt(),this.Z=t.spanId||pt().substring(16),this.tt=t.startTimestamp||sn(),this.tags=t.tags?e({},t.tags):{},this.data=t.data?e({},t.data):{},this.instrumenter=t.instrumenter||"sentry",this.nt={},this.setAttributes(e(((n={})["sentry.origin"]=t.origin||"manual",n["sentry.op"]=t.op,n),t.attributes)),this.rt=t.name||t.description,t.parentSpanId&&(this.et=t.parentSpanId),"sampled"in t&&(this.it=t.sampled),t.status&&(this.ot=t.status),t.endTimestamp&&(this.ut=t.endTimestamp),void 0!==t.exclusiveTime&&(this.ct=t.exclusiveTime),this.ft=t.measurements?e({},t.measurements):{}}return Object.defineProperty(t.prototype,"name",{get:function(){return this.rt||""},set:function(t){this.updateName(t)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"description",{get:function(){return this.rt},set:function(t){this.rt=t},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"traceId",{get:function(){return this.K},set:function(t){this.K=t},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"spanId",{get:function(){return this.Z},set:function(t){this.Z=t},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"parentSpanId",{get:function(){return this.et},set:function(t){this.et=t},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"sampled",{get:function(){return this.it},set:function(t){this.it=t},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"attributes",{get:function(){return this.nt},set:function(t){this.nt=t},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"startTimestamp",{get:function(){return this.tt},set:function(t){this.tt=t},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"endTimestamp",{get:function(){return this.ut},set:function(t){this.ut=t},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"status",{get:function(){return this.ot},set:function(t){this.ot=t},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"op",{get:function(){return this.nt["sentry.op"]},set:function(t){this.setAttribute(Dr,t)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"origin",{get:function(){return this.nt["sentry.origin"]},set:function(t){this.setAttribute(Lr,t)},enumerable:!1,configurable:!0}),t.prototype.spanContext=function(){var t=this;return{spanId:t.Z,traceId:t.K,traceFlags:t.it?1:0}},t.prototype.startChild=function(n){var r=new t(e(e({},n),{parentSpanId:this.Z,sampled:this.it,traceId:this.K}));r.spanRecorder=this.spanRecorder,r.spanRecorder&&r.spanRecorder.add(r);var i=ur(this);return r.transaction=i,r},t.prototype.setTag=function(t,n){var r;return this.tags=e(e({},this.tags),((r={})[t]=n,r)),this},t.prototype.setData=function(t,n){var r;return this.data=e(e({},this.data),((r={})[t]=n,r)),this},t.prototype.setAttribute=function(t,n){void 0===n?delete this.nt[t]:this.nt[t]=n},t.prototype.setAttributes=function(t){var n=this;Object.keys(t).forEach((function(r){return n.setAttribute(r,t[r])}))},t.prototype.setStatus=function(t){return this.ot=t,this},t.prototype.setHttpStatus=function(t){return Nr(this,t),this},t.prototype.setName=function(t){this.updateName(t)},t.prototype.updateName=function(t){return this.rt=t,this},t.prototype.isSuccess=function(){return"ok"===this.ot},t.prototype.finish=function(t){return this.end(t)},t.prototype.end=function(t){if(!this.ut){ur(this);this.ut=Bn(t)}},t.prototype.toTraceparent=function(){return $n(this)},t.prototype.toContext=function(){return ht({data:this.st(),description:this.rt,endTimestamp:this.ut,op:this.op,parentSpanId:this.et,sampled:this.it,spanId:this.Z,startTimestamp:this.tt,status:this.ot,tags:this.tags,traceId:this.K})},t.prototype.updateWithContext=function(t){return this.data=t.data||{},this.rt=t.name||t.description,this.ut=t.endTimestamp,this.op=t.op,this.et=t.parentSpanId,this.it=t.sampled,this.Z=t.spanId||this.Z,this.tt=t.startTimestamp||this.tt,this.ot=t.status,this.tags=t.tags||{},this.K=t.traceId||this.K,this},t.prototype.getTraceContext=function(){return Fn(this)},t.prototype.getSpanJSON=function(){return ht({data:this.st(),description:this.rt,op:this.nt["sentry.op"],parent_span_id:this.et,span_id:this.Z,start_timestamp:this.tt,status:this.ot,tags:Object.keys(this.tags).length>0?this.tags:void 0,timestamp:this.ut,trace_id:this.K,origin:this.nt["sentry.origin"],vt:Cr(this),profile_id:this.nt.profile_id,exclusive_time:this.ct,measurements:Object.keys(this.ft).length>0?this.ft:void 0})},t.prototype.isRecording=function(){return!this.ut&&!!this.it},t.prototype.toJSON=function(){return this.getSpanJSON()},t.prototype.st=function(){var t=this.data,n=this.nt,r=Object.keys(t).length>0,i=Object.keys(n).length>0;if(r||i)return r&&i?e(e({},t),n):r?t:n},t}();function Hr(t,n,r){var e;void 0===r&&(r=function(){});try{e=t()}catch(t){throw n(t),r(),t}return function(t,n,r){if(b(t))return t.then((function(t){return r(),t}),(function(t){throw n(t),r(),t}));return r(),t}(e,n,r)}function Ur(t){if("boolean"==typeof __SENTRY_TRACING__&&!__SENTRY_TRACING__)return!1;var n=tr(),r=t||n&&n.getOptions();return!!r&&(r.enableTracing||"tracesSampleRate"in r||"tracesSampler"in r)}function Fr(t){if(Ur()){var n=Wr(t),r=wr(),e=t.scope?t.scope.getSpan():$r();if(!(t.onlyIfParent&&!e)){var i=(t.scope||nr()).clone();return Br(r,{parentSpan:e,spanContext:n,forceTransaction:t.forceTransaction,scope:i})}}}function $r(){return nr().getSpan()}function Br(t,n){var r=n.parentSpan,i=n.spanContext,o=n.forceTransaction,u=n.scope;if(Ur()){var a,c=_r();if(r&&!o)a=r.startChild(i);else if(r){var f=cr(r),s=r.spanContext(),v=s.traceId,h=s.spanId,d=Xn(r);a=t.startTransaction(e(e({traceId:v,parentSpanId:h,parentSampled:d},i),{metadata:e({dynamicSamplingContext:f},i.metadata)}))}else{var l=e(e({},c.getPropagationContext()),u.getPropagationContext());v=l.traceId,f=l.dsc,h=l.parentSpanId,d=l.sampled;a=t.startTransaction(e(e({traceId:v,parentSpanId:h,parentSampled:d},i),{metadata:e({dynamicSamplingContext:f},i.metadata)}))}return u.setSpan(a),function(t,n,r){t&&(ut(t,Xr,r),ut(t,zr,n))}(a,u,c),a}}function Wr(t){if(t.startTime){var n=e({},t);return n.startTimestamp=Bn(t.startTime),delete n.startTime,n}return t}var zr="_sentryScope",Xr="_sentryIsolationScope";var Gr=function(t){function n(n,r){var i=t.call(this,n)||this;i.R={},i.ht=r||wr(),i.rt=n.name||"",i.dt=e({},n.metadata),i.lt=n.trimEnd,i.transaction=i;var o=i.dt.dynamicSamplingContext;return o&&(i._frozenDynamicSamplingContext=e({},o)),i}return r(n,t),Object.defineProperty(n.prototype,"name",{get:function(){return this.rt},set:function(t){this.setName(t)},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"metadata",{get:function(){return e(e(e({source:"custom",spanMetadata:{}},this.dt),this.nt["sentry.source"]&&{source:this.nt["sentry.source"]}),this.nt["sentry.sample_rate"]&&{sampleRate:this.nt["sentry.sample_rate"]})},set:function(t){this.dt=t},enumerable:!1,configurable:!0}),n.prototype.setName=function(t,n){void 0===n&&(n="custom"),this.rt=t,this.setAttribute(Mr,n)},n.prototype.updateName=function(t){return this.rt=t,this},n.prototype.initSpanRecorder=function(t){void 0===t&&(t=1e3),this.spanRecorder||(this.spanRecorder=new qr(t)),this.spanRecorder.add(this)},n.prototype.setContext=function(t,n){null===n?delete this.R[t]:this.R[t]=n},n.prototype.setMeasurement=function(t,n,r){void 0===r&&(r=""),this.ft[t]={value:n,unit:r}},n.prototype.setMetadata=function(t){this.dt=e(e({},this.dt),t)},n.prototype.end=function(t){var n=Bn(t),r=this.yt(n);if(r)return this.ht.captureEvent(r)},n.prototype.toContext=function(){var n=t.prototype.toContext.call(this);return ht(e(e({},n),{name:this.rt,trimEnd:this.lt}))},n.prototype.updateWithContext=function(n){return t.prototype.updateWithContext.call(this,n),this.rt=n.name||"",this.lt=n.trimEnd,this},n.prototype.getDynamicSamplingContext=function(){return cr(this)},n.prototype.setHub=function(t){this.ht=t},n.prototype.getProfileId=function(){if(void 0!==this.R&&void 0!==this.R.profile)return this.R.profile.profile_id},n.prototype.yt=function(n){var r=this;if(void 0===this.ut){this.rt||(this.rt="<unlabeled transaction>"),t.prototype.end.call(this,n);var i=this.ht.getClient();if(i&&i.emit&&i.emit("finishTransaction",this),!0===this.it){var o=this.spanRecorder?this.spanRecorder.spans.filter((function(t){return t!==r&&zn(t).timestamp})):[];if(this.lt&&o.length>0){var u=o.map((function(t){return zn(t).timestamp})).filter(Boolean);this.ut=u.reduce((function(t,n){return t>n?t:n}))}var a,c={scope:(a=this).gt,isolationScope:a.bt},f=c.scope,s=c.isolationScope,v=this.metadata,h=v.source,d=e({contexts:e(e({},this.R),{trace:Fn(this)}),spans:o,start_timestamp:this.tt,tags:this.tags,timestamp:this.ut,transaction:this.rt,type:"transaction",sdkProcessingMetadata:e(e(e({},v),{capturedSpanScope:f,capturedSpanIsolationScope:s}),ht({dynamicSamplingContext:cr(this)})),vt:Cr(this)},h&&{transaction_info:{source:h}});return Object.keys(this.ft).length>0&&(d.measurements=this.ft),d}i&&i.recordDroppedEvent("sample_rate","transaction")}},n}(Pr),Jr={idleTimeout:1e3,finalTimeout:3e4,heartbeatInterval:5e3},Vr=["heartbeatFailed","idleTimeout","documentHidden","finalTimeout","externalFinish","cancelled"],Yr=function(t){function n(n,r,e,i){var o=t.call(this,i)||this;return o.wt=n,o._t=r,o.transactionSpanId=e,o}return r(n,t),n.prototype.add=function(n){var r=this;if(n.spanContext().spanId!==this.transactionSpanId){var e=n.end;n.end=function(){for(var t=[],i=0;i<arguments.length;i++)t[i]=arguments[i];return r._t(n.spanContext().spanId),e.apply(n,t)},void 0===zn(n).timestamp&&this.wt(n.spanContext().spanId)}t.prototype.add.call(this,n)},n}(qr),Kr=function(t){function n(n,r,e,i,o,u,a){void 0===e&&(e=Jr.idleTimeout),void 0===i&&(i=Jr.finalTimeout),void 0===o&&(o=Jr.heartbeatInterval),void 0===u&&(u=!1),void 0===a&&(a=!1);var c=t.call(this,n,r)||this;return c.Et=r,c.St=e,c.Tt=i,c.Ot=o,c.jt=u,c.activities={},c.kt=0,c.xt=!1,c.It=!1,c.Ct=[],c.Rt=Vr[4],c.Mt=!a,u&&r.getScope().setSpan(c),a||c.At(),setTimeout((function(){c.xt||(c.setStatus("deadline_exceeded"),c.Rt=Vr[3],c.end())}),c.Tt),c}return r(n,t),n.prototype.end=function(n){var r,e,i=this,o=Bn(n);if(this.xt=!0,this.activities={},"ui.action.click"===this.op&&this.setAttribute("finishReason",this.Rt),this.spanRecorder){try{for(var a=u(this.Ct),c=a.next();!c.done;c=a.next()){(0,c.value)(this,o)}}catch(t){r={error:t}}finally{try{c&&!c.done&&(e=a.return)&&e.call(a)}finally{if(r)throw r.error}}this.spanRecorder.spans=this.spanRecorder.spans.filter((function(t){if(t.spanContext().spanId===i.spanContext().spanId)return!0;zn(t).timestamp||(t.setStatus("cancelled"),t.end(o));var n=zn(t),r=n.start_timestamp,e=n.timestamp,u=r&&r<o,a=(i.Tt+i.St)/1e3;return u&&(e&&r&&e-r<a)}))}if(this.jt){var f=this.Et.getScope();f.getTransaction()===this&&f.setSpan(void 0)}return t.prototype.end.call(this,n)},n.prototype.registerBeforeFinishCallback=function(t){this.Ct.push(t)},n.prototype.initSpanRecorder=function(t){var n=this;if(!this.spanRecorder){this.spanRecorder=new Yr((function(t){n.xt||n.wt(t)}),(function(t){n.xt||n._t(t)}),this.spanContext().spanId,t),this.Dt()}this.spanRecorder.add(this)},n.prototype.cancelIdleTimeout=function(t,n){var r=(void 0===n?{restartOnChildSpanChange:!0}:n).restartOnChildSpanChange;this.It=!1===r,this.Lt&&(clearTimeout(this.Lt),this.Lt=void 0,0===Object.keys(this.activities).length&&this.It&&(this.Rt=Vr[5],this.end(t)))},n.prototype.setFinishReason=function(t){this.Rt=t},n.prototype.sendAutoFinishSignal=function(){this.Mt||(this.At(),this.Mt=!0)},n.prototype.At=function(t){var n=this;this.cancelIdleTimeout(),this.Lt=setTimeout((function(){n.xt||0!==Object.keys(n.activities).length||(n.Rt=Vr[1],n.end(t))}),this.St)},n.prototype.wt=function(t){this.cancelIdleTimeout(void 0,{restartOnChildSpanChange:!this.It}),this.activities[t]=!0},n.prototype._t=function(t){if(this.activities[t]&&delete this.activities[t],0===Object.keys(this.activities).length){var n=sn();this.It?this.Mt&&(this.Rt=Vr[5],this.end(n)):this.At(n+this.St/1e3)}},n.prototype.Nt=function(){if(!this.xt){var t=Object.keys(this.activities).join("");t===this.qt?this.kt++:this.kt=1,this.qt=t,this.kt>=3?this.Mt&&(this.setStatus("deadline_exceeded"),this.Rt=Vr[0],this.end()):this.Dt()}},n.prototype.Dt=function(){var t=this;setTimeout((function(){t.Nt()}),this.Ot)},n}(Gr);function Qr(t,n,r){return Ur(n)?void 0!==t.sampled?(t.setAttribute(Ar,Number(t.sampled)),t):("function"==typeof n.tracesSampler?(e=n.tracesSampler(r),t.setAttribute(Ar,Number(e))):void 0!==r.parentSampled?e=r.parentSampled:void 0!==n.tracesSampleRate?(e=n.tracesSampleRate,t.setAttribute(Ar,Number(e))):(e=1,t.setAttribute(Ar,e)),Zr(e)&&e?(t.sampled=Math.random()<e,t.sampled,t):(t.sampled=!1,t)):(t.sampled=!1,t);var e}function Zr(t){return!(w(t)||"number"!=typeof t&&"boolean"!=typeof t)&&!(t<0||t>1)}function te(){var t=this.getScope().getSpan();return t?{"sentry-trace":$n(t)}:{}}function ne(t,n){var r=this.getClient(),i=r&&r.getOptions()||{};(i.instrumenter||"sentry")!==(t.instrumenter||"sentry")&&(t.sampled=!1);var o=new Gr(t,this);return(o=Qr(o,i,e({name:t.name,parentSampled:t.parentSampled,transactionContext:t,attributes:e(e({},t.data),t.attributes)},n))).isRecording()&&o.initSpanRecorder(i._experiments&&i._experiments.maxSpans),r&&r.emit&&r.emit("startTransaction",o),o}function re(t,n,r,i,o,u,a,c){void 0===c&&(c=!1);var f=t.getClient(),s=f&&f.getOptions()||{},v=new Kr(n,t,r,i,a,o,c);return(v=Qr(v,s,e({name:n.name,parentSampled:n.parentSampled,transactionContext:n,attributes:e(e({},n.data),n.attributes)},u))).isRecording()&&v.initSpanRecorder(s._experiments&&s._experiments.maxSpans),f&&f.emit&&f.emit("startTransaction",v),v}function ee(){var t=gr();t.__SENTRY__&&(t.__SENTRY__.extensions=t.__SENTRY__.extensions||{},t.__SENTRY__.extensions.startTransaction||(t.__SENTRY__.extensions.startTransaction=ne),t.__SENTRY__.extensions.traceHeaders||(t.__SENTRY__.extensions.traceHeaders=te),kr||(kr=!0,Pt(xr),Ft(xr)))}function ie(t,n,r,i){var o=In(r),u=t.type&&"replay_event"!==t.type?t.type:"event";!function(t,n){n&&(t.sdk=t.sdk||{},t.sdk.name=t.sdk.name||n.name,t.sdk.version=t.sdk.version||n.version,t.sdk.integrations=c(c([],a(t.sdk.integrations||[]),!1),a(n.integrations||[]),!1),t.sdk.packages=c(c([],a(t.sdk.packages||[]),!1),a(n.packages||[]),!1))}(t,r&&r.sdk);var f=function(t,n,r,i){var o=t.sdkProcessingMetadata&&t.sdkProcessingMetadata.dynamicSamplingContext;return e(e(e({event_id:t.event_id,sent_at:(new Date).toISOString()},n&&{sdk:n}),!!r&&i&&{dsn:W(i)}),o&&{trace:ht(e({},o))})}(t,o,i,n);return delete t.sdkProcessingMetadata,_n(f,[[{type:u},t]])}function oe(t){var n=t.protocol?"".concat(t.protocol,":"):"",r=t.port?":".concat(t.port):"";return"".concat(n,"//").concat(t.host).concat(r).concat(t.path?"/".concat(t.path):"","/api/")}function ue(t,n){return r=e({sentry_key:t.publicKey,sentry_version:"7"},n&&{sentry_client:"".concat(n.name,"/").concat(n.version)}),Object.keys(r).map((function(t){return"".concat(encodeURIComponent(t),"=").concat(encodeURIComponent(r[t]))})).join("&");var r}function ae(t,n){void 0===n&&(n={});var r="string"==typeof n?n:n.tunnel,e="string"!=typeof n&&n.dt?n.dt.sdk:void 0;return r||"".concat(function(t){return"".concat(oe(t)).concat(t.projectId,"/envelope/")}(t),"?").concat(ue(t,e))}var ce=[];function fe(t){var n=t.defaultIntegrations||[],r=t.integrations;n.forEach((function(t){t.isDefaultInstance=!0}));var e=function(t){var n={};return t.forEach((function(t){var r=t.name,e=n[r];e&&!e.isDefaultInstance&&t.isDefaultInstance||(n[r]=t)})),Object.keys(n).map((function(t){return n[t]}))}(Array.isArray(r)?c(c([],a(n),!1),a(r),!1):"function"==typeof r?_t(r(n)):n),i=function(t,n){for(var r=0;r<t.length;r++)if(!0===n(t[r]))return r;return-1}(e,(function(t){return"Debug"===t.name}));if(-1!==i){var o=a(e.splice(i,1),1)[0];e.push(o)}return e}function se(t,n){var r,e;try{for(var i=u(n),o=i.next();!o.done;o=i.next()){var a=o.value;a&&a.afterAllSetup&&a.afterAllSetup(t)}}catch(t){r={error:t}}finally{try{o&&!o.done&&(e=i.return)&&e.call(i)}finally{if(r)throw r.error}}}function ve(t,n,r){if(!r[n.name]){if(r[n.name]=n,-1===ce.indexOf(n.name)&&(n.setupOnce(Nn,wr),ce.push(n.name)),n.setup&&"function"==typeof n.setup&&n.setup(t),t.on&&"function"==typeof n.preprocessEvent){var e=n.preprocessEvent.bind(n);t.on("preprocessEvent",(function(n,r){return e(n,r,t)}))}if(t.addEventProcessor&&"function"==typeof n.processEvent){var i=n.processEvent.bind(n),o=Object.assign((function(n,r){return i(n,r,t)}),{id:n.name});t.addEventProcessor(o)}}}function he(t,n){return Object.assign((function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return n.apply(void 0,c([],a(t),!1))}),{id:t})}function de(t){return t.replace(/[^\w\-./]+/gi,"")}var le=[["\n","\\n"],["\r","\\r"],["\t","\\t"],["\\","\\\\"],["|","\\u{7c}"],[",","\\u{2c}"]];function pe(t){return c([],a(t),!1).reduce((function(t,n){return t+function(t){var n,r;try{for(var e=u(le),i=e.next();!i.done;i=e.next()){var o=a(i.value,2),c=o[0],f=o[1];if(t===c)return f}}catch(t){n={error:t}}finally{try{i&&!i.done&&(r=e.return)&&r.call(e)}finally{if(n)throw n.error}}return t}(n)}),"")}function me(t,n,r,e){var i={sent_at:(new Date).toISOString()};r&&r.sdk&&(i.sdk={name:r.sdk.name,version:r.sdk.version}),e&&n&&(i.dsn=W(n));var o=function(t){var n=function(t){var n,r,e="";try{for(var i=u(t),o=i.next();!o.done;o=i.next()){var c=o.value,f=Object.entries(c.tags),s=f.length>0?"|#".concat(f.map((function(t){var n=a(t,2),r=n[0],e=n[1];return"".concat(r,":").concat(e)})).join(",")):"";e+="".concat(c.name,"@").concat(c.unit,":").concat(c.metric,"|").concat(c.metricType).concat(s,"|T").concat(c.timestamp,"\n")}}catch(t){n={error:t}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}return e}(t);return[{type:"statsd",length:n.length},n]}(t);return _n(i,[o])}var ye=function(){function t(t){if(this.Pt=t,this._integrations={},this.Ht=!1,this.Ut=0,this.Ft={},this.$t={},this.T=[],t.dsn&&(this.Bt=X(t.dsn)),this.Bt){var n=ae(this.Bt,t);this.Wt=t.transport(e(e({tunnel:this.Pt.tunnel,recordDroppedEvent:this.recordDroppedEvent.bind(this)},t.transportOptions),{url:n}))}}return t.prototype.captureException=function(t,n,r){var e=this;if(!wt(t)){var i=n&&n.event_id;return this.zt(this.eventFromException(t,n).then((function(t){return e.Xt(t,n,r)})).then((function(t){i=t}))),i}},t.prototype.captureMessage=function(t,n,r,e){var i=this,o=r&&r.event_id,u=p(t)?t:String(t),a=m(t)?this.eventFromMessage(u,n,r):this.eventFromException(t,r);return this.zt(a.then((function(t){return i.Xt(t,r,e)})).then((function(t){o=t}))),o},t.prototype.captureEvent=function(t,n,r){if(!(n&&n.originalException&&wt(n.originalException))){var e=n&&n.event_id,i=(t.sdkProcessingMetadata||{}).capturedSpanScope;return this.zt(this.Xt(t,n,i||r).then((function(t){e=t}))),e}},t.prototype.captureSession=function(t){"string"!=typeof t.release||(this.sendSession(t),Hn(t,{init:!1}))},t.prototype.getDsn=function(){return this.Bt},t.prototype.getOptions=function(){return this.Pt},t.prototype.getSdkMetadata=function(){return this.Pt.dt},t.prototype.getTransport=function(){return this.Wt},t.prototype.flush=function(t){var n=this.Wt;return n?(this.metricsAggregator&&this.metricsAggregator.flush(),this.Gt(t).then((function(r){return n.flush(t).then((function(t){return r&&t}))}))):nn(!0)},t.prototype.close=function(t){var n=this;return this.flush(t).then((function(t){return n.getOptions().enabled=!1,n.metricsAggregator&&n.metricsAggregator.close(),t}))},t.prototype.getEventProcessors=function(){return this.T},t.prototype.addEventProcessor=function(t){this.T.push(t)},t.prototype.setupIntegrations=function(t){(t&&!this.Ht||this.Jt()&&!this.Ht)&&this.Vt()},t.prototype.init=function(){this.Jt()&&this.Vt()},t.prototype.getIntegrationById=function(t){return this.getIntegrationByName(t)},t.prototype.getIntegrationByName=function(t){return this._integrations[t]},t.prototype.getIntegration=function(t){try{return this._integrations[t.id]||null}catch(t){return null}},t.prototype.addIntegration=function(t){var n=this._integrations[t.name];ve(this,t,this._integrations),n||se(this,[t])},t.prototype.sendEvent=function(t,n){var r,e,i=this;void 0===n&&(n={}),this.emit("beforeSendEvent",t,n);var o=ie(t,this.Bt,this.Pt.dt,this.Pt.tunnel);try{for(var a=u(n.attachments||[]),c=a.next();!c.done;c=a.next()){o=En(o,jn(c.value,this.Pt.transportOptions&&this.Pt.transportOptions.textEncoder))}}catch(t){r={error:t}}finally{try{c&&!c.done&&(e=a.return)&&e.call(a)}finally{if(r)throw r.error}}var f=this.Yt(o);f&&f.then((function(n){return i.emit("afterSendEvent",t,n)}),null)},t.prototype.sendSession=function(t){var n=function(t,n,r,i){var o=In(r);return _n(e(e({sent_at:(new Date).toISOString()},o&&{sdk:o}),!!i&&n&&{dsn:W(n)}),["aggregates"in t?[{type:"sessions"},t]:[{type:"session"},t.toJSON()]])}(t,this.Bt,this.Pt.dt,this.Pt.tunnel);this.Yt(n)},t.prototype.recordDroppedEvent=function(t,n,r){if(this.Pt.sendClientReports){var e="number"==typeof r?r:1,i="".concat(t,":").concat(n);this.Ft[i]=(this.Ft[i]||0)+e}},t.prototype.captureAggregateMetrics=function(t){var n=me(t,this.Bt,this.Pt.dt,this.Pt.tunnel);this.Yt(n)},t.prototype.on=function(t,n){this.$t[t]||(this.$t[t]=[]),this.$t[t].push(n)},t.prototype.emit=function(t){for(var n=[],r=1;r<arguments.length;r++)n[r-1]=arguments[r];this.$t[t]&&this.$t[t].forEach((function(t){return t.apply(void 0,c([],a(n),!1))}))},t.prototype.Vt=function(){var t=this.Pt.integrations;this._integrations=function(t,n){var r={};return n.forEach((function(n){n&&ve(t,n,r)})),r}(this,t),se(this,t),this.Ht=!0},t.prototype.Kt=function(t,n){var r,i,o=!1,a=!1,c=n.exception&&n.exception.values;if(c){a=!0;try{for(var f=u(c),s=f.next();!s.done;s=f.next()){var v=s.value.mechanism;if(v&&!1===v.handled){o=!0;break}}}catch(t){r={error:t}}finally{try{s&&!s.done&&(i=f.return)&&i.call(f)}finally{if(r)throw r.error}}}var h="ok"===t.status;(h&&0===t.errors||h&&o)&&(Hn(t,e(e({},o&&{status:"crashed"}),{errors:t.errors||Number(a||o)})),this.captureSession(t))},t.prototype.Gt=function(t){var n=this;return new en((function(r){var e=0,i=setInterval((function(){0==n.Ut?(clearInterval(i),r(!0)):(e+=1,t&&e>=t&&(clearInterval(i),r(!1)))}),1)}))},t.prototype.Jt=function(){return!1!==this.getOptions().enabled&&void 0!==this.Wt},t.prototype.Qt=function(t,n,r,i){var o=this;void 0===i&&(i=_r());var u=this.getOptions(),a=Object.keys(this._integrations);return!n.integrations&&a.length>0&&(n.integrations=a),this.emit("preprocessEvent",t,n),Gn(u,t,n,r,this,i).then((function(t){if(null===t)return t;var n=e(e({},i.getPropagationContext()),r?r.getPropagationContext():void 0);if(!(t.contexts&&t.contexts.trace)&&n){var u=n.traceId,a=n.spanId,c=n.parentSpanId,f=n.dsc;t.contexts=e({trace:{trace_id:u,span_id:a,parent_span_id:c}},t.contexts);var s=f||ar(u,o,r);t.sdkProcessingMetadata=e({dynamicSamplingContext:s},t.sdkProcessingMetadata)}return t}))},t.prototype.Xt=function(t,n,r){return void 0===n&&(n={}),this.Zt(t,n,r).then((function(t){return t.event_id}),(function(t){}))},t.prototype.Zt=function(t,n,r){var i=this,o=this.getOptions(),u=o.sampleRate,a=be(t),c=ge(t),f=t.type||"error",s="before send for type `".concat(f,"`");if(c&&"number"==typeof u&&Math.random()>u)return this.recordDroppedEvent("sample_rate","error",t),rn(new G("Discarding event because it's not included in the random sample (sampling rate = ".concat(u,")"),"log"));var v="replay_event"===f?"replay":f,h=(t.sdkProcessingMetadata||{}).capturedSpanIsolationScope;return this.Qt(t,n,r,h).then((function(r){if(null===r)throw i.recordDroppedEvent("event_processor",v,t),new G("An event processor returned `null`, will not send event.","log");if(n.data&&!0===n.data.__sentry__)return r;var u=function(t,n,r){var i=t.beforeSend,o=t.beforeSendTransaction;if(ge(n)&&i)return i(n,r);if(be(n)&&o){if(n.spans){var u=n.spans.length;n.sdkProcessingMetadata=e(e({},n.sdkProcessingMetadata),{spanCountBeforeProcessing:u})}return o(n,r)}return n}(o,r,n);return function(t,n){var r="".concat(n," must return `null` or a valid event.");if(b(t))return t.then((function(t){if(!y(t)&&null!==t)throw new G(r);return t}),(function(t){throw new G("".concat(n," rejected with ").concat(t))}));if(!y(t)&&null!==t)throw new G(r);return t}(u,s)})).then((function(o){if(null===o){if(i.recordDroppedEvent("before_send",v,t),a){var u=1+(t.spans||[]).length;i.recordDroppedEvent("before_send","span",u)}throw new G("".concat(s," returned `null`, will not send event."),"log")}var c=r&&r.getSession();if(!a&&c&&i.Kt(c,o),a){var f=(o.sdkProcessingMetadata&&o.sdkProcessingMetadata.spanCountBeforeProcessing||0)-(o.spans?o.spans.length:0);f>0&&i.recordDroppedEvent("before_send","span",f)}var h=o.transaction_info;if(a&&h&&o.transaction!==t.transaction){o.transaction_info=e(e({},h),{source:"custom"})}return i.sendEvent(o,n),o})).then(null,(function(t){if(t instanceof G)throw t;throw i.captureException(t,{data:{__sentry__:!0},originalException:t}),new G("Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.\nReason: ".concat(t))}))},t.prototype.zt=function(t){var n=this;this.Ut++,t.then((function(t){return n.Ut--,t}),(function(t){return n.Ut--,t}))},t.prototype.Yt=function(t){if(this.emit("beforeEnvelope",t),this.Jt()&&this.Wt)return this.Wt.send(t).then(null,(function(t){}))},t.prototype.tn=function(){var t=this.Ft;return this.Ft={},Object.keys(t).map((function(n){var r=a(n.split(":"),2);return{reason:r[0],category:r[1],quantity:t[n]}}))},t}();function ge(t){return void 0===t.type}function be(t){return"transaction"===t.type}var we,_e=function(){function t(t){this.l=t}return Object.defineProperty(t.prototype,"weight",{get:function(){return 1},enumerable:!1,configurable:!0}),t.prototype.add=function(t){this.l+=t},t.prototype.toString=function(){return"".concat(this.l)},t}(),Ee=function(){function t(t){this.nn=t,this.rn=t,this.en=t,this.un=t,this.an=1}return Object.defineProperty(t.prototype,"weight",{get:function(){return 5},enumerable:!1,configurable:!0}),t.prototype.add=function(t){this.nn=t,t<this.rn&&(this.rn=t),t>this.en&&(this.en=t),this.un+=t,this.an++},t.prototype.toString=function(){return"".concat(this.nn,":").concat(this.rn,":").concat(this.en,":").concat(this.un,":").concat(this.an)},t}(),Se=function(){function t(t){this.l=[t]}return Object.defineProperty(t.prototype,"weight",{get:function(){return this.l.length},enumerable:!1,configurable:!0}),t.prototype.add=function(t){this.l.push(t)},t.prototype.toString=function(){return this.l.join(":")},t}(),Te=function(){function t(t){this.first=t,this.l=new Set([t])}return Object.defineProperty(t.prototype,"weight",{get:function(){return this.l.size},enumerable:!1,configurable:!0}),t.prototype.add=function(t){this.l.add(t)},t.prototype.toString=function(){return Array.from(this.l).map((function(t){return"string"==typeof t?function(t){for(var n=0,r=0;r<t.length;r++)n=(n<<5)-n+t.charCodeAt(r),n&=n;return n>>>0}(t):t})).join(":")},t}(),Oe=((we={}).c=_e,we.g=Ee,we.d=Se,we.s=Te,we);function je(t){var n=wr().getStackTop();n.client=t,n.scope.setClient(t)}function ke(t,n,r){void 0===r&&(r=on(t.bufferSize||30));var e={};function i(i){var o=[];if(Sn(i,(function(n,r){var i=xn(r);if(function(t,n,r){return void 0===r&&(r=Date.now()),function(t,n){return t[n]||t.all||0}(t,n)>r}(e,i)){var u=xe(n,r);t.recordDroppedEvent("ratelimit_backoff",i,u)}else o.push(n)})),0===o.length)return nn();var u=_n(i[0],o),a=function(n){Sn(u,(function(r,e){var i=xe(r,e);t.recordDroppedEvent(n,xn(e),i)}))};return r.add((function(){return n({body:On(u,t.textEncoder)}).then((function(t){return e=Cn(e,t),t}),(function(t){throw a("network_error"),t}))})).then((function(t){return t}),(function(t){if(t instanceof G)return a("queue_overflow"),nn();throw t}))}return i.__sentry__baseTransport__=!0,{send:i,flush:function(t){return r.drain(t)}}}function xe(t,n){if("event"===n||"transaction"===n)return Array.isArray(t)?t[1]:void 0}function Ie(t){return[{type:"span"},t]}var Ce,Re=[/^Script error\.?$/,/^Javascript error: Script error\.? on line 0$/,/^ResizeObserver loop completed with undelivered notifications.$/,/^Cannot redefine property: googletag$/],Me=[/^.*\/healthcheck$/,/^.*\/healthy$/,/^.*\/live$/,/^.*\/ready$/,/^.*\/heartbeat$/,/^.*\/health$/,/^.*\/healthz$/],Ae="InboundFilters",De=function(t){return void 0===t&&(t={}),{name:Ae,setupOnce:function(){},processEvent:function(n,r,e){var i=e.getOptions(),o=function(t,n){void 0===t&&(t={});void 0===n&&(n={});return{allowUrls:c(c([],a(t.allowUrls||[]),!1),a(n.allowUrls||[]),!1),denyUrls:c(c([],a(t.denyUrls||[]),!1),a(n.denyUrls||[]),!1),ignoreErrors:c(c(c([],a(t.ignoreErrors||[]),!1),a(n.ignoreErrors||[]),!1),a(t.disableErrorDefaults?[]:Re),!1),ignoreTransactions:c(c(c([],a(t.ignoreTransactions||[]),!1),a(n.ignoreTransactions||[]),!1),a(t.disableTransactionDefaults?[]:Me),!1),ignoreInternal:void 0===t.ignoreInternal||t.ignoreInternal}}(t,i);return function(t,n){if(n.ignoreInternal&&function(t){try{return"SentryError"===t.exception.values[0].type}catch(t){}return!1}(t))return!0;if(function(t,n){if(t.type||!n||!n.length)return!1;return function(t){var n,r=[];t.message&&r.push(t.message);try{n=t.exception.values[t.exception.values.length-1]}catch(t){}n&&n.value&&(r.push(n.value),n.type&&r.push("".concat(n.type,": ").concat(n.value)));return r}(t).some((function(t){return j(t,n)}))}(t,n.ignoreErrors))return!0;if(function(t,n){if("transaction"!==t.type||!n||!n.length)return!1;var r=t.transaction;return!!r&&j(r,n)}(t,n.ignoreTransactions))return!0;if(function(t,n){if(!n||!n.length)return!1;var r=Ne(t);return!!r&&j(r,n)}(t,n.denyUrls))return!0;if(!function(t,n){if(!n||!n.length)return!0;var r=Ne(t);return!r||j(r,n)}(t,n.allowUrls))return!0;return!1}(n,o)?null:n}}},Le=he(Ae,De);function Ne(t){try{var n;try{n=t.exception.values[0].stacktrace.frames}catch(t){}return n?function(t){void 0===t&&(t=[]);for(var n=t.length-1;n>=0;n--){var r=t[n];if(r&&"<anonymous>"!==r.filename&&"[native code]"!==r.filename)return r.filename||null}return null}(n):null}catch(t){return null}}var qe="FunctionToString",Pe=new WeakMap,He=function(){return{name:qe,setupOnce:function(){Ce=Function.prototype.toString;try{Function.prototype.toString=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var r=ct(this),e=Pe.has(tr())&&void 0!==r?r:this;return Ce.apply(e,t)}}catch(t){}},setup:function(t){Pe.set(t,!0)}}},Ue=he(qe,He),Fe="LinkedErrors",$e=he(Fe,(function(t){void 0===t&&(t={});var n=t.limit||5,r=t.key||"cause";return{name:Fe,setupOnce:function(){},preprocessEvent:function(t,e,i){var o=i.getOptions();k(Rn,o.stackParser,o.maxValueLength,r,n,t,e)}}})),Be=Object.freeze({__proto__:null,FunctionToString:Ue,InboundFilters:Le,LinkedErrors:$e}),We=function(){function t(t){var n=this;this.U=t,this.cn=new Map,this.fn=setInterval((function(){return n.flush()}),5e3)}return t.prototype.add=function(t,n,r,e,i,o){void 0===e&&(e="none"),void 0===i&&(i={}),void 0===o&&(o=sn());var u=Math.floor(o),c=n.replace(/[^\w\-.]+/gi,"_"),f=function(t){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(n[de(r)]=pe(String(t[r])));return n}(i),s=function(t){return t.replace(/[^\w]+/gi,"_")}(e),v=function(t,n,r,e){var i=Object.entries(ht(e)).sort((function(t,n){return t[0].localeCompare(n[0])}));return"".concat(t).concat(n).concat(r).concat(i)}(t,c,s,f),h=this.cn.get(v),d=h&&"s"===t?h.metric.weight:0;h?(h.metric.add(r),h.timestamp<u&&(h.timestamp=u)):(h={metric:new Oe[t](r),timestamp:u,metricType:t,name:c,unit:s,tags:f},this.cn.set(v,h)),function(t,n,r,e,i,o){var u=$r();if(u){var c=Ir(u)||new Map,f="".concat(t,":").concat(n,"@").concat(e),s=c.get(o);if(s){var v=a(s,2)[1];c.set(o,[f,{min:Math.min(v.min,r),max:Math.max(v.max,r),count:v.count+=1,sum:v.sum+=r,tags:v.tags}])}else c.set(o,[f,{min:r,max:r,count:1,sum:r,tags:i}]);jr||(jr=new WeakMap),jr.set(u,c)}}(t,c,"string"==typeof r?h.metric.weight-d:r,s,i,v)},t.prototype.flush=function(){if(0!==this.cn.size){if(this.U.captureAggregateMetrics){var t=Array.from(this.cn).map((function(t){return a(t,2)[1]}));this.U.captureAggregateMetrics(t)}this.cn.clear()}},t.prototype.close=function(){clearInterval(this.fn),this.flush()},t}(),ze="MetricsAggregator",Xe=function(){return{name:ze,setupOnce:function(){},setup:function(t){t.metricsAggregator=new We(t)}}};function Ge(t,n,r,i){void 0===i&&(i={});var o=tr(),u=nr();if(o){if(!o.metricsAggregator)return;var a=i.unit,c=i.tags,f=i.timestamp,s=o.getOptions(),v=s.release,h=s.environment,d=u.getTransaction(),l={};v&&(l.release=v),h&&(l.environment=h),d&&(l.transaction=zn(d).description||""),o.metricsAggregator.add(t,n,r,a,e(e({},l),c),f)}}var Je={increment:function(t,n,r){void 0===n&&(n=1),Ge("c",t,n,r)},distribution:function(t,n,r){Ge("d",t,n,r)},set:function(t,n,r){Ge("s",t,n,r)},gauge:function(t,n,r){Ge("g",t,n,r)},MetricsAggregator:he(ze,Xe),metricsAggregatorIntegration:Xe},Ve=Be,Ye=M;var Ke,Qe,Ze,ti,ni,ri,ei=function(t,n,r){var e,i;return function(o){n.value>=0&&(o||r)&&((i=n.value-(e||0))||void 0===e)&&(e=n.value,n.delta=i,t(n))}},ii=function(){return Ye.__WEB_VITALS_POLYFILL__?Ye.performance&&(performance.getEntriesByType&&performance.getEntriesByType("navigation")[0]||function(){var t=Ye.performance.timing,n=Ye.performance.navigation.type,r={entryType:"navigation",startTime:0,type:2==n?"back_forward":1===n?"reload":"navigate"};for(var e in t)"navigationStart"!==e&&"toJSON"!==e&&(r[e]=Math.max(t[e]-t.navigationStart,0));return r}()):Ye.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0]},oi=function(){var t=ii();return t&&t.activationStart||0},ui=function(t,n){var r=ii(),e="navigate";return r&&(e=Ye.document&&Ye.document.prerendering||oi()>0?"prerender":r.type.replace(/_/g,"-")),{name:t,value:void 0===n?-1:n,rating:"good",delta:0,entries:[],id:"v3-".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:e}},ai=function(t,n,r){try{if(PerformanceObserver.supportedEntryTypes.includes(t)){var e=new PerformanceObserver((function(t){n(t.getEntries())}));return e.observe(Object.assign({type:t,buffered:!0},r||{})),e}}catch(t){}},ci=function(t,n){var r=function(e){"pagehide"!==e.type&&"hidden"!==Ye.document.visibilityState||(t(e),n&&(removeEventListener("visibilitychange",r,!0),removeEventListener("pagehide",r,!0)))};Ye.document&&(addEventListener("visibilitychange",r,!0),addEventListener("pagehide",r,!0))},fi=-1,si=function(){return fi<0&&(Ye.document&&Ye.document.visibilityState&&(fi="hidden"!==Ye.document.visibilityState||Ye.document.prerendering?1/0:0),ci((function(t){var n=t.timeStamp;fi=n}),!0)),{get firstHiddenTime(){return fi}}},vi=0,hi=1/0,di=0,li=function(t){t.forEach((function(t){t.interactionId&&(hi=Math.min(hi,t.interactionId),di=Math.max(di,t.interactionId),vi=di?(di-hi)/7+1:0)}))},pi=function(){"interactionCount"in performance||Ke||(Ke=ai("event",li,{type:"event",buffered:!0,durationThreshold:0}))},mi=function(){return Ke?vi:performance.interactionCount||0},yi=[],gi={},bi=function(t){var n=yi[yi.length-1],r=gi[t.interactionId];if(r||yi.length<10||t.duration>n.latency){if(r)r.entries.push(t),r.latency=Math.max(r.latency,t.duration);else{var e={id:t.interactionId,latency:t.duration,entries:[t]};gi[e.id]=e,yi.push(e)}yi.sort((function(t,n){return n.latency-t.latency})),yi.splice(10).forEach((function(t){delete gi[t.id]}))}},wi=function(t,n){n=n||{},pi();var r,e=ui("INP"),i=function(t){t.forEach((function(t){(t.interactionId&&bi(t),"first-input"===t.entryType)&&(!yi.some((function(n){return n.entries.some((function(n){return t.duration===n.duration&&t.startTime===n.startTime}))}))&&bi(t))}));var n,i=(n=Math.min(yi.length-1,Math.floor(mi()/50)),yi[n]);i&&i.latency!==e.value&&(e.value=i.latency,e.entries=i.entries,r())},o=ai("event",i,{durationThreshold:n.durationThreshold||40});r=ei(t,e,n.reportAllChanges),o&&(o.observe({type:"first-input",buffered:!0}),ci((function(){i(o.takeRecords()),e.value<0&&mi()>0&&(e.value=0,e.entries=[]),r(!0)})))},_i={},Ei=function(t){Ye.document&&(Ye.document.prerendering?addEventListener("prerenderingchange",(function(){return Ei(t)}),!0):"complete"!==Ye.document.readyState?addEventListener("load",(function(){return Ei(t)}),!0):setTimeout(t,0))},Si={},Ti={};function Oi(t,n){return Ai(t,n),Ti[t]||(!function(t){var n={};"event"===t&&(n.durationThreshold=0);ai(t,(function(n){ji(t,{entries:n})}),n)}(t),Ti[t]=!0),Di(t,n)}function ji(t,n){var r,e,i=Si[t];if(i&&i.length)try{for(var o=u(i),a=o.next();!a.done;a=o.next()){var c=a.value;try{c(n)}catch(t){}}}catch(t){r={error:t}}finally{try{a&&!a.done&&(e=o.return)&&e.call(o)}finally{if(r)throw r.error}}}function ki(){return function(t,n){void 0===n&&(n={});var r,e=ui("CLS",0),i=0,o=[],u=function(t){t.forEach((function(t){if(!t.hadRecentInput){var n=o[0],u=o[o.length-1];i&&0!==o.length&&t.startTime-u.startTime<1e3&&t.startTime-n.startTime<5e3?(i+=t.value,o.push(t)):(i=t.value,o=[t]),i>e.value&&(e.value=i,e.entries=o,r&&r())}}))},a=ai("layout-shift",u);if(a){r=ei(t,e,n.reportAllChanges);var c=function(){u(a.takeRecords()),r(!0)};return ci(c),c}}((function(t){ji("cls",{metric:t}),Qe=t}),{reportAllChanges:!0})}function xi(){return function(t){var n,r=si(),e=ui("FID"),i=function(t){t.startTime<r.firstHiddenTime&&(e.value=t.processingStart-t.startTime,e.entries.push(t),n(!0))},o=function(t){t.forEach(i)},u=ai("first-input",o);n=ei(t,e),u&&ci((function(){o(u.takeRecords()),u.disconnect()}),!0)}((function(t){ji("fid",{metric:t}),Ze=t}))}function Ii(){return function(t){var n,r=si(),e=ui("LCP"),i=function(t){var i=t[t.length-1];if(i){var o=Math.max(i.startTime-oi(),0);o<r.firstHiddenTime&&(e.value=o,e.entries=[i],n())}},o=ai("largest-contentful-paint",i);if(o){n=ei(t,e);var u=function(){_i[e.id]||(i(o.takeRecords()),o.disconnect(),_i[e.id]=!0,n(!0))};return["keydown","click"].forEach((function(t){Ye.document&&addEventListener(t,u,{once:!0,capture:!0})})),ci(u,!0),u}}((function(t){ji("lcp",{metric:t}),ti=t}))}function Ci(){return function(t,n){n=n||{};var r=ui("TTFB"),e=ei(t,r,n.reportAllChanges);Ei((function(){var t=ii();if(t){if(r.value=Math.max(t.responseStart-oi(),0),r.value<0||r.value>performance.now())return;r.entries=[t],e(!0)}}))}((function(t){ji("ttfb",{metric:t}),ni=t}))}function Ri(){return wi((function(t){ji("inp",{metric:t}),ri=t}))}function Mi(t,n,r,e,i){var o;return void 0===i&&(i=!1),Ai(t,n),Ti[t]||(o=r(),Ti[t]=!0),e&&n({metric:e}),Di(t,n,i?o:void 0)}function Ai(t,n){Si[t]=Si[t]||[],Si[t].push(n)}function Di(t,n,r){return function(){r&&r();var e=Si[t];if(e){var i=e.indexOf(n);-1!==i&&e.splice(i,1)}}}function Li(t){return"number"==typeof t&&isFinite(t)}function Ni(t,n){var r=n.startTimestamp,i=function(t,n){var r={};for(var e in t)Object.prototype.hasOwnProperty.call(t,e)&&n.indexOf(e)<0&&(r[e]=t[e]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(e=Object.getOwnPropertySymbols(t);i<e.length;i++)n.indexOf(e[i])<0&&Object.prototype.propertyIsEnumerable.call(t,e[i])&&(r[e[i]]=t[e[i]])}return r}(n,["startTimestamp"]);return r&&t.startTimestamp>r&&(t.startTimestamp=r),t.startChild(e({startTimestamp:r},i))}function qi(t){return t/1e3}function Pi(){return Ye&&Ye.addEventListener&&Ye.performance}var Hi,Ui,Fi=0,$i={};function Bi(){var t,n=Pi();if(n&&vn){n.mark&&Ye.performance.mark("sentry-tracing-init");var r=(t=function(t){var n=t.metric,r=n.entries[n.entries.length-1];if(r){var e=qi(vn),i=qi(r.startTime);$i.fid={value:n.value,unit:"millisecond"},$i["mark.fid"]={value:e+i,unit:"second"}}},Mi("fid",t,xi,Ze)),e=function(t,n){return void 0===n&&(n=!1),Mi("cls",t,ki,Qe,n)}((function(t){var n=t.metric,r=n.entries[n.entries.length-1];r&&($i.cls={value:n.value,unit:""},Ui=r)}),!0),i=function(t,n){return void 0===n&&(n=!1),Mi("lcp",t,Ii,ti,n)}((function(t){var n=t.metric,r=n.entries[n.entries.length-1];r&&($i.lcp={value:n.value,unit:"millisecond"},Hi=r)}),!0),o=function(t){return Mi("ttfb",t,Ci,ni)}((function(t){var n=t.metric;n.entries[n.entries.length-1]&&($i.ttfb={value:n.value,unit:"millisecond"})}));return function(){r(),e(),i(),o()}}return function(){}}function Wi(t,n){if(Pi()&&vn){var r=function(t,n){return r=function(r){var i=r.metric;if(void 0!==i.value){var o=i.entries.find((function(t){return t.duration===i.value&&void 0!==zi[t.name]})),u=tr();if(o&&u){var a=zi[o.name],c=u.getOptions(),f=qi(vn+o.startTime),s=qi(i.value),v=void 0!==o.interactionId?t[o.interactionId]:void 0;if(void 0!==v){var h=v.routeName,d=v.parentContext,l=v.activeTransaction,p=v.user,m=v.replayId,y=void 0!==p?p.email||p.id||p.ip_address:void 0,g=void 0!==l?l.getProfileId():void 0,b=new Pr({startTimestamp:f,endTimestamp:f+s,op:"ui.interaction.".concat(a),name:N(o.target),attributes:e(e(e({release:c.release,environment:c.environment,transaction:h},void 0!==y&&""!==y?{user:y}:{}),void 0!==g?{profile_id:g}:{}),void 0!==m?{replay_id:m}:{}),exclusiveTime:i.value,measurements:{inp:{value:i.value,unit:"millisecond"}}}),w=function(t,n,r){return!!Ur(n)&&(!!Zr(i=void 0!==t&&"function"==typeof n.tracesSampler?n.tracesSampler({transactionContext:t,name:t.name,parentSampled:t.parentSampled,attributes:e(e({},t.data),t.attributes),location:Ye.location}):void 0!==t&&void 0!==t.sampled?t.sampled:void 0!==n.tracesSampleRate?n.tracesSampleRate:1)&&(!0===i?r:!1===i?0:i*r));var i}(d,c,n);if(w)if(Math.random()<w){var _=b?(S=[b],T=u.getDsn(),O={sent_at:(new Date).toISOString()},T&&(O.dsn=W(T)),_n(O,S.map(Ie))):void 0,E=u&&u.getTransport();E&&_&&E.send(_).then(null,(function(t){}))}else var S,T,O}}}},Mi("inp",r,Ri,ri);var r}(t,n);return function(){r()}}return function(){}}var zi={click:"click",pointerdown:"click",pointerup:"click",mousedown:"click",mouseup:"click",touchstart:"click",touchend:"click",mouseover:"hover",mouseout:"hover",mouseenter:"hover",mouseleave:"hover",pointerover:"hover",pointerout:"hover",pointerenter:"hover",pointerleave:"hover",dragstart:"drag",dragend:"drag",drag:"drag",dragenter:"drag",dragleave:"drag",dragover:"drag",drop:"drag",keydown:"press",keyup:"press",keypress:"press",input:"press"};function Xi(t){var n=Pi();if(n&&Ye.performance.getEntries&&vn){var r=qi(vn),e=n.getEntries(),i=zn(t),o=i.op,u=i.start_timestamp;if(e.slice(Fi).forEach((function(n){var e=qi(n.startTime),i=qi(n.duration);if(!("navigation"===t.op&&u&&r+e<u))switch(n.entryType){case"navigation":!function(t,n,r){["unloadEvent","redirect","domContentLoadedEvent","loadEvent","connect"].forEach((function(e){Gi(t,n,e,r)})),Gi(t,n,"secureConnection",r,"TLS/SSL","connectEnd"),Gi(t,n,"fetch",r,"cache","domainLookupStart"),Gi(t,n,"domainLookup",r,"DNS"),function(t,n,r){n.responseEnd&&(Ni(t,{op:"browser",origin:"auto.browser.browser.metrics",description:"request",startTimestamp:r+qi(n.requestStart),endTimestamp:r+qi(n.responseEnd)}),Ni(t,{op:"browser",origin:"auto.browser.browser.metrics",description:"response",startTimestamp:r+qi(n.responseStart),endTimestamp:r+qi(n.responseEnd)}))}(t,n,r)}(t,n,r);break;case"mark":case"paint":case"measure":!function(t,n,r,e,i){var o=i+r,u=o+e;Ni(t,{description:n.name,endTimestamp:u,op:n.entryType,origin:"auto.resource.browser.metrics",startTimestamp:o})}(t,n,e,i,r);var o=si(),a=n.startTime<o.firstHiddenTime;"first-paint"===n.name&&a&&($i.fp={value:n.startTime,unit:"millisecond"}),"first-contentful-paint"===n.name&&a&&($i.fcp={value:n.startTime,unit:"millisecond"});break;case"resource":!function(t,n,r,e,i,o){if("xmlhttprequest"===n.initiatorType||"fetch"===n.initiatorType)return;var u=un(r),a={};Ji(a,n,"transferSize","http.response_transfer_size"),Ji(a,n,"encodedBodySize","http.response_content_length"),Ji(a,n,"decodedBodySize","http.decoded_response_content_length"),"renderBlockingStatus"in n&&(a["resource.render_blocking_status"]=n.renderBlockingStatus);u.protocol&&(a["url.scheme"]=u.protocol.split(":").pop());u.host&&(a["server.address"]=u.host);a["url.same_origin"]=r.includes(Ye.location.origin);var c=o+e,f=c+i;Ni(t,{description:r.replace(Ye.location.origin,""),endTimestamp:f,op:n.initiatorType?"resource.".concat(n.initiatorType):"resource.other",origin:"auto.resource.browser.metrics",startTimestamp:c,data:a})}(t,n,n.name,e,i,r)}})),Fi=Math.max(e.length-1,0),function(t){var n=Ye.navigator;if(!n)return;var r=n.connection;r&&(r.effectiveType&&t.setTag("effectiveConnectionType",r.effectiveType),r.type&&t.setTag("connectionType",r.type),Li(r.rtt)&&($i["connection.rtt"]={value:r.rtt,unit:"millisecond"}));Li(n.deviceMemory)&&t.setTag("deviceMemory","".concat(n.deviceMemory," GB"));Li(n.hardwareConcurrency)&&t.setTag("hardwareConcurrency",String(n.hardwareConcurrency))}(t),"pageload"===o){!function(t){var n=ii();if(!n)return;var r=n.responseStart,e=n.requestStart;e<=r&&(t["ttfb.requestTime"]={value:r-e,unit:"millisecond"})}($i),["fcp","fp","lcp"].forEach((function(t){if($i[t]&&u&&!(r>=u)){var n=$i[t].value,e=r+qi(n),i=Math.abs(1e3*(e-u));$i[t].value=i}}));var a=$i["mark.fid"];a&&$i.fid&&(Ni(t,{description:"first input delay",endTimestamp:a.value+qi($i.fid.value),op:"ui.action",origin:"auto.ui.browser.metrics",startTimestamp:a.value}),delete $i["mark.fid"]),"fcp"in $i||delete $i.cls,Object.keys($i).forEach((function(t){!function(t,n,r){var e=Or();e&&e.setMeasurement(t,n,r)}(t,$i[t].value,$i[t].unit)})),function(t){Hi&&(Hi.element&&t.setTag("lcp.element",N(Hi.element)),Hi.id&&t.setTag("lcp.id",Hi.id),Hi.url&&t.setTag("lcp.url",Hi.url.trim().slice(0,200)),t.setTag("lcp.size",Hi.size));Ui&&Ui.sources&&Ui.sources.forEach((function(n,r){return t.setTag("cls.source.".concat(r+1),N(n.node))}))}(t)}Hi=void 0,Ui=void 0,$i={}}}function Gi(t,n,r,e,i,o){var u=o?n[o]:n["".concat(r,"End")],a=n["".concat(r,"Start")];a&&u&&Ni(t,{op:"browser",origin:"auto.browser.browser.metrics",description:i||r,startTimestamp:e+qi(a),endTimestamp:e+qi(u)})}function Ji(t,n,r,e){var i=n[r];null!=i&&i<2147483647&&(t[e]=i)}function Vi(t,n,r,i,o){var u;if(void 0===o&&(o="auto.http.browser"),Ur()&&t.fetchData){var f=n(t.fetchData.url);if(!t.endTimestamp||!f){var s=nr(),v=tr(),h=t.fetchData,d=h.method,l=h.url,p=function(t){try{return new URL(t).href}catch(t){return}}(l),m=p?un(p).host:void 0,y=f?Fr({name:"".concat(d," ").concat(l),onlyIfParent:!0,attributes:(u={url:l,type:"fetch","http.method":d,"http.url":p,"server.address":m},u["sentry.origin"]=o,u),op:"http.client"}):void 0;if(y&&(t.fetchData.__span=y.spanContext().spanId,i[y.spanContext().spanId]=y),r(t.fetchData.url)&&v){var g=t.args[0];t.args[1]=t.args[1]||{};var b=t.args[1];b.headers=function(t,n,r,i,o){var u=o||r.getSpan(),f=_r(),s=e(e({},f.getPropagationContext()),r.getPropagationContext()),v=s.traceId,h=s.spanId,d=s.sampled,l=s.dsc,p=u?$n(u):wn(v,h,d),m=mn(l||(u?cr(u):ar(v,n,r))),y=i.headers||("undefined"!=typeof Request&&_(t,Request)?t.headers:void 0);if(y){if("undefined"!=typeof Headers&&_(y,Headers))return(g=new Headers(y)).append("sentry-trace",p),m&&g.append(hn,m),g;if(Array.isArray(y)){var g=c(c([],a(y),!1),[["sentry-trace",p]],!1);return m&&g.push([hn,m]),g}var b="baggage"in y?y.baggage:void 0,w=[];return Array.isArray(b)?w.push.apply(w,c([],a(b),!1)):b&&w.push(b),m&&w.push(m),e(e({},y),{"sentry-trace":p,baggage:w.length>0?w.join(","):void 0})}return{"sentry-trace":p,baggage:m}}(g,v,s,b,y)}return y}var w=t.fetchData.__span;if(!w)return;var E=i[w];E&&(!function(t,n){if(n.response){Nr(t,n.response.status);var r=n.response&&n.response.headers&&n.response.headers.get("content-length");if(r){var e=parseInt(r);e>0&&t.setAttribute("http.response_content_length",e)}}else n.error&&t.setStatus("internal_error");t.end()}(E,t),delete i[w])}}var Yi=["localhost",/^\/(?!\/)/],Ki={traceFetch:!0,traceXHR:!0,enableHTTPTimings:!0,tracingOrigins:Yi,tracePropagationTargets:Yi};function Qi(t){var n=e({traceFetch:Ki.traceFetch,traceXHR:Ki.traceXHR},t),r=n.traceFetch,i=n.traceXHR,o=n.tracePropagationTargets,u=n.tracingOrigins,a=n.shouldCreateSpanForRequest,c=n.enableHTTPTimings,f="function"==typeof a?a:function(t){return!0},s=function(t){return function(t,n){return j(t,n||Yi)}(t,o||u)},v={};r&&Mt((function(t){var n=Vi(t,f,s,v);if(n){var r=no(t.fetchData.url),e=r?un(r).host:void 0;n.setAttributes({"http.url":r,"server.address":e})}c&&n&&Zi(n)})),i&&Vt((function(t){var n=function(t,n,r,i){var o,u=t.xhr,a=u&&u.__sentry_xhr_v3__;if(!Ur()||!u||u.__sentry_own_request__||!a)return;var c=n(a.url);if(t.endTimestamp&&c){if(!(g=u.__sentry_xhr_span_id__))return;var f=i[g];return void(f&&void 0!==a.status_code&&(Nr(f,a.status_code),f.end(),delete i[g]))}var s=nr(),v=_r(),h=no(a.url),d=h?un(h).host:void 0,l=c?Fr({name:"".concat(a.method," ").concat(a.url),onlyIfParent:!0,attributes:(o={type:"xhr","http.method":a.method,"http.url":h,url:a.url,"server.address":d},o["sentry.origin"]="auto.http.browser",o),op:"http.client"}):void 0;l&&(u.__sentry_xhr_span_id__=l.spanContext().spanId,i[u.__sentry_xhr_span_id__]=l);var p=tr();if(u.setRequestHeader&&r(a.url)&&p){var m=e(e({},v.getPropagationContext()),s.getPropagationContext()),y=m.traceId,g=m.spanId,b=m.sampled,w=m.dsc;!function(t,n,r){try{t.setRequestHeader("sentry-trace",n),r&&t.setRequestHeader(hn,r)}catch(t){}}(u,l?$n(l):wn(y,g,b),mn(w||(l?cr(l):ar(y,p,s))))}return l}(t,f,s,v);c&&n&&Zi(n)}))}function Zi(t){var n=(zn(t).data||{}).url;if(n&&"string"==typeof n)var r=Oi("resource",(function(e){e.entries.forEach((function(e){if(function(t){return"resource"===t.entryType&&"initiatorType"in t&&"string"==typeof t.nextHopProtocol&&("fetch"===t.initiatorType||"xmlhttprequest"===t.initiatorType)}(e)&&e.name.endsWith(n)){var i=function(t){var n=function(t){var n,r,e,i="unknown",o="unknown",c="";try{for(var f=u(t),s=f.next();!s.done;s=f.next()){var v=s.value;if("/"===v){i=(e=a(t.split("/"),2))[0],o=e[1];break}if(!isNaN(Number(v))){i="h"===c?"http":c,o=t.split(c)[1];break}c+=v}}catch(t){n={error:t}}finally{try{s&&!s.done&&(r=f.return)&&r.call(f)}finally{if(n)throw n.error}}c===t&&(i=c);return{name:i,version:o}}(t.nextHopProtocol),r=n.name,e=n.version,i=[];if(i.push(["network.protocol.version",e],["network.protocol.name",r]),!vn)return i;return c(c([],a(i),!1),[["http.request.redirect_start",to(t.redirectStart)],["http.request.fetch_start",to(t.fetchStart)],["http.request.domain_lookup_start",to(t.domainLookupStart)],["http.request.domain_lookup_end",to(t.domainLookupEnd)],["http.request.connect_start",to(t.connectStart)],["http.request.secure_connection_start",to(t.secureConnectionStart)],["http.request.connection_end",to(t.connectEnd)],["http.request.request_start",to(t.requestStart)],["http.request.response_start",to(t.responseStart)],["http.request.response_end",to(t.responseEnd)]],!1)}(e);i.forEach((function(n){return t.setAttribute.apply(t,c([],a(n),!1))})),setTimeout(r)}}))}))}function to(t){return void 0===t&&(t=0),((vn||performance.timeOrigin)+t)/1e3}function no(t){try{return new URL(t,Ye.location.origin).href}catch(t){return}}var ro=e(e(e({},Jr),{markBackgroundTransactions:!0,routingInstrumentation:function(t,n,r){if(void 0===n&&(n=!0),void 0===r&&(r=!0),Ye&&Ye.location){var e,i=Ye.location.href;n&&(e=t({name:Ye.location.pathname,startTimestamp:vn?vn/1e3:void 0,op:"pageload",origin:"auto.pageload.browser",metadata:{source:"url"}})),r&&zt((function(n){var r=n.to,o=n.from;void 0===o&&i&&-1!==i.indexOf(r)?i=void 0:o!==r&&(i=void 0,e&&e.end(),e=t({name:Ye.location.pathname,op:"navigation",origin:"auto.navigation.browser",metadata:{source:"url"}}))}))}},startTransactionOnLocationChange:!0,startTransactionOnPageLoad:!0,enableLongTask:!0,enableInp:!1,interactionsSampleRate:1,_experiments:{}}),Ki),eo=function(){function t(t){this.name="BrowserTracing",this.sn=!1,ee(),this.options=e(e({},ro),t),void 0!==this.options._experiments.enableLongTask&&(this.options.enableLongTask=this.options._experiments.enableLongTask),t&&!t.tracePropagationTargets&&t.tracingOrigins&&(this.options.tracePropagationTargets=t.tracingOrigins),this.vn=Bi(),this.hn={},this.options.enableInp&&Wi(this.hn,this.options.interactionsSampleRate),this.options.enableLongTask&&Oi("longtask",(function(t){var n,r,e=t.entries;try{for(var i=u(e),o=i.next();!o.done;o=i.next()){var a=o.value,c=Or();if(!c)return;var f=qi(vn+a.startTime),s=qi(a.duration);c.startChild({description:"Main UI thread blocked",op:"ui.long-task",origin:"auto.ui.browser.metrics",startTimestamp:f,endTimestamp:f+s})}}catch(t){n={error:t}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}})),this.options._experiments.enableInteractions&&Oi("event",(function(t){var n,r,e=t.entries;try{for(var i=u(e),o=i.next();!o.done;o=i.next()){var a=o.value,c=Or();if(!c)return;if("click"===a.name){var f=qi(vn+a.startTime),s=qi(a.duration),v={description:N(a.target),op:"ui.interaction.".concat(a.name),origin:"auto.ui.browser.metrics",startTimestamp:f,endTimestamp:f+s},h=P(a.target);h&&(v.attributes={"ui.component_name":h}),c.startChild(v)}}}catch(t){n={error:t}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}})),this.dn={name:void 0,context:void 0}}return t.prototype.setupOnce=function(t,n){var r=this;this.ln=n;var e=n().getClient(),i=e&&e.getOptions(),o=this.options,u=o.routingInstrumentation,a=o.startTransactionOnLocationChange,c=o.startTransactionOnPageLoad,f=o.markBackgroundTransactions,s=o.traceFetch,v=o.traceXHR,h=o.shouldCreateSpanForRequest,d=o.enableHTTPTimings,l=o._experiments,p=i&&i.tracePropagationTargets,m=p||this.options.tracePropagationTargets;u((function(t){var e=r.pn(t);return r.options._experiments.onStartRouteTransaction&&r.options._experiments.onStartRouteTransaction(e,t,n),e}),c,a),f&&Ye.document&&Ye.document.addEventListener("visibilitychange",(function(){var t=Or();Ye.document.hidden&&t&&(zn(t).status||t.setStatus("cancelled"),t.setTag("visibilitychange","document.hidden"),t.end())})),l.enableInteractions&&this.mn(),this.options.enableInp&&this.yn(),Qi({traceFetch:s,traceXHR:v,tracePropagationTargets:m,shouldCreateSpanForRequest:h,enableHTTPTimings:d})},t.prototype.pn=function(t){var n=this;if(this.ln){var r,i=this.ln(),o=this.options,u=o.beforeNavigate,a=o.idleTimeout,c=o.finalTimeout,f=o.heartbeatInterval,s="pageload"===t.op;if(s){var v=function(t,n){var r=bn(t),e=pn(n),i=r||{},o=i.traceId,u=i.parentSpanId,a=i.parentSampled;return r?{traceId:o||pt(),parentSpanId:u||pt().substring(16),spanId:pt().substring(16),sampled:a,dsc:e||{}}:{traceId:o||pt(),spanId:pt().substring(16)}}(s?io("sentry-trace"):"",s?io("baggage"):void 0),h=v.traceId,d=v.dsc,l=v.parentSpanId,p=v.sampled;r=e(e({traceId:h,parentSpanId:l,parentSampled:p},t),{metadata:e(e({},t.metadata),{dynamicSamplingContext:d}),trimEnd:!0})}else r=e({trimEnd:!0},t);var m="function"==typeof u?u(r):r,y=void 0===m?e(e({},r),{sampled:!1}):m;y.metadata=y.name!==r.name?e(e({},y.metadata),{source:"custom"}):y.metadata,this.dn.name=y.name,this.dn.context=y,y.sampled;var g=re(i,y,a,c,!0,{location:Ye.location},f,s);return s&&Ye.document&&(Ye.document.addEventListener("readystatechange",(function(){["interactive","complete"].includes(Ye.document.readyState)&&g.sendAutoFinishSignal()})),["interactive","complete"].includes(Ye.document.readyState)&&g.sendAutoFinishSignal()),g.registerBeforeFinishCallback((function(t){n.vn(),Xi(t)})),g}},t.prototype.mn=function(){var t,n=this,r=function(){var r,e=n.options,i=e.idleTimeout,o=e.finalTimeout,u=e.heartbeatInterval,a=Or();if(!(a&&a.op&&["navigation","pageload"].includes(a.op))&&(t&&(t.setFinishReason("interactionInterrupted"),t.end(),t=void 0),n.ln&&n.dn.name)){var c=n.ln(),f=Ye.location,s={name:n.dn.name,op:"ui.action.click",trimEnd:!0,data:(r={},r["sentry.source"]=n.dn.context?oo(n.dn.context):"url",r)};t=re(c,s,i,o,!0,{location:f},u)}};["click"].forEach((function(t){Ye.document&&addEventListener(t,r,{once:!1,capture:!0})}))},t.prototype.yn=function(){var t=this,n=function(n){var r=n.entries,e=tr(),i=void 0!==e&&void 0!==e.getIntegrationByName?e.getIntegrationByName("Replay"):void 0,o=void 0!==i?i.getReplayId():void 0,u=Or(),a=nr(),c=void 0!==a?a.getUser():void 0;r.forEach((function(n){if(function(t){return"duration"in t}(n)){var r=n.interactionId;if(void 0===r)return;var e=t.hn[r],i=n.duration,a=n.startTime,f=Object.keys(t.hn),s=f.length>0?f.reduce((function(n,r){return t.hn[n].duration<t.hn[r].duration?n:r})):void 0;if("first-input"===n.entryType)if(f.map((function(n){return t.hn[n]})).some((function(t){return t.duration===i&&t.startTime===a})))return;if(!r)return;if(e)e.duration=Math.max(e.duration,i);else if(f.length<10||void 0===s||i>t.hn[s].duration){var v=t.dn.name,h=t.dn.context;v&&h&&(s&&Object.keys(t.hn).length>=10&&delete t.hn[s],t.hn[r]={routeName:v,duration:i,parentContext:h,user:c,activeTransaction:u,replayId:o,startTime:a})}}}))};Oi("event",n),Oi("first-input",n)},t}();function io(t){var n,r=(n="meta[name=".concat(t,"]"),L.document&&L.document.querySelector?L.document.querySelector(n):null);return r?r.getAttribute("content"):void 0}function oo(t){var n=t.attributes&&t.attributes["sentry.source"],r=t.data&&t.data["sentry.source"],e=t.metadata&&t.metadata.source;return n||r||e}function uo(){ee()}var ao=M,co=0;function fo(){return co>0}function so(){co++,setTimeout((function(){co--}))}function vo(t,n,r){if(void 0===n&&(n={}),"function"!=typeof t)return t;try{var i=t.__sentry_wrapped__;if(i)return"function"==typeof i?i:t;if(ct(t))return t}catch(n){return t}var sentryWrapped=function(){var i=Array.prototype.slice.call(arguments);try{r&&"function"==typeof r&&r.apply(this,arguments);var o=i.map((function(t){return vo(t,n)}));return t.apply(this,o)}catch(t){throw so(),Zn((function(r){r.addEventProcessor((function(t){return n.mechanism&&(gt(t,void 0,void 0),bt(t,n.mechanism)),t.extra=e(e({},t.extra),{arguments:i}),t})),captureException(t)})),t}};try{for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&(sentryWrapped[o]=t[o])}catch(t){}at(sentryWrapped,t),ut(t,"__sentry_wrapped__",sentryWrapped);try{Object.getOwnPropertyDescriptor(sentryWrapped,"name").configurable&&Object.defineProperty(sentryWrapped,"name",{get:function(){return t.name}})}catch(t){}return sentryWrapped}function ho(t,n){var r=po(t,n),e={type:n&&n.name,value:yo(n)};return r.length&&(e.stacktrace={frames:r}),void 0===e.type&&""===e.value&&(e.value="Unrecoverable error caught"),e}function lo(t,n){return{exception:{values:[ho(t,n)]}}}function po(t,n){var r=n.stacktrace||n.stack||"",e=function(t){if(t){if("number"==typeof t.framesToPop)return t.framesToPop;if(mo.test(t.message))return 1}return 0}(n);try{return t(r,e)}catch(t){}return[]}var mo=/Minified React error #\d+;/i;function yo(t){var n=t&&t.message;return n?n.error&&"string"==typeof n.error.message?n.error.message:n:"No error message"}function go(t,n,r,e){var i=wo(t,n,r&&r.syntheticException||void 0,e);return bt(i),i.level="error",r&&r.event_id&&(i.event_id=r.event_id),nn(i)}function bo(t,n,r,e,i){void 0===r&&(r="info");var o=_o(t,n,e&&e.syntheticException||void 0,i);return o.level=r,e&&e.event_id&&(o.event_id=e.event_id),nn(o)}function wo(t,n,r,i,o){var u;if(h(n)&&n.error)return lo(t,n.error);if(d(n)||v(n,"DOMException")){var a=n;if("stack"in n)u=lo(t,n);else{var c=a.name||(d(a)?"DOMError":"DOMException"),f=a.message?"".concat(c,": ").concat(a.message):c;gt(u=_o(t,f,r,i),f)}return"code"in a&&(u.tags=e(e({},u.tags),{"DOMException.code":"".concat(a.code)})),u}return s(n)?lo(t,n):y(n)||g(n)?(u=function(t,n,r,e){var i=tr(),o=i&&i.getOptions().normalizeDepth,u={exception:{values:[{type:g(n)?n.constructor.name:e?"UnhandledRejection":"Error",value:Eo(n,{isUnhandledRejection:e})}]},extra:{__serialized__:Zt(n,o)}};if(r){var a=po(t,r);a.length&&(u.exception.values[0].stacktrace={frames:a})}return u}(t,n,r,o),bt(u,{synthetic:!0}),u):(gt(u=_o(t,n,r,i),"".concat(n),void 0),bt(u,{synthetic:!0}),u)}function _o(t,n,r,e){var i={};if(e&&r){var o=po(t,r);o.length&&(i.exception={values:[{value:n,stacktrace:{frames:o}}]})}if(p(n)){var u=n.__sentry_template_string__,a=n.__sentry_template_values__;return i.logentry={message:u,params:a},i}return i.message=n,i}function Eo(t,n){var r=n.isUnhandledRejection,e=function(t,n){void 0===n&&(n=40);var r=Object.keys(ft(t));if(r.sort(),!r.length)return"[object has no keys]";if(r[0].length>=n)return S(r[0],n);for(var e=r.length;e>0;e--){var i=r.slice(0,e).join(", ");if(!(i.length>n))return e===r.length?i:S(i,n)}return""}(t),i=r?"promise rejection":"exception";if(h(t))return"Event `ErrorEvent` captured as ".concat(i," with message `").concat(t.message,"`");if(g(t)){var o=function(t){try{var n=Object.getPrototypeOf(t);return n?n.constructor.name:void 0}catch(t){}}(t);return"Event `".concat(o,"` (type=").concat(t.type,") captured as ").concat(i)}return"Object captured as ".concat(i," with keys: ").concat(e)}function So(t,n){var r=n.metadata,i=n.tunnel,o=n.dsn,u=e(e({event_id:t.event_id,sent_at:(new Date).toISOString()},r&&r.sdk&&{sdk:{name:r.sdk.name,version:r.sdk.version}}),!!i&&!!o&&{dsn:W(o)}),a=function(t){return[{type:"user_report"},t]}(t);return _n(u,[a])}var To=function(t){function n(n){var r=this;return function(t,n,r,e){void 0===r&&(r=[n]),void 0===e&&(e="npm");var i=t.dt||{};i.sdk||(i.sdk={name:"sentry.javascript.".concat(n),packages:r.map((function(t){return{name:"".concat(e,":@sentry/").concat(t),version:pr}})),version:pr}),t.dt=i}(n,"browser",["browser"],ao.SENTRY_SDK_SOURCE||"cdn"),r=t.call(this,n)||this,n.sendClientReports&&ao.document&&ao.document.addEventListener("visibilitychange",(function(){"hidden"===ao.document.visibilityState&&r.gn()})),r}return r(n,t),n.prototype.eventFromException=function(t,n){return go(this.Pt.stackParser,t,n,this.Pt.attachStacktrace)},n.prototype.eventFromMessage=function(t,n,r){return void 0===n&&(n="info"),bo(this.Pt.stackParser,t,n,r,this.Pt.attachStacktrace)},n.prototype.captureUserFeedback=function(t){if(this.Jt()){var n=So(t,{metadata:this.getSdkMetadata(),dsn:this.getDsn(),tunnel:this.getOptions().tunnel});this.Yt(n)}},n.prototype.Qt=function(n,r,e){return n.platform=n.platform||"javascript",t.prototype.Qt.call(this,n,r,e)},n.prototype.gn=function(){var t=this.tn();if(0!==t.length&&this.Bt){var n,r,e,i=(n=t,_n((r=this.Pt.tunnel&&W(this.Bt))?{dsn:r}:{},[[{type:"client_report"},{timestamp:e||fn(),discarded_events:n}]]));this.Yt(i)}},n}(ye),Oo=void 0;function jo(t,n){void 0===n&&(n=function(){if(Oo)return Oo;if(Rt(ao.fetch))return Oo=ao.fetch.bind(ao);var t=ao.document,n=ao.fetch;if(t&&"function"==typeof t.createElement)try{var r=t.createElement("iframe");r.hidden=!0,t.head.appendChild(r);var e=r.contentWindow;e&&e.fetch&&(n=e.fetch),t.head.removeChild(r)}catch(t){}return Oo=n.bind(ao)}());var r=0,i=0;return ke(t,(function(o){var u=o.body.length;r+=u,i++;var a=e({body:o.body,method:"POST",referrerPolicy:"origin",headers:t.headers,keepalive:r<=6e4&&i<15},t.fetchOptions);try{return n(t.url,a).then((function(t){return r-=u,i--,{statusCode:t.status,headers:{"x-sentry-rate-limits":t.headers.get("X-Sentry-Rate-Limits"),"retry-after":t.headers.get("Retry-After")}}}))}catch(t){return Oo=void 0,r-=u,i--,rn(t)}}))}function ko(t){return ke(t,(function(n){return new en((function(r,e){var i=new XMLHttpRequest;for(var o in i.onerror=e,i.onreadystatechange=function(){4===i.readyState&&r({statusCode:i.status,headers:{"x-sentry-rate-limits":i.getResponseHeader("X-Sentry-Rate-Limits"),"retry-after":i.getResponseHeader("Retry-After")}})},i.open("POST",t.url),t.headers)Object.prototype.hasOwnProperty.call(t.headers,o)&&i.setRequestHeader(o,t.headers[o]);i.send(n.body)}))}))}var xo="?";function Io(t,n,r,e){var i={filename:t,function:n,in_app:!0};return void 0!==r&&(i.lineno=r),void 0!==e&&(i.colno=e),i}var Co=/^\s*at (?:(.+?\)(?: \[.+\])?|.*?) ?\((?:address at )?)?(?:async )?((?:<anonymous>|[-a-z]+:|.*bundle|\/)?.*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,Ro=/\((\S*)(?::(\d+))(?::(\d+))\)/,Mo=[30,function(t){var n=Co.exec(t);if(n){if(n[2]&&0===n[2].indexOf("eval")){var r=Ro.exec(n[2]);r&&(n[2]=r[1],n[3]=r[2],n[4]=r[3])}var e=a(Wo(n[1]||xo,n[2]),2),i=e[0];return Io(e[1],i,n[3]?+n[3]:void 0,n[4]?+n[4]:void 0)}}],Ao=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:[-a-z]+)?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i,Do=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,Lo=[50,function(t){var n,r=Ao.exec(t);if(r){if(r[3]&&r[3].indexOf(" > eval")>-1){var e=Do.exec(r[3]);e&&(r[1]=r[1]||"eval",r[3]=e[1],r[4]=e[2],r[5]="")}var i=r[3],o=r[1]||xo;return o=(n=a(Wo(o,i),2))[0],Io(i=n[1],o,r[4]?+r[4]:void 0,r[5]?+r[5]:void 0)}}],No=/^\s*at (?:((?:\[object object\])?.+) )?\(?((?:[-a-z]+):.*?):(\d+)(?::(\d+))?\)?\s*$/i,qo=[40,function(t){var n=No.exec(t);return n?Io(n[2],n[1]||xo,+n[3],n[4]?+n[4]:void 0):void 0}],Po=/ line (\d+).*script (?:in )?(\S+)(?:: in function (\S+))?$/i,Ho=[10,function(t){var n=Po.exec(t);return n?Io(n[2],n[3]||xo,+n[1]):void 0}],Uo=/ line (\d+), column (\d+)\s*(?:in (?:<anonymous function: ([^>]+)>|([^)]+))\(.*\))? in (.*):\s*$/i,Fo=[20,function(t){var n=Uo.exec(t);return n?Io(n[5],n[3]||n[4]||xo,+n[1],+n[2]):void 0}],$o=[Mo,Lo,qo],Bo=Y.apply(void 0,c([],a($o),!1)),Wo=function(t,n){var r=-1!==t.indexOf("safari-extension"),e=-1!==t.indexOf("safari-web-extension");return r||e?[-1!==t.indexOf("@")?t.split("@")[0]:xo,r?"safari-extension:".concat(n):"safari-web-extension:".concat(n)]:[t,n]},zo=1024,Xo="Breadcrumbs",Go=function(t){void 0===t&&(t={});var n=e({console:!0,dom:!0,fetch:!0,history:!0,sentry:!0,xhr:!0},t);return{name:Xo,setupOnce:function(){},setup:function(t){var r,i;n.console&&(r=function(t){return function(n){if(tr()===t){var r={category:"console",data:{arguments:n.args,logger:"console"},level:cn(n.level),message:T(n.args," ")};if("assert"===n.level){if(!1!==n.args[0])return;r.message="Assertion failed: ".concat(T(n.args.slice(1)," ")||"console.assert"),r.data.arguments=n.args.slice(1)}Qn(r,{input:n.args,level:n.level})}}}(t),rt(i="console",r),et(i,lt)),n.dom&&function(t){rt("dom",t),et("dom",jt)}(function(t,n){return function(r){if(tr()===t){var e,i,o,u="object"==typeof n?n.serializeAttribute:void 0,a="object"==typeof n&&"number"==typeof n.maxStringLength?n.maxStringLength:void 0;a&&a>zo&&(a=zo),"string"==typeof u&&(u=[u]);try{var c=r.event,f=(o=c)&&o.target?c.target:c;e=N(f,{keyAttrs:u,maxStringLength:a}),i=P(f)}catch(t){e="<unknown>"}if(0!==e.length){var s={category:"ui.".concat(r.name),message:e};i&&(s.data={"ui.component_name":i}),Qn(s,{event:r.event,name:r.name,global:r.global})}}}}(t,n.dom)),n.xhr&&Vt(function(t){return function(n){if(tr()===t){var r=n.startTimestamp,e=n.endTimestamp,i=n.xhr.__sentry_xhr_v3__;if(r&&e&&i){var o=i.method,u=i.url,a=i.status_code,c=i.body;Qn({category:"xhr",data:{method:o,url:u,status_code:a},type:"http"},{xhr:n.xhr,input:c,startTimestamp:r,endTimestamp:e})}}}}(t)),n.fetch&&Mt(function(t){return function(n){if(tr()===t){var r=n.startTimestamp,i=n.endTimestamp;if(i&&(!n.fetchData.url.match(/sentry_key/)||"POST"!==n.fetchData.method))if(n.error){Qn({category:"fetch",data:n.fetchData,level:"error",type:"http"},{data:n.error,input:n.args,startTimestamp:r,endTimestamp:i})}else{var o=n.response;Qn({category:"fetch",data:e(e({},n.fetchData),{status_code:o&&o.status}),type:"http"},{input:n.args,response:o,startTimestamp:r,endTimestamp:i})}}}}(t)),n.history&&zt(function(t){return function(n){if(tr()===t){var r=n.from,e=n.to,i=un(ao.location.href),o=r?un(r):void 0,u=un(e);o&&o.path||(o=i),i.protocol===u.protocol&&i.host===u.host&&(e=u.relative),i.protocol===o.protocol&&i.host===o.host&&(r=o.relative),Qn({category:"navigation",data:{from:r,to:e}})}}}(t)),n.sentry&&t.on&&t.on("beforeSendEvent",function(t){return function(n){tr()===t&&Qn({category:"sentry.".concat("transaction"===n.type?"transaction":"event"),event_id:n.event_id,level:n.level,message:yt(n)},{event:n})}}(t))}}},Jo=he(Xo,Go);var Vo="Dedupe",Yo=function(){var t;return{name:Vo,setupOnce:function(){},processEvent:function(n){if(n.type)return n;try{if(function(t,n){if(!n)return!1;if(function(t,n){var r=t.message,e=n.message;if(!r&&!e)return!1;if(r&&!e||!r&&e)return!1;if(r!==e)return!1;if(!Zo(t,n))return!1;if(!Qo(t,n))return!1;return!0}(t,n))return!0;if(function(t,n){var r=tu(n),e=tu(t);if(!r||!e)return!1;if(r.type!==e.type||r.value!==e.value)return!1;if(!Zo(t,n))return!1;if(!Qo(t,n))return!1;return!0}(t,n))return!0;return!1}(n,t))return null}catch(t){}return t=n}}},Ko=he(Vo,Yo);function Qo(t,n){var r=nu(t),e=nu(n);if(!r&&!e)return!0;if(r&&!e||!r&&e)return!1;if(r=r,(e=e).length!==r.length)return!1;for(var i=0;i<e.length;i++){var o=e[i],u=r[i];if(o.filename!==u.filename||o.lineno!==u.lineno||o.colno!==u.colno||o.function!==u.function)return!1}return!0}function Zo(t,n){var r=t.fingerprint,e=n.fingerprint;if(!r&&!e)return!0;if(r&&!e||!r&&e)return!1;r=r,e=e;try{return!(r.join("")!==e.join(""))}catch(t){return!1}}function tu(t){return t.exception&&t.exception.values&&t.exception.values[0]}function nu(t){var n=t.exception;if(n)try{return n.values[0].stacktrace.frames}catch(t){return}}var ru="GlobalHandlers",eu=function(t){void 0===t&&(t={});var n=e({onerror:!0,onunhandledrejection:!0},t);return{name:ru,setupOnce:function(){Error.stackTraceLimit=50},setup:function(t){n.onerror&&function(t){Pt((function(n){var r=uu(),e=r.stackParser,i=r.attachStacktrace;if(tr()===t&&!fo()){var o=n.msg,u=n.url,a=n.line,c=n.column,f=n.error,s=void 0===f&&l(o)?function(t,n,r,e){var i=/^(?:[Uu]ncaught (?:exception: )?)?(?:((?:Eval|Internal|Range|Reference|Syntax|Type|URI|)Error): )?(.*)$/i,o=h(t)?t.message:t,u="Error",a=o.match(i);a&&(u=a[1],o=a[2]);return ou({exception:{values:[{type:u,value:o}]}},n,r,e)}(o,u,a,c):ou(wo(e,f||o,void 0,i,!1),u,a,c);s.level="error",Kn(s,{originalException:f,mechanism:{handled:!1,type:"onerror"}})}}))}(t),n.onunhandledrejection&&function(t){Ft((function(n){var r=uu(),e=r.stackParser,i=r.attachStacktrace;if(tr()===t&&!fo()){var o=function(t){if(m(t))return t;var n=t;try{if("reason"in n)return n.reason;if("detail"in n&&"reason"in n.detail)return n.detail.reason}catch(t){}return t}(n),u=m(o)?{exception:{values:[{type:"UnhandledRejection",value:"Non-Error promise rejection captured with value: ".concat(String(o))}]}}:wo(e,o,void 0,i,!0);u.level="error",Kn(u,{originalException:o,mechanism:{handled:!1,type:"onunhandledrejection"}})}}))}(t)}}},iu=he(ru,eu);function ou(t,n,r,e){var i=t.exception=t.exception||{},o=i.values=i.values||[],u=o[0]=o[0]||{},a=u.stacktrace=u.stacktrace||{},c=a.frames=a.frames||[],f=isNaN(parseInt(e,10))?void 0:e,s=isNaN(parseInt(r,10))?void 0:r,v=l(n)&&n.length>0?n:function(){try{return L.document.location.href}catch(t){return""}}();return 0===c.length&&c.push({colno:f,filename:v,function:"?",in_app:!0,lineno:s}),t}function uu(){var t=tr();return t&&t.getOptions()||{stackParser:function(){return[]},attachStacktrace:!1}}var au="HttpContext",cu=function(){return{name:au,setupOnce:function(){},preprocessEvent:function(t){if(ao.navigator||ao.location||ao.document){var n=t.request&&t.request.url||ao.location&&ao.location.href,r=(ao.document||{}).referrer,i=(ao.navigator||{}).userAgent,o=e(e(e({},t.request&&t.request.headers),r&&{Referer:r}),i&&{"User-Agent":i}),u=e(e(e({},t.request),n&&{url:n}),{headers:o});t.request=u}}}},fu=he(au,cu),su="LinkedErrors",vu=function(t){void 0===t&&(t={});var n=t.limit||5,r=t.key||"cause";return{name:su,setupOnce:function(){},preprocessEvent:function(t,e,i){var o=i.getOptions();k(ho,o.stackParser,o.maxValueLength,r,n,t,e)}}},hu=he(su,vu),du=["EventTarget","Window","Node","ApplicationCache","AudioTrackList","BroadcastChannel","ChannelMergerNode","CryptoOperation","EventSource","FileReader","HTMLUnknownElement","IDBDatabase","IDBRequest","IDBTransaction","KeyOperation","MediaController","MessagePort","ModalWindow","Notification","SVGElementInstance","Screen","SharedWorker","TextTrack","TextTrackCue","TextTrackList","WebSocket","WebSocketWorker","Worker","XMLHttpRequest","XMLHttpRequestEventTarget","XMLHttpRequestUpload"],lu="TryCatch",pu=function(t){void 0===t&&(t={});var n=e({XMLHttpRequest:!0,eventTarget:!0,requestAnimationFrame:!0,setInterval:!0,setTimeout:!0},t);return{name:lu,setupOnce:function(){n.setTimeout&&ot(ao,"setTimeout",yu),n.setInterval&&ot(ao,"setInterval",yu),n.requestAnimationFrame&&ot(ao,"requestAnimationFrame",gu),n.XMLHttpRequest&&"XMLHttpRequest"in ao&&ot(XMLHttpRequest.prototype,"send",bu);var t=n.eventTarget;t&&(Array.isArray(t)?t:du).forEach(wu)}}},mu=he(lu,pu);function yu(t){return function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];var e=n[0];return n[0]=vo(e,{mechanism:{data:{function:Z(t)},handled:!1,type:"instrument"}}),t.apply(this,n)}}function gu(t){return function(n){return t.apply(this,[vo(n,{mechanism:{data:{function:"requestAnimationFrame",handler:Z(t)},handled:!1,type:"instrument"}})])}}function bu(t){return function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];var e=this,i=["onload","onerror","onprogress","onreadystatechange"];return i.forEach((function(t){t in e&&"function"==typeof e[t]&&ot(e,t,(function(n){var r={mechanism:{data:{function:t,handler:Z(n)},handled:!1,type:"instrument"}},e=ct(n);return e&&(r.mechanism.data.handler=Z(e)),vo(n,r)}))})),t.apply(this,n)}}function wu(t){var n=ao,r=n[t]&&n[t].prototype;r&&r.hasOwnProperty&&r.hasOwnProperty("addEventListener")&&(ot(r,"addEventListener",(function(n){return function(r,e,i){try{"function"==typeof e.handleEvent&&(e.handleEvent=vo(e.handleEvent,{mechanism:{data:{function:"handleEvent",handler:Z(e),target:t},handled:!1,type:"instrument"}}))}catch(t){}return n.apply(this,[r,vo(e,{mechanism:{data:{function:"addEventListener",handler:Z(e),target:t},handled:!1,type:"instrument"}}),i])}})),ot(r,"removeEventListener",(function(t){return function(n,r,e){var i=r;try{var o=i&&i.__sentry_wrapped__;o&&t.call(this,n,o,e)}catch(t){}return t.call(this,n,i,e)}})))}var _u=[De(),He(),pu(),Go(),eu(),vu(),Yo(),cu()];function Eu(t){return c([],a(_u),!1)}var Su=Object.freeze({__proto__:null,GlobalHandlers:iu,TryCatch:mu,Breadcrumbs:Jo,LinkedErrors:hu,HttpContext:fu,Dedupe:Ko}),Tu={};ao.Sentry&&ao.Sentry.Integrations&&(Tu=ao.Sentry.Integrations);var Ou=e(e(e({},Tu),Ve),Su);return Ou.Replay=An,Ou.BrowserTracing=eo,uo(),t.Breadcrumbs=Jo,t.BrowserClient=To,t.BrowserTracing=eo,t.Dedupe=Ko,t.Feedback=Mn,t.FunctionToString=Ue,t.GlobalHandlers=iu,t.HttpContext=fu,t.Hub=yr,t.InboundFilters=Le,t.Integrations=Ou,t.LinkedErrors=hu,t.Replay=An,t.SDK_VERSION=pr,t.SEMANTIC_ATTRIBUTE_SENTRY_OP=Dr,t.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN=Lr,t.SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE=Ar,t.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE=Mr,t.Scope=dr,t.Span=Pr,t.TryCatch=mu,t.WINDOW=ao,t.addBreadcrumb=Qn,t.addEventProcessor=function(t){var n=tr();n&&n.addEventProcessor&&n.addEventProcessor(t)},t.addExtensionMethods=uo,t.addGlobalEventProcessor=Nn,t.addIntegration=function(t){var n=tr();n&&n.addIntegration&&n.addIntegration(t)},t.breadcrumbsIntegration=Go,t.browserApiErrorsIntegration=pu,t.browserTracingIntegration=function(t){void 0===t&&(t={});var n=t;return"boolean"==typeof t.markBackgroundSpan&&(n.markBackgroundTransactions=t.markBackgroundSpan),"boolean"==typeof t.instrumentPageLoad&&(n.startTransactionOnPageLoad=t.instrumentPageLoad),"boolean"==typeof t.instrumentNavigation&&(n.startTransactionOnLocationChange=t.instrumentNavigation),new eo(n)},t.captureEvent=Kn,t.captureException=captureException,t.captureMessage=function(t,n){var r="string"==typeof n?n:void 0,e="string"!=typeof n?{captureContext:n}:void 0;return wr().captureMessage(t,r,e)},t.captureSession=or,t.captureUserFeedback=function(t){var n=tr();n&&n.captureUserFeedback(t)},t.chromeStackLineParser=Mo,t.close=function(t){return i(this,void 0,void 0,(function(){var n;return o(this,(function(r){return(n=tr())?[2,n.close(t)]:[2,Promise.resolve(!1)]}))}))},t.configureScope=function(t){wr().configureScope(t)},t.continueTrace=function(t,n){var r=t.sentryTrace,i=t.baggage,o=nr(),u=function(t,n){var r=bn(t),e=pn(n),i=r||{},o=i.traceId,u=i.parentSpanId,a=i.parentSampled;return r?{traceparentData:r,dynamicSamplingContext:e||{},propagationContext:{traceId:o||pt(),parentSpanId:u||pt().substring(16),spanId:pt().substring(16),sampled:a,dsc:e||{}}}:{traceparentData:r,dynamicSamplingContext:void 0,propagationContext:{traceId:o||pt(),spanId:pt().substring(16)}}}(r,i),a=u.traceparentData,c=u.dynamicSamplingContext,f=u.propagationContext;o.setPropagationContext(f);var s=e(e({},a),{metadata:ht({dynamicSamplingContext:c})});return n?Er((function(){return n(s)})):s},t.createTransport=ke,t.createUserFeedbackEnvelope=So,t.dedupeIntegration=Yo,t.defaultIntegrations=_u,t.defaultStackLineParsers=$o,t.defaultStackParser=Bo,t.endSession=er,t.eventFromException=go,t.eventFromMessage=bo,t.exceptionFromError=ho,t.feedbackIntegration=function(t){return new Mn({})},t.flush=function(t){return i(this,void 0,void 0,(function(){var n;return o(this,(function(r){return(n=tr())?[2,n.flush(t)]:[2,Promise.resolve(!1)]}))}))},t.forceLoad=function(){},t.functionToStringIntegration=He,t.geckoStackLineParser=Lo,t.getActiveSpan=$r,t.getClient=tr,t.getCurrentHub=wr,t.getCurrentScope=nr,t.getDefaultIntegrations=Eu,t.getHubFromCarrier=Sr,t.globalHandlersIntegration=eu,t.httpContextIntegration=cu,t.inboundFiltersIntegration=De,t.init=function(t){void 0===t&&(t={}),void 0===t.defaultIntegrations&&(t.defaultIntegrations=Eu()),void 0===t.release&&("string"==typeof __SENTRY_RELEASE__&&(t.release=__SENTRY_RELEASE__),ao.SENTRY_RELEASE&&ao.SENTRY_RELEASE.id&&(t.release=ao.SENTRY_RELEASE.id)),void 0===t.autoSessionTracking&&(t.autoSessionTracking=!0),void 0===t.sendClientReports&&(t.sendClientReports=!0);var n,r=e(e({},t),{stackParser:(n=t.stackParser||Bo,Array.isArray(n)?Y.apply(void 0,c([],a(n),!1)):n),integrations:fe(t),transport:t.transport||(Ct()?jo:ko)});!function(t,n){!0===n.debug&&F((function(){console.warn("[Sentry] Cannot initialize SDK with `debug` option using a non-debug bundle.")})),nr().update(n.initialScope);var r=new t(n);je(r),function(t){t.init?t.init():t.setupIntegrations&&t.setupIntegrations()}(r)}(To,r),t.autoSessionTracking&&function(){if(void 0===ao.document)return;rr({ignoreDuration:!0}),or(),zt((function(t){var n=t.from,r=t.to;void 0!==n&&n!==r&&(rr({ignoreDuration:!0}),or())}))}()},t.isInitialized=function(){return!!tr()},t.lastEventId=function(){return wr().lastEventId()},t.linkedErrorsIntegration=vu,t.makeFetchTransport=jo,t.makeMain=br,t.makeXHRTransport=ko,t.metrics=Je,t.onLoad=function(t){t()},t.opera10StackLineParser=Ho,t.opera11StackLineParser=Fo,t.parameterize=function(t){for(var n=[],r=1;r<arguments.length;r++)n[r-1]=arguments[r];var e=new String(String.raw.apply(String,c([t],a(n),!1)));return e.__sentry_template_string__=t.join("\0").replace(/%/g,"%%").replace(/\0/g,"%s"),e.__sentry_template_values__=n,e},t.replayIntegration=function(t){return new An({})},t.setContext=function(t,n){wr().setContext(t,n)},t.setCurrentClient=je,t.setExtra=function(t,n){wr().setExtra(t,n)},t.setExtras=function(t){wr().setExtras(t)},t.setTag=function(t,n){wr().setTag(t,n)},t.setTags=function(t){wr().setTags(t)},t.setUser=function(t){wr().setUser(t)},t.showReportDialog=function(t,n){if(void 0===t&&(t={}),void 0===n&&(n=wr()),ao.document){var r=n.getStackTop(),i=r.client,o=r.scope,u=t.dsn||i&&i.getDsn();if(u){o&&(t.user=e(e({},o.getUser()),t.user)),t.eventId||(t.eventId=n.lastEventId());var a=ao.document.createElement("script");a.async=!0,a.crossOrigin="anonymous",a.src=function(t,n){var r=X(t);if(!r)return"";var e="".concat(oe(r),"embed/error-page/"),i="dsn=".concat(W(r));for(var o in n)if("dsn"!==o&&"onClose"!==o)if("user"===o){var u=n.user;if(!u)continue;u.name&&(i+="&name=".concat(encodeURIComponent(u.name))),u.email&&(i+="&email=".concat(encodeURIComponent(u.email)))}else i+="&".concat(encodeURIComponent(o),"=").concat(encodeURIComponent(n[o]));return"".concat(e,"?").concat(i)}(u,t),t.onLoad&&(a.onload=t.onLoad);var c=t.onClose;if(c){var f=function(t){if("__sentry_reportdialog_closed__"===t.data)try{c()}finally{ao.removeEventListener("message",f)}};ao.addEventListener("message",f)}var s=ao.document.head||ao.document.body;s&&s.appendChild(a)}}},t.startInactiveSpan=Fr,t.startSession=rr,t.startSpan=function(t,n){var r=Wr(t);return Er((function(){return Zn(t.scope,(function(e){var i=wr(),o=e.getSpan(),u=t.onlyIfParent&&!o?void 0:Br(i,{parentSpan:o,spanContext:r,forceTransaction:t.forceTransaction,scope:e});return Hr((function(){return n(u)}),(function(){if(u){var t=zn(u).status;t&&"ok"!==t||u.setStatus("internal_error")}}),(function(){return u&&u.end()}))}))}))},t.startSpanManual=function(t,n){var r=Wr(t);return Er((function(){return Zn(t.scope,(function(e){var i=wr(),o=e.getSpan(),u=t.onlyIfParent&&!o?void 0:Br(i,{parentSpan:o,spanContext:r,forceTransaction:t.forceTransaction,scope:e});function a(){u&&u.end()}return Hr((function(){return n(u,a)}),(function(){if(u&&u.isRecording()){var t=zn(u).status;t&&"ok"!==t||u.setStatus("internal_error")}}))}))}))},t.startTransaction=function(t,n){return wr().startTransaction(e({},t),n)},t.winjsStackLineParser=qo,t.withActiveSpan=function(t,n){return Zn((function(r){return r.setSpan(t),n(r)}))},t.withIsolationScope=function(t){return Er((function(){return t(_r())}))},t.withScope=Zn,t.wrap=function(t){return vo(t)()},"includes"in Array.prototype||(Array.prototype.includes=function(t){return this.indexOf(t)>-1}),"find"in Array.prototype||(Array.prototype.find=function(t){for(var n=0;n<this.length;n++)if(t(this[n]))return this[n]}),"findIndex"in Array.prototype||(Array.prototype.findIndex=function(t){for(var n=0;n<this.length;n++)if(t(this[n]))return n;return-1}),"includes"in String.prototype||(String.prototype.includes=function(t){return this.indexOf(t)>-1}),"startsWith"in String.prototype||(String.prototype.startsWith=function(t){return 0===this.indexOf(t)}),"endsWith"in String.prototype||(String.prototype.endsWith=function(t){var n=this.indexOf(t);return n>-1&&n===this.length-t.length}),t}({});
//# sourceMappingURL=bundle.tracing.es5.min.js.map
