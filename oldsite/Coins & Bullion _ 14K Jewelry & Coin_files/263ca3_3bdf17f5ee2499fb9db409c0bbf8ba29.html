
<!-- saved from url=(0090)https://www-14kexchange-com.filesusr.com/html/263ca3_3bdf17f5ee2499fb9db409c0bbf8ba29.html -->
<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<script type="text/javascript" src="./bullionvaultchart.js.download"></script>
		<script type="text/javascript">
			var options = {
				bullion: 'silver',
				currency: 'USD',
				timeframe: '1y',
				chartType: 'line',
				miniChartModeAxis: 'oz',
				referrerID: 'MYUSERNAME',
				containerDefinedSize: true,
				miniChartMode: true,
				displayLatestPriceLine: true,
				switchBullion: true,
				switchCurrency: true,
				switchTimeframe: true,
				switchChartType: false,
				exportButton: true
			};
			var chartBV = new BullionVaultChart(options, 'embed');
		</script><link rel="stylesheet" type="text/css" href="./bvchartstyle.css">
	<link rel="stylesheet" href="chrome-extension://ihcjicgdanjaechkgeegckofjjedodee/app/content-style.css"></head>
	<body>
	<div id="embed" style="height: 210px; width: 400px; min-height: 350px;"><div id="jschart_wrapper" class="bvchart" style="min-width: 200px;"><div id="jschart_title" class="bvchart__title"><b>Silver 1 year</b> GMT-4</div><div id="jschart_container" class="bvchart__container" data-highcharts-chart="0" style="overflow: hidden; height: 335px; width: 400px;"><div id="highcharts-721wtyp-0" style="position: relative; overflow: hidden; width: 396px; height: 335px; text-align: left; line-height: normal; z-index: 0; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); user-select: none; touch-action: manipulation; outline: none; font-size: 14px;" dir="ltr" class="highcharts-container "><svg version="1.1" class="highcharts-root" style="font-family: Helvetica, Arial, sans-serif; font-size: 14px;" xmlns="http://www.w3.org/2000/svg" width="396" height="335" viewBox="0 0 396 335" role="img" aria-label=""><desc>Created with Highcharts 11.4.1</desc><defs><filter id="highcharts-drop-shadow-0"><fedropshadow dx="1" dy="1" flood-color="#000000" flood-opacity="0.75" stdDeviation="2.5"></fedropshadow></filter><clippath id="highcharts-721wtyp-181-"><rect x="0" y="0" width="352" height="305" fill="none"></rect></clippath></defs><rect fill="#ffffff" class="highcharts-background" filter="none" x="0" y="0" width="396" height="335" rx="0" ry="0"></rect><rect fill="none" class="highcharts-plot-background" x="0" y="14" width="352" height="305" filter="none"></rect><image preserveAspectRatio="none" x="53" y="148" width="250" height="38" href="https://www.bullionvault.com/chart/bullionvault2.png"></image><g class="highcharts-grid highcharts-xaxis-grid" data-z-index="1"><path fill="none" stroke="#e9e9e9" stroke-width="1" stroke-dasharray="none" data-z-index="1" class="highcharts-grid-line" d="M 45.5 14 L 45.5 319" opacity="1"></path><path fill="none" stroke="#e9e9e9" stroke-width="1" stroke-dasharray="none" data-z-index="1" class="highcharts-grid-line" d="M 105.5 14 L 105.5 319" opacity="1"></path><path fill="none" stroke="#e9e9e9" stroke-width="1" stroke-dasharray="none" data-z-index="1" class="highcharts-grid-line" d="M 165.5 14 L 165.5 319" opacity="1"></path><path fill="none" stroke="#e9e9e9" stroke-width="1" stroke-dasharray="none" data-z-index="1" class="highcharts-grid-line" d="M 223.5 14 L 223.5 319" opacity="1"></path><path fill="none" stroke="#e9e9e9" stroke-width="1" stroke-dasharray="none" data-z-index="1" class="highcharts-grid-line" d="M 283.5 14 L 283.5 319" opacity="1"></path><path fill="none" stroke="#e9e9e9" stroke-width="1" stroke-dasharray="none" data-z-index="1" class="highcharts-grid-line" d="M 343.5 14 L 343.5 319" opacity="1"></path></g><g class="highcharts-grid highcharts-yaxis-grid" data-z-index="1"></g><g class="highcharts-grid highcharts-yaxis-grid" data-z-index="1"><path fill="none" stroke="#e9e9e9" stroke-width="1" stroke-dasharray="none" data-z-index="1" class="highcharts-grid-line" d="M 0 273.5 L 352 273.5" opacity="1"></path><path fill="none" stroke="#e9e9e9" stroke-width="1" stroke-dasharray="none" data-z-index="1" class="highcharts-grid-line" d="M 0 164.5 L 352 164.5" opacity="1"></path><path fill="none" stroke="#e9e9e9" stroke-width="1" stroke-dasharray="none" data-z-index="1" class="highcharts-grid-line" d="M 0 55.5 L 352 55.5" opacity="1"></path></g><rect fill="none" class="highcharts-plot-border" data-z-index="1" stroke="#cccccc" stroke-width="0" x="0" y="14" width="352" height="305"></rect><g class="highcharts-axis highcharts-xaxis" data-z-index="2"><path fill="none" class="highcharts-tick" stroke="#000000" stroke-width="2" d="M 46 319 L 46 324" opacity="1"></path><path fill="none" class="highcharts-tick" stroke="#000000" stroke-width="2" d="M 106 319 L 106 324" opacity="1"></path><path fill="none" class="highcharts-tick" stroke="#000000" stroke-width="2" d="M 166 319 L 166 324" opacity="1"></path><path fill="none" class="highcharts-tick" stroke="#000000" stroke-width="2" d="M 224 319 L 224 324" opacity="1"></path><path fill="none" class="highcharts-tick" stroke="#000000" stroke-width="2" d="M 284 319 L 284 324" opacity="1"></path><path fill="none" class="highcharts-tick" stroke="#000000" stroke-width="2" d="M 344 319 L 344 324" opacity="1"></path><path fill="none" class="highcharts-axis-line" stroke="#8c8c8c" stroke-width="1" data-z-index="7" d="M 0 319.5 L 352 319.5"></path></g><g class="highcharts-axis highcharts-yaxis" data-z-index="2"><text x="0" data-z-index="7" text-anchor="end" transform="translate(0,0)" class="highcharts-axis-title" style="color: rgb(0, 0, 0); font-size: 0.8em; font-weight: bold; text-anchor: start; fill: rgb(0, 0, 0);" visibility="hidden">USD/kg <a href="https://www.bullionvault.com/price-alerts.do" target="_self" style="text-decoration: underline; font-weight: normal;"><tspan>Set<tspan dy="14" x="0">​</tspan>price alert</tspan></a></text><path fill="none" class="highcharts-axis-line" stroke="#8c8c8c" stroke-width="1" data-z-index="7" d="M -0.5 14 L -0.5 319" visibility="hidden"></path></g><g class="highcharts-axis highcharts-yaxis" data-z-index="2"><path fill="none" class="highcharts-tick" stroke="#000000" stroke-width="2" d="M 353 273 L 358 273" opacity="1"></path><path fill="none" class="highcharts-tick" stroke="#000000" stroke-width="2" d="M 353 164 L 358 164" opacity="1"></path><path fill="none" class="highcharts-tick" stroke="#000000" stroke-width="2" d="M 353 55 L 358 55" opacity="1"></path><text x="388" data-z-index="7" text-anchor="end" transform="translate(0,0)" class="highcharts-axis-title" style="color: rgb(0, 0, 0); font-size: 11px; font-weight: bold; fill: rgb(0, 0, 0);" y="9">USD/oz</text><path fill="none" class="highcharts-axis-line" stroke="#8c8c8c" stroke-width="1" data-z-index="7" d="M 353.5 14 L 353.5 319"></path></g><g class="highcharts-series-group" data-z-index="3" filter="none"><g class="highcharts-series highcharts-series-0 highcharts-area-series" data-z-index="0.1" opacity="1" transform="translate(0,14) scale(1 1)" clip-path="url(#highcharts-721wtyp-181-)"><path fill="rgba(246,246,244,0.7)" d="M 0 194.25722520838002 L 1.9664804469274 225.78091788246002 L 3.9329608938547 220.528383356147 L 5.8994413407821 226.54461434024398 L 7.8659217877095 263.779059415306 L 9.8324022346369 261.360687298991 L 11.798882681564 264.305161419557 L 13.765363128492 230.71100279326498 L 15.731843575419 243.50716121924302 L 17.698324022346 245.518228558074 L 19.664804469274 291.136363636364 L 21.631284916201 271.662103962875 L 23.597765363128 273.987135401017 L 25.564245810056 258.781090374921 L 27.530726256983 270.58444340578 L 29.497206703911 230.71100279326498 L 31.463687150838 232.221424676438 L 33.430167597765 219.696802768782 L 35.396648044693 230.795857955241 L 37.36312849162 209.225675780946 L 39.329608938547 208.156500740048 L 41.296089385475 226.88403498814802 L 43.262569832402 236.54055242101498 L 45.22905027933 235.51380496110602 L 47.195530726257 257.737371882616 L 49.162011173184 236.34538554847 L 51.128491620112 260.851556327135 L 53.094972067039 249.871298367442 L 55.061452513966 240.842709133198 L 57.027932960894 184.57525122692 L 58.994413407821 181.18104474787998 L 60.960893854749 184.77041809947002 L 62.927374301676 183.46364860504002 L 64.893854748603 172.05911483546 L 66.860335195531 186.26386895024 L 68.826815642458 154.6298645656 L 70.793296089385 159.86542805952 L 72.759776536313 156.99732358473 L 74.72625698324 167.50239263735 L 76.692737430168 149.08033697237 L 78.659217877095 145.80492772009 L 80.625698324022 159.65329015458 L 82.59217877095 191.21941040964 L 84.558659217877 162.69110495332 L 86.525139664804 172.2882237728 L 88.491620111732 164.74459987313 L 90.458100558659 156.01300370581 L 92.424581005587 103.42825982929 L 94.391061452514 99.86434302629999 L 96.357541899441 100.5516698383 L 98.324022346369 103.25854950534 L 100.2905027933 106.27939327168 L 102.25698324022 82.545404467 L 104.22346368715 131.9905073504 L 106.18994413408 137.49760736265 L 108.15642458101 139.10136992399 L 110.12290502793 172.68704303409 L 112.08938547486 169.1231262311 L 114.05586592179 166.10228246475 L 116.02234636872 185.80565107556998 L 117.98882681564 194.70695756685 L 119.95530726257 197.19321381275 L 121.9217877095 173.0010071334 L 123.88826815642 179.51788357315002 L 125.85474860335 168.24063254655 L 127.82122905028 166.39927553167 L 129.78770949721 193.24744878087 L 131.75418994413 198.10964956209 L 133.72067039106 187.56215292848 L 135.68715083799 188.27493628908002 L 137.65363128492 168.54611112966 L 139.62011173184 178.20262856252998 L 141.58659217877 175.29209650675 L 143.5530726257 152.78850755072 L 145.51955307263 179.73002147809 L 147.48603351955 189.37805339476 L 149.45251396648 190.32843120888998 L 151.41899441341 223.141922345007 L 153.38547486034 217.89787333489102 L 155.35195530726 212.976273940284 L 157.31843575419 215.87832047986302 L 159.28491620112 210.286365305646 L 161.25139664804 221.76726872099601 L 163.21787709497 233.545165203263 L 165.1843575419 232.280823289821 L 167.15083798883 215.114624022079 L 169.11731843575 214.452753758666 L 171.08379888268 202.19118285314 L 173.05027932961 202.3184655961 L 175.01675977654 193.56141288018 L 176.98324022346 213.256295974805 L 178.94972067039 186.56934753336 L 180.91620111732 194.68998653446 L 182.88268156425 195.90341535072 L 184.84916201117 184.38008435438002 L 186.8156424581 191.93219377024002 L 188.78212290503 188.58890038839002 L 190.74860335196 199.56915834808 L 192.71508379888 180.80768203519 L 194.68156424581 168.98735797193 L 196.64804469274 174.73205243771 L 198.61452513966 148.05358951246 L 200.58100558659 144.30299135312 L 202.54748603352 155.25779276422 L 204.51396648045 148.2742129336 L 206.48044692737 142.31738056288 L 208.4469273743 146.86561724479 L 210.41340782123 149.14822110195 L 212.37988826816 125.18512335993 L 214.34636871508 124.51476758032001 L 216.31284916201 137.23455636052 L 218.27932960894 142.78408395375 L 220.24581005587 153.04307303665 L 222.21229050279 172.91615197142 L 224.17877094972 170.32806953115 L 226.14525139665 152.04178212533 L 228.11173184358 132.94937068073 L 230.0782122905 135.50351105621 L 232.04469273743 149.97980168931 L 234.01117318436 115.29949698972999 L 235.97765363128 99.99162576926 L 237.94413407821 101.31536629608999 L 239.91061452514 96.56347722543 L 241.87709497207 108.26500406192 L 243.84357541899 121.85031549427 L 245.81005586592 124.69296342047 L 247.77653631285 105.21870374698 L 249.74301675978 92.36314670761999 L 251.7094972067 89.79203529975001 L 253.67597765363 104.62471761315001 L 255.64245810056 152.62728274296 L 257.60893854749 215.717095672109 L 259.57541899441 204.25316328915 L 261.54189944134 179.55182563794 L 263.50837988827 142.03735852836 L 265.4748603352 148.00267641527 L 267.44134078212 139.30502231273 L 269.40782122905 135.85990273651 L 271.37430167598 135.85990273651 L 273.34078212291 129.92004139819 L 275.30726256983 105.59206645968001 L 277.27374301676 119.94107434981001 L 279.24022346369 125.59242813742 L 281.20670391061 124.61659377468999 L 283.17318435754 137.93036868872 L 285.13966480447 150.11556994847 L 287.1061452514 136.12295373863 L 289.07262569832 137.6673176866 L 291.03910614525 130.18309240032 L 293.00558659218 132.16021767436 L 294.97206703911 124.2177745134 L 296.93854748603 132.83057345397 L 298.90502793296 142.03735852836 L 300.87150837989 140.31479874025 L 302.83798882682 110.49669482189 L 304.80446927374 110.11484659300001 L 306.77094972067 110.85308650219 L 308.7374301676 115.41829421649001 L 310.70391061453 115.7492293482 L 312.67039106145 123.47104908802001 L 314.63687150838 75.71456392792999 L 316.60335195531 81.72230939583 L 318.56983240223 41.534904684000026 L 320.53631284916 39.59172147474999 L 322.50279329609 24.996633614890015 L 324.46927374302 30.401907432760026 L 326.43575418994 32.353576158199985 L 328.40223463687 32.183865834250014 L 330.3687150838 20.73690448369001 L 332.33519553073 40.87303442058999 L 334.30167597765 38.811053984579985 L 336.26815642458 42.51922456292999 L 338.23463687151 23.92745857399001 L 340.20111731844 40.966375098770015 L 342.16759776536 36.81695767814 L 344.13407821229 29.629725458769997 L 346.10055865922 15.577710635549977 L 348.06703910615 13.863636363640012 L 350.03351955307 26.091265204380022 L 352 18.021539300460006 L 352 18.021539300460006 L 352 305 L 352 305 L 350.03351955307 305 L 348.06703910615 305 L 346.10055865922 305 L 344.13407821229 305 L 342.16759776536 305 L 340.20111731844 305 L 338.23463687151 305 L 336.26815642458 305 L 334.30167597765 305 L 332.33519553073 305 L 330.3687150838 305 L 328.40223463687 305 L 326.43575418994 305 L 324.46927374302 305 L 322.50279329609 305 L 320.53631284916 305 L 318.56983240223 305 L 316.60335195531 305 L 314.63687150838 305 L 312.67039106145 305 L 310.70391061453 305 L 308.7374301676 305 L 306.77094972067 305 L 304.80446927374 305 L 302.83798882682 305 L 300.87150837989 305 L 298.90502793296 305 L 296.93854748603 305 L 294.97206703911 305 L 293.00558659218 305 L 291.03910614525 305 L 289.07262569832 305 L 287.1061452514 305 L 285.13966480447 305 L 283.17318435754 305 L 281.20670391061 305 L 279.24022346369 305 L 277.27374301676 305 L 275.30726256983 305 L 273.34078212291 305 L 271.37430167598 305 L 269.40782122905 305 L 267.44134078212 305 L 265.4748603352 305 L 263.50837988827 305 L 261.54189944134 305 L 259.57541899441 305 L 257.60893854749 305 L 255.64245810056 305 L 253.67597765363 305 L 251.7094972067 305 L 249.74301675978 305 L 247.77653631285 305 L 245.81005586592 305 L 243.84357541899 305 L 241.87709497207 305 L 239.91061452514 305 L 237.94413407821 305 L 235.97765363128 305 L 234.01117318436 305 L 232.04469273743 305 L 230.0782122905 305 L 228.11173184358 305 L 226.14525139665 305 L 224.17877094972 305 L 222.21229050279 305 L 220.24581005587 305 L 218.27932960894 305 L 216.31284916201 305 L 214.34636871508 305 L 212.37988826816 305 L 210.41340782123 305 L 208.4469273743 305 L 206.48044692737 305 L 204.51396648045 305 L 202.54748603352 305 L 200.58100558659 305 L 198.61452513966 305 L 196.64804469274 305 L 194.68156424581 305 L 192.71508379888 305 L 190.74860335196 305 L 188.78212290503 305 L 186.8156424581 305 L 184.84916201117 305 L 182.88268156425 305 L 180.91620111732 305 L 178.94972067039 305 L 176.98324022346 305 L 175.01675977654 305 L 173.05027932961 305 L 171.08379888268 305 L 169.11731843575 305 L 167.15083798883 305 L 165.1843575419 305 L 163.21787709497 305 L 161.25139664804 305 L 159.28491620112 305 L 157.31843575419 305 L 155.35195530726 305 L 153.38547486034 305 L 151.41899441341 305 L 149.45251396648 305 L 147.48603351955 305 L 145.51955307263 305 L 143.5530726257 305 L 141.58659217877 305 L 139.62011173184 305 L 137.65363128492 305 L 135.68715083799 305 L 133.72067039106 305 L 131.75418994413 305 L 129.78770949721 305 L 127.82122905028 305 L 125.85474860335 305 L 123.88826815642 305 L 121.9217877095 305 L 119.95530726257 305 L 117.98882681564 305 L 116.02234636872 305 L 114.05586592179 305 L 112.08938547486 305 L 110.12290502793 305 L 108.15642458101 305 L 106.18994413408 305 L 104.22346368715 305 L 102.25698324022 305 L 100.2905027933 305 L 98.324022346369 305 L 96.357541899441 305 L 94.391061452514 305 L 92.424581005587 305 L 90.458100558659 305 L 88.491620111732 305 L 86.525139664804 305 L 84.558659217877 305 L 82.59217877095 305 L 80.625698324022 305 L 78.659217877095 305 L 76.692737430168 305 L 74.72625698324 305 L 72.759776536313 305 L 70.793296089385 305 L 68.826815642458 305 L 66.860335195531 305 L 64.893854748603 305 L 62.927374301676 305 L 60.960893854749 305 L 58.994413407821 305 L 57.027932960894 305 L 55.061452513966 305 L 53.094972067039 305 L 51.128491620112 305 L 49.162011173184 305 L 47.195530726257 305 L 45.22905027933 305 L 43.262569832402 305 L 41.296089385475 305 L 39.329608938547 305 L 37.36312849162 305 L 35.396648044693 305 L 33.430167597765 305 L 31.463687150838 305 L 29.497206703911 305 L 27.530726256983 305 L 25.564245810056 305 L 23.597765363128 305 L 21.631284916201 305 L 19.664804469274 305 L 17.698324022346 305 L 15.731843575419 305 L 13.765363128492 305 L 11.798882681564 305 L 9.8324022346369 305 L 7.8659217877095 305 L 5.8994413407821 305 L 3.9329608938547 305 L 1.9664804469274 305 L 0 305 Z" class="highcharts-area" data-z-index="0" fill-opacity="1" style="pointer-events: none;"></path><path fill="none" d="M 0 194.25722520838002 L 1.9664804469274 225.78091788246002 L 3.9329608938547 220.528383356147 L 5.8994413407821 226.54461434024398 L 7.8659217877095 263.779059415306 L 9.8324022346369 261.360687298991 L 11.798882681564 264.305161419557 L 13.765363128492 230.71100279326498 L 15.731843575419 243.50716121924302 L 17.698324022346 245.518228558074 L 19.664804469274 291.136363636364 L 21.631284916201 271.662103962875 L 23.597765363128 273.987135401017 L 25.564245810056 258.781090374921 L 27.530726256983 270.58444340578 L 29.497206703911 230.71100279326498 L 31.463687150838 232.221424676438 L 33.430167597765 219.696802768782 L 35.396648044693 230.795857955241 L 37.36312849162 209.225675780946 L 39.329608938547 208.156500740048 L 41.296089385475 226.88403498814802 L 43.262569832402 236.54055242101498 L 45.22905027933 235.51380496110602 L 47.195530726257 257.737371882616 L 49.162011173184 236.34538554847 L 51.128491620112 260.851556327135 L 53.094972067039 249.871298367442 L 55.061452513966 240.842709133198 L 57.027932960894 184.57525122692 L 58.994413407821 181.18104474787998 L 60.960893854749 184.77041809947002 L 62.927374301676 183.46364860504002 L 64.893854748603 172.05911483546 L 66.860335195531 186.26386895024 L 68.826815642458 154.6298645656 L 70.793296089385 159.86542805952 L 72.759776536313 156.99732358473 L 74.72625698324 167.50239263735 L 76.692737430168 149.08033697237 L 78.659217877095 145.80492772009 L 80.625698324022 159.65329015458 L 82.59217877095 191.21941040964 L 84.558659217877 162.69110495332 L 86.525139664804 172.2882237728 L 88.491620111732 164.74459987313 L 90.458100558659 156.01300370581 L 92.424581005587 103.42825982929 L 94.391061452514 99.86434302629999 L 96.357541899441 100.5516698383 L 98.324022346369 103.25854950534 L 100.2905027933 106.27939327168 L 102.25698324022 82.545404467 L 104.22346368715 131.9905073504 L 106.18994413408 137.49760736265 L 108.15642458101 139.10136992399 L 110.12290502793 172.68704303409 L 112.08938547486 169.1231262311 L 114.05586592179 166.10228246475 L 116.02234636872 185.80565107556998 L 117.98882681564 194.70695756685 L 119.95530726257 197.19321381275 L 121.9217877095 173.0010071334 L 123.88826815642 179.51788357315002 L 125.85474860335 168.24063254655 L 127.82122905028 166.39927553167 L 129.78770949721 193.24744878087 L 131.75418994413 198.10964956209 L 133.72067039106 187.56215292848 L 135.68715083799 188.27493628908002 L 137.65363128492 168.54611112966 L 139.62011173184 178.20262856252998 L 141.58659217877 175.29209650675 L 143.5530726257 152.78850755072 L 145.51955307263 179.73002147809 L 147.48603351955 189.37805339476 L 149.45251396648 190.32843120888998 L 151.41899441341 223.141922345007 L 153.38547486034 217.89787333489102 L 155.35195530726 212.976273940284 L 157.31843575419 215.87832047986302 L 159.28491620112 210.286365305646 L 161.25139664804 221.76726872099601 L 163.21787709497 233.545165203263 L 165.1843575419 232.280823289821 L 167.15083798883 215.114624022079 L 169.11731843575 214.452753758666 L 171.08379888268 202.19118285314 L 173.05027932961 202.3184655961 L 175.01675977654 193.56141288018 L 176.98324022346 213.256295974805 L 178.94972067039 186.56934753336 L 180.91620111732 194.68998653446 L 182.88268156425 195.90341535072 L 184.84916201117 184.38008435438002 L 186.8156424581 191.93219377024002 L 188.78212290503 188.58890038839002 L 190.74860335196 199.56915834808 L 192.71508379888 180.80768203519 L 194.68156424581 168.98735797193 L 196.64804469274 174.73205243771 L 198.61452513966 148.05358951246 L 200.58100558659 144.30299135312 L 202.54748603352 155.25779276422 L 204.51396648045 148.2742129336 L 206.48044692737 142.31738056288 L 208.4469273743 146.86561724479 L 210.41340782123 149.14822110195 L 212.37988826816 125.18512335993 L 214.34636871508 124.51476758032001 L 216.31284916201 137.23455636052 L 218.27932960894 142.78408395375 L 220.24581005587 153.04307303665 L 222.21229050279 172.91615197142 L 224.17877094972 170.32806953115 L 226.14525139665 152.04178212533 L 228.11173184358 132.94937068073 L 230.0782122905 135.50351105621 L 232.04469273743 149.97980168931 L 234.01117318436 115.29949698972999 L 235.97765363128 99.99162576926 L 237.94413407821 101.31536629608999 L 239.91061452514 96.56347722543 L 241.87709497207 108.26500406192 L 243.84357541899 121.85031549427 L 245.81005586592 124.69296342047 L 247.77653631285 105.21870374698 L 249.74301675978 92.36314670761999 L 251.7094972067 89.79203529975001 L 253.67597765363 104.62471761315001 L 255.64245810056 152.62728274296 L 257.60893854749 215.717095672109 L 259.57541899441 204.25316328915 L 261.54189944134 179.55182563794 L 263.50837988827 142.03735852836 L 265.4748603352 148.00267641527 L 267.44134078212 139.30502231273 L 269.40782122905 135.85990273651 L 271.37430167598 135.85990273651 L 273.34078212291 129.92004139819 L 275.30726256983 105.59206645968001 L 277.27374301676 119.94107434981001 L 279.24022346369 125.59242813742 L 281.20670391061 124.61659377468999 L 283.17318435754 137.93036868872 L 285.13966480447 150.11556994847 L 287.1061452514 136.12295373863 L 289.07262569832 137.6673176866 L 291.03910614525 130.18309240032 L 293.00558659218 132.16021767436 L 294.97206703911 124.2177745134 L 296.93854748603 132.83057345397 L 298.90502793296 142.03735852836 L 300.87150837989 140.31479874025 L 302.83798882682 110.49669482189 L 304.80446927374 110.11484659300001 L 306.77094972067 110.85308650219 L 308.7374301676 115.41829421649001 L 310.70391061453 115.7492293482 L 312.67039106145 123.47104908802001 L 314.63687150838 75.71456392792999 L 316.60335195531 81.72230939583 L 318.56983240223 41.534904684000026 L 320.53631284916 39.59172147474999 L 322.50279329609 24.996633614890015 L 324.46927374302 30.401907432760026 L 326.43575418994 32.353576158199985 L 328.40223463687 32.183865834250014 L 330.3687150838 20.73690448369001 L 332.33519553073 40.87303442058999 L 334.30167597765 38.811053984579985 L 336.26815642458 42.51922456292999 L 338.23463687151 23.92745857399001 L 340.20111731844 40.966375098770015 L 342.16759776536 36.81695767814 L 344.13407821229 29.629725458769997 L 346.10055865922 15.577710635549977 L 348.06703910615 13.863636363640012 L 350.03351955307 26.091265204380022 L 352 18.021539300460006 L 352 18.021539300460006" class="highcharts-graph" data-z-index="1" stroke="#a5a5a5" stroke-width="1" stroke-linejoin="round" stroke-linecap="round" filter="none"></path></g><g class="highcharts-markers highcharts-series-0 highcharts-area-series" data-z-index="0.1" opacity="1" transform="translate(0,14) scale(1 1)" clip-path="none"></g></g><text x="198" text-anchor="middle" class="highcharts-title" data-z-index="4" style="font-size: 1em; color: rgb(51, 51, 51); font-weight: bold; fill: rgb(51, 51, 51);" y="21"></text><text x="198" text-anchor="middle" class="highcharts-subtitle" data-z-index="4" style="color: rgb(102, 102, 102); font-size: 0.8em; fill: rgb(102, 102, 102);" y="23"></text><text x="10" text-anchor="start" class="highcharts-caption" data-z-index="4" style="color: rgb(102, 102, 102); font-size: 0.8em; fill: rgb(102, 102, 102);" y="331"></text><g class="highcharts-plot-lines-5" data-z-index="5"><path fill="none" class="highcharts-plot-line " stroke="#0967e2" stroke-width="1" d="M 0 32.5 L 352 32.5"></path></g><text x="342" text-anchor="end" class="highcharts-plot-line-label " data-z-index="5" transform="translate(0,0)" y="29" style="font-size: 0.8em; font-weight: bold; color: rgb(9, 103, 226); fill: rgb(9, 103, 226);">36.86</text><g class="highcharts-axis-labels highcharts-xaxis-labels" data-z-index="7"><text x="48.968886620455216" style="color: rgb(0, 0, 0); cursor: default; font-size: 11px; fill: rgb(0, 0, 0);" text-anchor="end" transform="translate(0,0) rotate(-45 48.968886620455216 333)" y="333" opacity="1"><title>September'24</title>September'…</text><text x="108.94654236901067" style="color: rgb(0, 0, 0); cursor: default; font-size: 11px; fill: rgb(0, 0, 0);" text-anchor="end" transform="translate(0,0) rotate(-45 108.94654236901067 333)" y="333" opacity="1">November'24</text><text x="168.96516434293068" style="color: rgb(0, 0, 0); cursor: default; font-size: 11px; fill: rgb(0, 0, 0);" text-anchor="end" transform="translate(0,0) rotate(-45 168.96516434293068 333)" y="333" opacity="1">January'25</text><text x="226.9763375272907" style="color: rgb(0, 0, 0); cursor: default; font-size: 11px; fill: rgb(0, 0, 0);" text-anchor="end" transform="translate(0,0) rotate(-45 226.9763375272907 333)" y="333" opacity="1">March'25</text><text x="286.9130228159307" style="color: rgb(0, 0, 0); cursor: default; font-size: 11px; fill: rgb(0, 0, 0);" text-anchor="end" transform="translate(0,0) rotate(-45 286.9130228159307 333)" y="333" opacity="1">May'25</text><text x="346.89067644722064" style="color: rgb(0, 0, 0); cursor: default; font-size: 11px; fill: rgb(0, 0, 0);" text-anchor="end" transform="translate(0,0) rotate(-45 346.89067644722064 333)" y="333" opacity="1">July'25</text></g><g class="highcharts-axis-labels highcharts-yaxis-labels" data-z-index="7"></g><g class="highcharts-axis-labels highcharts-yaxis-labels" data-z-index="7"><text x="360" style="color: rgb(0, 0, 0); cursor: default; font-size: 0.8em; fill: rgb(0, 0, 0);" text-anchor="start" transform="translate(0,0)" y="276" opacity="1">28</text><text x="360" style="color: rgb(0, 0, 0); cursor: default; font-size: 0.8em; fill: rgb(0, 0, 0);" text-anchor="start" transform="translate(0,0)" y="167" opacity="1">32</text><text x="360" style="color: rgb(0, 0, 0); cursor: default; font-size: 0.8em; fill: rgb(0, 0, 0);" text-anchor="start" transform="translate(0,0)" y="58" opacity="1">36</text></g></svg></div></div><a id="jschart_timestamp" class="bvchart__timestamp" href="https://www.bullionvaultaffiliate.com/MYUSERNAME/en/help/custom_gold_price_charts.html" target="_blank" style="font-size: 9px; padding: 2px; right: 0px; display: block;">Embed</a></div></div>
	
	</body></html>