!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("react")):"function"==typeof define&&define.amd?define("rb_wixui.thunderbolt[Container_DefaultAreaSkin]",["react"],t):"object"==typeof exports?exports["rb_wixui.thunderbolt[Container_DefaultAreaSkin]"]=t(require("react")):e["rb_wixui.thunderbolt[Container_DefaultAreaSkin]"]=t(e.React)}("undefined"!=typeof self?self:this,(function(e){return function(){var t={5329:function(t){"use strict";t.exports=e},448:function(e){function t(){return e.exports=t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,t.apply(null,arguments)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}},r={};function n(e){var o=r[e];if(void 0!==o)return o.exports;var a=r[e]={exports:{}};return t[e](a,a.exports,n),a.exports}n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,{a:t}),t},n.d=function(e,t){for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var o={};return function(){"use strict";n.r(o),n.d(o,{components:function(){return N}});var e=n(448),t=n.n(e),r=n(5329),a=n.n(r);function i(e){var t,r,n="";if("string"==typeof e||"number"==typeof e)n+=e;else if("object"==typeof e)if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(r=i(e[t]))&&(n&&(n+=" "),n+=r);else for(t in e)e[t]&&(n&&(n+=" "),n+=t);return n}var s=function(){for(var e,t,r=0,n="";r<arguments.length;)(e=arguments[r++])&&(t=i(e))&&(n&&(n+=" "),n+=t);return n};const l="wixui-",c=(e,...t)=>{const r=[];return e&&r.push(`${l}${e}`),t.forEach((e=>{e&&(r.push(`${l}${e}`),r.push(e))})),r.join(" ")};var u={root:"box"};const d=e=>Object.entries(e).reduce(((e,[t,r])=>(t.includes("data-")&&(e[t]=r),e)),{});const f=13,p=27;function m(e){return t=>{t.keyCode===e&&(t.preventDefault(),t.stopPropagation(),t.currentTarget.click())}}m(32),m(f),m(p);const b=["aria-id","aria-metadata","aria-type"],v=(e,t)=>Object.entries(e).reduce(((e,[r,n])=>(t.includes(r)||(e[r]=n),e)),{}),y="mesh-container-content",h="inline-content",x=e=>a().createElement("div",{key:e.props.id+"-rotated-wrapper","data-mesh-id":e.props.id+"-rotated-wrapper"},e),g=(e,r)=>{const{id:n,className:o,wedges:i=[],rotatedComponents:l=[],children:c,fixedComponents:u=[],extraClassName:f="",renderRotatedComponents:p=x}=e,m=a().Children.toArray(c()),b=[],v=[];m.forEach((e=>u.includes(e.props.id)?b.push(e):v.push(e)));const g=(e=>{const{wedges:t,rotatedComponents:r,childrenArray:n,renderRotatedComponents:o}=e,i=r.reduce(((e,t)=>({...e,[t]:!0})),{});return[...n.map((e=>{return i[(t=e,t.props.id.split("__")[0])]?o(e):e;var t})),...t.map((e=>a().createElement("div",{key:e,"data-mesh-id":e})))]})({childrenArray:v,rotatedComponents:l,wedges:i,renderRotatedComponents:p});return a().createElement("div",t()({},d(e),{"data-mesh-id":n+"inlineContent","data-testid":h,className:s(o,f),ref:r}),a().createElement("div",{"data-mesh-id":n+"inlineContent-gridContainer","data-testid":y},g),b)};var C=a().forwardRef(g),w="J6KGih";const _="container-bg",j=(e,n)=>{const{id:o,className:a,meshProps:i,renderSlot:l,children:c,onClick:u,onKeyPress:f,onDblClick:p,onFocus:m,onBlur:y,onMouseEnter:h,onMouseLeave:x,translations:g,hasPlatformClickHandler:_,a11y:j={},ariaAttributes:E={},tabIndex:k,role:O,style:S,lang:A}=e,N=r.useRef(null),{"aria-label-interactions":D,...M}=j;D&&(M["aria-label"]=(null==g?void 0:g.ariaLabel)||"Interactive element, focus to trigger content change");const P={id:o,children:c,...i},R=s(a,{[w]:_});return r.useImperativeHandle(n,(()=>({focus:()=>{var e;null==(e=N.current)||e.focus()},blur:()=>{var e;null==(e=N.current)||e.blur()}}))),r.createElement("div",t()({id:o},d(e),{ref:N,className:R,onClick:u,onKeyDown:e=>{f&&(" "===e.key&&e.preventDefault(),f(e))},onFocus:m,onBlur:y,onDoubleClick:p,onMouseEnter:h,onMouseLeave:x,style:S,lang:A},M,(({role:e,tabIndex:t,tabindex:r,...n}={})=>{const o=Object.entries(n).reduce(((e,[t,r])=>({...e,[`aria-${t}`.toLowerCase()]:r})),{role:e,tabIndex:t??r});return Object.keys(o).forEach((e=>{void 0!==o[e]&&null!==o[e]||delete o[e]})),v(o,b)})({...E,tabIndex:k,role:O})),l({containerChildren:r.createElement(C,P)}))},E=r.forwardRef(j),k=(e,n)=>{let{classes:o,className:a,customClassNames:i=[],...l}=e;return r.createElement(E,t()({},l,{ref:n,className:s(o.root,a),renderSlot:e=>{let{containerChildren:t}=e;return r.createElement(r.Fragment,null,r.createElement("div",{className:s(o.bg,c(u.root,...i)),"data-testid":_}),t)}}))},O=r.forwardRef(k);var S={root:"KaEeLN",bg:"uYj0Sg"};const A=(e,n)=>r.createElement(O,t()({ref:n},e,{classes:S}));const N={Container_DefaultAreaSkin:{component:r.forwardRef(A)}}}(),o}()}));
//# sourceMappingURL=https://static.parastorage.com/services/editor-elements-library/dist/thunderbolt/rb_wixui.thunderbolt[Container_DefaultAreaSkin].42a7990c.bundle.min.js.map