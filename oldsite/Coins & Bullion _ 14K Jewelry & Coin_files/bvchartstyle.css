#jsChart {
	display: flex;
}

.bvchart {
	position: relative;
	flex-grow: 1;
}

.bvchart__title {
	color: black;
	text-align: center;
	font-family: 'Arial', sans-serif;
}

.bvchart__metal,
.bvchart__time {
	background-color: #e8e8e8;
	border: 1px solid #c0c0c0;
	border-radius: 3px;
	color: black;
	font-size: 12px;
	line-height: 21px;
	padding: 0 3px;
	font-weight: bold;
}

.bvchart--clicked {
	color: white;
	background-color: #434343;
	border-color: #434343;
}

.bvchart__export {
	color: black;
	background-color: #f4f4f4;
	border: 1px solid black;
	padding: 0 2px;
	border-radius: 3px;
	font-size: 12px;
	line-height: 21px;
	font-weight: bold;
}

.bvchart__pricealert {
	color: white;
	background-color: #c7aa50;
	border: 1px solid #c7aa50;
	padding: 0 5px;
	border-radius: 3px;
	font-size: 12px;
	line-height: 21px;
	font-weight: bold;
}

.bvchart__popup {
	color: black;
	background-color: #f4f4f4;
	border: 1px solid black;
	padding: 0 5px;
	border-radius: 3px;
	font-size: 12px;
	line-height: 21px;
}

.bvchart__list {
	color: black;
	background-color: white;
	padding: 2px;
	border-radius: 3px;
	font-size: 16px;
	border: medium none blue;
}

.bvchart__controlbar {
	background-color: #f4f4f4;
	width: 100%;
	box-sizing: border-box;
	display: flex;
	gap: 8px;
	flex-wrap: nowrap;
	font-family: "Arial CE", "Helvetica CE", Arial, helvetica, sans-serif;
	font-weight: bolder;
	white-space: nowrap;
	padding: 6px 20px;
}

.bvchart__controlgroup {
	display: flex;
	gap: 4px;
}

.bvchart__controlgroup--right {
	position: absolute;
	right: 20px;
}

.bvchart__credits a:link,
.bvchart__credits a:visited {
	color: black;
	text-decoration: none;
	font-family: "Arial CE", "Helvetica CE", Arial, helvetica, sans-serif;
	font-size: 12px;
}

.bvchart__credits a:hover {
	color: black;
	text-decoration: underline;
	font-family: "Arial CE", "Helvetica CE", Arial, helvetica, sans-serif;
	font-size: 12px;
}

.bvchart__credits {
	width: 128px;
	right: 8px;
	bottom: 8px;
	position: absolute;
	border: 2px solid #8d959b;
	display: none;
	z-index: 4;
	margin-right: 2px;
	margin-bottom: 2px;
	opacity: 0.0;
	filter: alpha(opacity = 0); /* For IE8 and earlier */
	padding-bottom: 30px;
}

.bvchart__container {
	background-color: #ffffff;
}

.bvchart__timestamp {
	font-family: "Arial CE", "Helvetica CE", Arial, helvetica, sans-serif;
	border: 0 solid blue;
	position: absolute;
	bottom: 0;
	display: none;
}

.bvchart__creditLinks {
	display: none;
	background-color: white;
	padding: 4px;
	padding-top: 4px;
}

.bvchart__creditLogo {
	display: none;
	position: absolute;
	bottom: 8px;
	right: 8px;
	width: 120px;
	height: 21px;
	margin: 2px 5px 6px;
	z-index: 5;
}

.bvchart__timestamp a:link,
.bvchart__timestamp a:visited {
	color: black;
	text-decoration: underline;
}

.bvchart__timestamp a:hover {
	color: black;
	text-decoration: underline;
}

.bvchart--hidden {
	display: none;
}

@media (min-width: 768px) {

	.bvchart--hidden-md {
		display: none;
	}

	.bvchart--visible-md {
		display: block;
	}

	.bvchart--visible-md--flex {
		display: flex;
	}

	.bvchart__list {
		font-size: 12px;
	}
}

@media (min-width: 992px) {

	.bvchart--visible-lg {
		display: block;
	}
}
