<?php
/**
 * Simple File-based Caching System - 14K Jewelry & Coin Exchange
 * Basic caching functionality for improved performance
 */

class SimpleCache {
    private $cacheDir;
    private $defaultTTL;
    
    public function __construct($cacheDir = null, $defaultTTL = 3600) {
        $this->cacheDir = $cacheDir ?: sys_get_temp_dir() . '/14k_cache';
        $this->defaultTTL = $defaultTTL;
        
        // Create cache directory if it doesn't exist
        if (!is_dir($this->cacheDir)) {
            mkdir($this->cacheDir, 0755, true);
        }
    }
    
    /**
     * Get cached data
     */
    public function get($key) {
        $filename = $this->getCacheFilename($key);
        
        if (!file_exists($filename)) {
            return null;
        }
        
        $data = file_get_contents($filename);
        $cached = unserialize($data);
        
        // Check if cache has expired
        if ($cached['expires'] < time()) {
            unlink($filename);
            return null;
        }
        
        return $cached['data'];
    }
    
    /**
     * Set cached data
     */
    public function set($key, $data, $ttl = null) {
        $ttl = $ttl ?: $this->defaultTTL;
        $filename = $this->getCacheFilename($key);
        
        $cached = [
            'data' => $data,
            'expires' => time() + $ttl,
            'created' => time()
        ];
        
        return file_put_contents($filename, serialize($cached)) !== false;
    }
    
    /**
     * Delete cached data
     */
    public function delete($key) {
        $filename = $this->getCacheFilename($key);
        
        if (file_exists($filename)) {
            return unlink($filename);
        }
        
        return true;
    }
    
    /**
     * Clear all cache
     */
    public function clear() {
        $files = glob($this->cacheDir . '/*.cache');
        
        foreach ($files as $file) {
            unlink($file);
        }
        
        return true;
    }
    
    /**
     * Get cache filename for a key
     */
    private function getCacheFilename($key) {
        $hash = md5($key);
        return $this->cacheDir . '/' . $hash . '.cache';
    }
    
    /**
     * Cache a function result
     */
    public function remember($key, $callback, $ttl = null) {
        $cached = $this->get($key);
        
        if ($cached !== null) {
            return $cached;
        }
        
        $result = $callback();
        $this->set($key, $result, $ttl);
        
        return $result;
    }
    
    /**
     * Get cache statistics
     */
    public function getStats() {
        $files = glob($this->cacheDir . '/*.cache');
        $totalSize = 0;
        $expiredCount = 0;
        $validCount = 0;
        
        foreach ($files as $file) {
            $totalSize += filesize($file);
            
            $data = file_get_contents($file);
            $cached = unserialize($data);
            
            if ($cached['expires'] < time()) {
                $expiredCount++;
            } else {
                $validCount++;
            }
        }
        
        return [
            'total_files' => count($files),
            'valid_files' => $validCount,
            'expired_files' => $expiredCount,
            'total_size' => $totalSize,
            'cache_dir' => $this->cacheDir
        ];
    }
    
    /**
     * Clean expired cache files
     */
    public function cleanup() {
        $files = glob($this->cacheDir . '/*.cache');
        $cleaned = 0;
        
        foreach ($files as $file) {
            $data = file_get_contents($file);
            $cached = unserialize($data);
            
            if ($cached['expires'] < time()) {
                unlink($file);
                $cleaned++;
            }
        }
        
        return $cleaned;
    }
}

// Global cache instance
$cache = new SimpleCache();

/**
 * Helper function to get cache instance
 */
function getCache() {
    global $cache;
    return $cache;
}

/**
 * Helper function for quick caching
 */
function cache_remember($key, $callback, $ttl = 3600) {
    return getCache()->remember($key, $callback, $ttl);
}

/**
 * Helper function to cache database queries
 */
function cache_query($sql, $params = [], $ttl = 300) {
    global $pdo;
    
    if (!$pdo) {
        return [];
    }
    
    $cacheKey = 'query_' . md5($sql . serialize($params));
    
    return cache_remember($cacheKey, function() use ($pdo, $sql, $params) {
        try {
            $stmt = $pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Cache query error: " . $e->getMessage());
            return [];
        }
    }, $ttl);
}
?>
