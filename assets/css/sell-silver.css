/**
 * Sell Silver Page Styles - 14K Jewelry & Coin Exchange
 * Specific styles for the sell silver page
 */

/* Page Header */
.page-header {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.page-title {
    font-family: 'Playfair Display', serif;
    font-size: 3rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.page-subtitle {
    font-size: 1.25rem;
    color: #6c757d;
    margin-bottom: 1rem;
}

.page-description {
    font-size: 1.1rem;
    color: #495057;
    line-height: 1.6;
}

/* Breadcrumb Navigation */
.breadcrumb-nav {
    background-color: #f8f9fa;
    padding: 1rem 0;
    border-bottom: 1px solid #dee2e6;
}

.breadcrumb {
    margin-bottom: 0;
    background: none;
    padding: 0;
}

.breadcrumb-item a {
    color: #6c757d;
    text-decoration: none;
}

.breadcrumb-item a:hover {
    color: #495057;
    text-decoration: underline;
}

.breadcrumb-item.active {
    color: #495057;
}

/* Silver Types */
.silver-types {
    background-color: #fff;
}

.silver-card {
    background: #fff;
    padding: 2rem 1.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
}

.silver-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.silver-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #6c757d, #495057);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.silver-icon i {
    font-size: 2rem;
    color: white;
}

.silver-card h3 {
    font-family: 'Playfair Display', serif;
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1.5rem;
}

.silver-list {
    list-style: none;
    padding: 0;
    margin: 0;
    text-align: left;
}

.silver-list li {
    padding: 0.5rem 0;
    color: #495057;
    position: relative;
    padding-left: 1.5rem;
    border-bottom: 1px solid #f8f9fa;
}

.silver-list li:last-child {
    border-bottom: none;
}

.silver-list li:before {
    content: '●';
    position: absolute;
    left: 0;
    color: #6c757d;
    font-weight: bold;
}

/* Silver Pricing */
.silver-pricing {
    background-color: #f8f9fa;
}

.pricing-features {
    margin-top: 2rem;
}

.pricing-feature {
    display: flex;
    align-items: flex-start;
    margin-bottom: 2rem;
    padding: 1rem;
    background: #fff;
    border-radius: 8px;
    transition: background-color 0.3s ease;
}

.pricing-feature:hover {
    background: #e9ecef;
}

.pricing-feature i {
    font-size: 2rem;
    margin-right: 1rem;
    margin-top: 0.25rem;
    flex-shrink: 0;
}

.pricing-feature h4 {
    font-family: 'Playfair Display', serif;
    font-size: 1.25rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.pricing-feature p {
    color: #6c757d;
    margin: 0;
    line-height: 1.6;
}

/* Price Calculator */
.price-calculator {
    background: #fff;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.price-calculator h3 {
    font-family: 'Playfair Display', serif;
    font-size: 1.75rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.calculator-note {
    display: flex;
    align-items: flex-start;
    text-align: left;
    margin-top: 1.5rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.calculator-note i {
    color: #007bff;
    margin-right: 0.75rem;
    margin-top: 0.25rem;
    flex-shrink: 0;
}

.calculator-note p {
    margin: 0;
    color: #6c757d;
    font-size: 0.9rem;
    line-height: 1.5;
}

/* CTA Section */
.silver-cta {
    background: linear-gradient(135deg, #007bff, #0056b3);
}

.silver-cta h2 {
    font-family: 'Playfair Display', serif;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.silver-cta .lead {
    font-size: 1.25rem;
    opacity: 0.9;
}

.cta-buttons .btn {
    margin: 0.5rem;
    padding: 0.75rem 2rem;
    font-weight: 600;
    border-radius: 50px;
    transition: all 0.3s ease;
}

.cta-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Section Titles */
.section-title {
    font-family: 'Playfair Display', serif;
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.25rem;
    color: #6c757d;
    margin-bottom: 2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-title {
        font-size: 2.5rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .silver-cta h2 {
        font-size: 2rem;
    }
    
    .cta-buttons .btn {
        display: block;
        width: 100%;
        margin: 0.5rem 0;
    }
    
    .silver-card {
        padding: 1.5rem 1rem;
        margin-bottom: 2rem;
    }
    
    .silver-icon {
        width: 60px;
        height: 60px;
    }
    
    .silver-icon i {
        font-size: 1.5rem;
    }
    
    .pricing-feature {
        flex-direction: column;
        text-align: center;
    }
    
    .pricing-feature i {
        margin-right: 0;
        margin-bottom: 1rem;
    }
    
    .price-calculator {
        padding: 1.5rem;
        margin-top: 2rem;
    }
    
    .calculator-note {
        flex-direction: column;
        text-align: center;
    }
    
    .calculator-note i {
        margin-right: 0;
        margin-bottom: 0.5rem;
    }
}

@media (max-width: 576px) {
    .page-header {
        padding: 60px 0;
    }
    
    .page-title {
        font-size: 2rem;
    }
    
    .silver-card {
        padding: 1rem;
    }
    
    .silver-list {
        text-align: center;
    }
    
    .price-calculator {
        padding: 1rem;
    }
}
