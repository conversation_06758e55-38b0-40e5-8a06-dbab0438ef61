/**
 * Contact Page Styles - 14K Jewelry & Coin Exchange
 * Specific styles for the contact page
 */

/* Page Header */
.page-header {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.page-title {
    font-family: 'Playfair Display', serif;
    font-size: 3rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.page-subtitle {
    font-size: 1.25rem;
    color: #6c757d;
    margin-bottom: 1rem;
}

.page-description {
    font-size: 1.1rem;
    color: #495057;
    line-height: 1.6;
}

/* Breadcrumb Navigation */
.breadcrumb-nav {
    background-color: #f8f9fa;
    padding: 1rem 0;
    border-bottom: 1px solid #dee2e6;
}

.breadcrumb {
    margin-bottom: 0;
    background: none;
    padding: 0;
}

.breadcrumb-item a {
    color: #6c757d;
    text-decoration: none;
}

.breadcrumb-item a:hover {
    color: #495057;
    text-decoration: underline;
}

.breadcrumb-item.active {
    color: #495057;
}

/* Contact Information Cards */
.contact-info {
    background-color: #fff;
}

.contact-card {
    background: #fff;
    padding: 2rem 1.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
}

.contact-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.contact-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.contact-icon i {
    font-size: 2rem;
    color: white;
}

.contact-card h4 {
    font-family: 'Playfair Display', serif;
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.contact-card p {
    color: #6c757d;
    margin-bottom: 1rem;
    line-height: 1.6;
}

.contact-card a {
    color: #007bff;
    text-decoration: none;
}

.contact-card a:hover {
    color: #0056b3;
    text-decoration: underline;
}

/* Contact Form */
.contact-form {
    background-color: #f8f9fa;
}

.form-container {
    background: #fff;
    padding: 3rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.form-container h2 {
    font-family: 'Playfair Display', serif;
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 2rem;
}

.contact-form-fields .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.contact-form-fields .form-control,
.contact-form-fields .form-select {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.contact-form-fields .form-control:focus,
.contact-form-fields .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.contact-form-fields .btn-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border: none;
    padding: 0.75rem 2rem;
    font-weight: 600;
    border-radius: 50px;
    transition: all 0.3s ease;
}

.contact-form-fields .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

/* Map Section */
.map-section {
    position: relative;
}

.map-placeholder {
    position: relative;
    height: 400px;
    background: #e9ecef;
    overflow: hidden;
}

.map-iframe-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #6c757d;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
}

.map-iframe-placeholder i {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: #007bff;
}

.map-iframe-placeholder p {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.map-iframe-placeholder small {
    font-size: 0.9rem;
    opacity: 0.8;
}

.map-overlay {
    position: absolute;
    top: 20px;
    left: 20px;
    background: rgba(255, 255, 255, 0.95);
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    z-index: 10;
    max-width: 300px;
}

.map-info h3 {
    font-family: 'Playfair Display', serif;
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.map-info p {
    color: #6c757d;
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.map-info .btn {
    margin-top: 1rem;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

/* Alert Styles */
.alert {
    border-radius: 8px;
    border: none;
    padding: 1rem 1.5rem;
    margin-bottom: 2rem;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert ul {
    padding-left: 1rem;
}

/* Section Titles */
.section-title {
    font-family: 'Playfair Display', serif;
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.25rem;
    color: #6c757d;
    margin-bottom: 2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-title {
        font-size: 2.5rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .form-container {
        padding: 2rem 1.5rem;
    }
    
    .form-container h2 {
        font-size: 2rem;
    }
    
    .contact-card {
        padding: 1.5rem 1rem;
        margin-bottom: 2rem;
    }
    
    .contact-icon {
        width: 60px;
        height: 60px;
    }
    
    .contact-icon i {
        font-size: 1.5rem;
    }
    
    .map-overlay {
        position: relative;
        top: auto;
        left: auto;
        margin: 1rem;
        max-width: none;
    }
}

@media (max-width: 576px) {
    .page-header {
        padding: 60px 0;
    }
    
    .page-title {
        font-size: 2rem;
    }
    
    .form-container {
        padding: 1.5rem 1rem;
    }
    
    .contact-form-fields .btn-primary {
        width: 100%;
    }
    
    .map-placeholder {
        height: 300px;
    }
}
