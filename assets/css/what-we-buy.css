/**
 * What We Buy Page Styles - 14K Jewelry & Coin Exchange
 * Specific styles for the what we buy page
 */

/* Page Header */
.page-header {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.page-title {
    font-family: 'Playfair Display', serif;
    font-size: 3rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.page-subtitle {
    font-size: 1.25rem;
    color: #6c757d;
    margin-bottom: 1rem;
}

.page-description {
    font-size: 1.1rem;
    color: #495057;
    line-height: 1.6;
}

/* Breadcrumb Navigation */
.breadcrumb-nav {
    background-color: #f8f9fa;
    padding: 1rem 0;
    border-bottom: 1px solid #dee2e6;
}

.breadcrumb {
    margin-bottom: 0;
    background: none;
    padding: 0;
}

.breadcrumb-item a {
    color: #6c757d;
    text-decoration: none;
}

.breadcrumb-item a:hover {
    color: #495057;
    text-decoration: underline;
}

.breadcrumb-item.active {
    color: #495057;
}

/* Buy Categories */
.buy-categories {
    background-color: #fff;
}

.buy-category-card {
    background: #fff;
    padding: 2rem 1.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
    text-align: center;
}

.buy-category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.category-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #28a745, #20c997);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.category-icon i {
    font-size: 2rem;
    color: white;
}

.buy-category-card h3 {
    font-family: 'Playfair Display', serif;
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1.5rem;
}

.category-list {
    list-style: none;
    padding: 0;
    margin: 0;
    text-align: left;
}

.category-list li {
    padding: 0.5rem 0;
    color: #495057;
    position: relative;
    padding-left: 1.5rem;
    border-bottom: 1px solid #f8f9fa;
}

.category-list li:last-child {
    border-bottom: none;
}

.category-list li:before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #28a745;
    font-weight: bold;
}

/* Selling Process */
.selling-process {
    background-color: #f8f9fa;
}

.process-step {
    padding: 2rem 1rem;
}

.step-number {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: bold;
    margin: 0 auto 1rem;
}

.process-step h4 {
    font-family: 'Playfair Display', serif;
    font-size: 1.25rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.process-step p {
    color: #6c757d;
    line-height: 1.6;
}

/* CTA Section */
.sell-cta {
    background: linear-gradient(135deg, #007bff, #0056b3);
}

.sell-cta h2 {
    font-family: 'Playfair Display', serif;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.sell-cta .lead {
    font-size: 1.25rem;
    opacity: 0.9;
}

.cta-buttons .btn {
    margin: 0.5rem;
    padding: 0.75rem 2rem;
    font-weight: 600;
    border-radius: 50px;
    transition: all 0.3s ease;
}

.cta-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Section Titles */
.section-title {
    font-family: 'Playfair Display', serif;
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.25rem;
    color: #6c757d;
    margin-bottom: 2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-title {
        font-size: 2.5rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .sell-cta h2 {
        font-size: 2rem;
    }
    
    .cta-buttons .btn {
        display: block;
        width: 100%;
        margin: 0.5rem 0;
    }
    
    .buy-category-card {
        padding: 1.5rem 1rem;
        margin-bottom: 2rem;
    }
    
    .category-icon {
        width: 60px;
        height: 60px;
    }
    
    .category-icon i {
        font-size: 1.5rem;
    }
    
    .process-step {
        padding: 1.5rem 0.5rem;
    }
}

@media (max-width: 576px) {
    .page-header {
        padding: 60px 0;
    }
    
    .page-title {
        font-size: 2rem;
    }
    
    .buy-category-card {
        padding: 1rem;
    }
    
    .category-list {
        text-align: center;
    }
}
