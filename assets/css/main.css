/* 14K Jewelry & Coin Exchange - Main Styles */

/* CSS Variables */
:root {
    --primary-color: #d4af37;
    --secondary-color: #2c3e50;
    --accent-color: #e74c3c;
    --text-dark: #2c3e50;
    --text-light: #7f8c8d;
    --bg-light: #f8f9fa;
    --bg-dark: #2c3e50;
    --border-color: #dee2e6;
    --shadow: 0 2px 10px rgba(0,0,0,0.1);
    --shadow-hover: 0 5px 20px rgba(0,0,0,0.15);
    --transition: all 0.3s ease;
    --border-radius: 8px;
    --font-primary: 'Open Sans', sans-serif;
    --font-heading: 'Playfair Display', serif;
}

/* Base Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: var(--font-primary);
    line-height: 1.6;
    color: var(--text-dark);
    background-color: #fff;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-heading);
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1rem;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

.section-title {
    color: var(--text-dark);
    margin-bottom: 1rem;
    position: relative;
}

.section-title::after {
    content: '';
    display: block;
    width: 60px;
    height: 3px;
    background: var(--primary-color);
    margin: 1rem auto 0;
}

.section-subtitle {
    color: var(--text-light);
    font-size: 1.1rem;
    margin-bottom: 2rem;
}

/* Buttons */
.btn {
    border-radius: var(--border-radius);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: var(--transition);
    border: none;
    padding: 0.75rem 1.5rem;
}

.btn-primary {
    background: var(--primary-color);
    color: #fff;
    box-shadow: var(--shadow);
}

.btn-primary:hover {
    background: #b8941f;
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: #fff;
    transform: translateY(-2px);
}

.btn-outline-light {
    border: 2px solid #fff;
    color: #fff;
    background: transparent;
}

.btn-outline-light:hover {
    background: #fff;
    color: var(--text-dark);
    transform: translateY(-2px);
}

/* Navigation */
.top-bar {
    font-size: 0.9rem;
}

.top-bar a:hover {
    color: var(--primary-color) !important;
}

.navbar {
    padding: 1rem 0;
    transition: var(--transition);
}

.navbar-brand {
    display: flex;
    align-items: center;
    font-weight: 700;
}

.logo-placeholder {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-color), #b8941f);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
}

.logo-icon {
    color: white;
    font-weight: bold;
    font-size: 1.2rem;
    font-family: var(--font-heading);
}

.brand-text strong {
    font-size: 1.5rem;
    color: var(--text-dark);
    line-height: 1.2;
}

.brand-text small {
    font-size: 0.8rem;
    font-style: italic;
}

.navbar-nav .nav-link {
    font-weight: 600;
    color: var(--text-dark);
    margin: 0 0.5rem;
    transition: var(--transition);
    position: relative;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color);
}

.navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: var(--transition);
    transform: translateX(-50%);
}

.navbar-nav .nav-link:hover::after {
    width: 100%;
}

.dropdown-menu {
    border: none;
    box-shadow: var(--shadow);
    border-radius: var(--border-radius);
    padding: 0.5rem 0;
}

.dropdown-item {
    padding: 0.75rem 1.5rem;
    transition: var(--transition);
}

.dropdown-item:hover {
    background: var(--bg-light);
    color: var(--primary-color);
}

/* Cards */
.service-card {
    background: #fff;
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--shadow);
    transition: var(--transition);
    height: 100%;
    border: 1px solid var(--border-color);
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.service-icon {
    width: 80px;
    height: 80px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: #fff;
    font-size: 2rem;
}

.service-card h3 {
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.service-card p {
    color: var(--text-light);
    margin-bottom: 1.5rem;
}

/* Feature List */
.feature-list {
    margin-top: 2rem;
}

.feature-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 2rem;
}

.feature-item i {
    font-size: 1.5rem;
    margin-right: 1rem;
    margin-top: 0.25rem;
    flex-shrink: 0;
}

.feature-item h4 {
    margin-bottom: 0.5rem;
    color: var(--text-dark);
}

.feature-item p {
    color: var(--text-light);
    margin-bottom: 0;
}

/* Footer */
.footer {
    background: var(--bg-dark) !important;
}

.footer h5, .footer h6 {
    color: var(--primary-color);
}

.footer .contact-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.footer .contact-item i {
    width: 20px;
    flex-shrink: 0;
}

.newsletter-signup .form-control {
    border-radius: var(--border-radius);
}

/* Responsive Design */
@media (max-width: 768px) {
    h1 { font-size: 2rem; }
    h2 { font-size: 1.75rem; }
    h3 { font-size: 1.5rem; }

    .service-card {
        margin-bottom: 2rem;
    }

    .feature-item {
        text-align: center;
        flex-direction: column;
    }

    .feature-item i {
        margin-right: 0;
        margin-bottom: 1rem;
    }
}

/* Utility Classes */
.text-primary { color: var(--primary-color) !important; }
.bg-primary { background-color: var(--primary-color) !important; }
.border-primary { border-color: var(--primary-color) !important; }

.shadow-custom { box-shadow: var(--shadow); }
.shadow-hover-custom { box-shadow: var(--shadow-hover); }

.transition { transition: var(--transition); }

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}