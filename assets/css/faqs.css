/**
 * FAQs Page Styles - 14K Jewelry & Coin Exchange
 * Specific styles for the FAQs page
 */

/* Page Header */
.page-header {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.page-title {
    font-family: 'Playfair Display', serif;
    font-size: 3rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.page-subtitle {
    font-size: 1.25rem;
    color: #6c757d;
    margin-bottom: 1rem;
}

.page-description {
    font-size: 1.1rem;
    color: #495057;
    line-height: 1.6;
}

/* Breadcrumb Navigation */
.breadcrumb-nav {
    background-color: #f8f9fa;
    padding: 1rem 0;
    border-bottom: 1px solid #dee2e6;
}

.breadcrumb {
    margin-bottom: 0;
    background: none;
    padding: 0;
}

.breadcrumb-item a {
    color: #6c757d;
    text-decoration: none;
}

.breadcrumb-item a:hover {
    color: #495057;
    text-decoration: underline;
}

.breadcrumb-item.active {
    color: #495057;
}

/* FAQs Section */
.faqs-section {
    background-color: #fff;
}

.faq-category {
    margin-bottom: 3rem;
}

.category-title {
    font-family: 'Playfair Display', serif;
    font-size: 2rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 2rem;
    padding-bottom: 0.5rem;
    border-bottom: 3px solid #007bff;
    display: inline-block;
}

/* Accordion Styles */
.accordion {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.accordion-item {
    border: none;
    border-bottom: 1px solid #e9ecef;
}

.accordion-item:last-child {
    border-bottom: none;
}

.accordion-header {
    margin-bottom: 0;
}

.accordion-button {
    background-color: #f8f9fa;
    border: none;
    padding: 1.5rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    transition: all 0.3s ease;
    box-shadow: none;
}

.accordion-button:not(.collapsed) {
    background-color: #007bff;
    color: white;
    box-shadow: none;
}

.accordion-button:focus {
    box-shadow: none;
    border-color: transparent;
}

.accordion-button::after {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23495057'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
    transition: transform 0.3s ease;
}

.accordion-button:not(.collapsed)::after {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='white'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
    transform: rotate(-180deg);
}

.accordion-body {
    padding: 2rem;
    background-color: #fff;
    color: #6c757d;
    line-height: 1.7;
    font-size: 1rem;
    border-top: 1px solid #e9ecef;
}

/* FAQ CTA Section */
.faq-cta {
    background-color: #f8f9fa;
}

.faq-cta h2 {
    font-family: 'Playfair Display', serif;
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.faq-cta .lead {
    font-size: 1.25rem;
    color: #6c757d;
}

.cta-buttons .btn {
    margin: 0.5rem;
    padding: 0.75rem 2rem;
    font-weight: 600;
    border-radius: 50px;
    transition: all 0.3s ease;
}

.cta-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Hover Effects */
.accordion-item:hover .accordion-button:not(.collapsed) {
    background-color: #0056b3;
}

.accordion-item:hover .accordion-button.collapsed {
    background-color: #e9ecef;
}

/* Section Titles */
.section-title {
    font-family: 'Playfair Display', serif;
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.25rem;
    color: #6c757d;
    margin-bottom: 2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-title {
        font-size: 2.5rem;
    }
    
    .category-title {
        font-size: 1.75rem;
    }
    
    .faq-cta h2 {
        font-size: 2rem;
    }
    
    .cta-buttons .btn {
        display: block;
        width: 100%;
        margin: 0.5rem 0;
    }
    
    .accordion-button {
        padding: 1rem 1.5rem;
        font-size: 1rem;
    }
    
    .accordion-body {
        padding: 1.5rem;
    }
}

@media (max-width: 576px) {
    .page-header {
        padding: 60px 0;
    }
    
    .page-title {
        font-size: 2rem;
    }
    
    .category-title {
        font-size: 1.5rem;
    }
    
    .accordion-button {
        padding: 1rem;
        font-size: 0.95rem;
    }
    
    .accordion-body {
        padding: 1rem;
        font-size: 0.95rem;
    }
    
    .faq-category {
        margin-bottom: 2rem;
    }
}

/* Animation for accordion */
.accordion-collapse {
    transition: height 0.35s ease;
}

/* Focus states for accessibility */
.accordion-button:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* Print styles */
@media print {
    .accordion-button::after {
        display: none;
    }
    
    .accordion-collapse {
        display: block !important;
        height: auto !important;
    }
    
    .accordion-body {
        border-top: none;
        padding-top: 0;
    }
}
