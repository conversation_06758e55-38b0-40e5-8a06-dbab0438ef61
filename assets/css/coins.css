/**
 * Coins & Bullion Page Styles - 14K Jewelry & Coin Exchange
 * Specific styles for the coins and bullion page
 */

/* Page Header */
.page-header {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.page-title {
    font-family: 'Playfair Display', serif;
    font-size: 3rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.page-subtitle {
    font-size: 1.25rem;
    color: #6c757d;
    margin-bottom: 1rem;
}

.page-description {
    font-size: 1.1rem;
    color: #495057;
    line-height: 1.6;
}

/* Breadcrumb Navigation */
.breadcrumb-nav {
    background-color: #f8f9fa;
    padding: 1rem 0;
    border-bottom: 1px solid #dee2e6;
}

.breadcrumb {
    margin-bottom: 0;
    background: none;
    padding: 0;
}

.breadcrumb-item a {
    color: #6c757d;
    text-decoration: none;
}

.breadcrumb-item a:hover {
    color: #495057;
    text-decoration: underline;
}

.breadcrumb-item.active {
    color: #495057;
}

/* Coin Cards */
.coin-types {
    background-color: #fff;
}

.coin-card {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
}

.coin-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.coin-image {
    height: 200px;
    overflow: hidden;
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    display: flex;
    align-items: center;
    justify-content: center;
}

.coin-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.coin-card:hover .coin-image img {
    transform: scale(1.05);
}

.coin-content {
    padding: 1.5rem;
}

.coin-content h3 {
    font-family: 'Playfair Display', serif;
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.coin-content p {
    color: #6c757d;
    margin-bottom: 1rem;
    line-height: 1.6;
}

.coin-features {
    list-style: none;
    padding: 0;
    margin: 0;
}

.coin-features li {
    padding: 0.25rem 0;
    color: #495057;
    position: relative;
    padding-left: 1.5rem;
}

.coin-features li:before {
    content: '●';
    position: absolute;
    left: 0;
    color: #ffd700;
    font-weight: bold;
}

/* Section Titles */
.section-title {
    font-family: 'Playfair Display', serif;
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.25rem;
    color: #6c757d;
    margin-bottom: 2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-title {
        font-size: 2.5rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .coin-content {
        padding: 1rem;
    }
}

@media (max-width: 576px) {
    .page-header {
        padding: 60px 0;
    }
    
    .page-title {
        font-size: 2rem;
    }
}
