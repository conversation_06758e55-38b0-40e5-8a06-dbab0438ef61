/**
 * About Page Styles - 14K Jewelry & Coin Exchange
 * Specific styles for the about page
 */

/* Page Header */
.page-header {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.page-title {
    font-family: 'Playfair Display', serif;
    font-size: 3rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.page-subtitle {
    font-size: 1.25rem;
    color: #6c757d;
    margin-bottom: 1rem;
}

.page-description {
    font-size: 1.1rem;
    color: #495057;
    line-height: 1.6;
}

/* Breadcrumb Navigation */
.breadcrumb-nav {
    background-color: #f8f9fa;
    padding: 1rem 0;
    border-bottom: 1px solid #dee2e6;
}

.breadcrumb {
    margin-bottom: 0;
    background: none;
    padding: 0;
}

.breadcrumb-item a {
    color: #6c757d;
    text-decoration: none;
}

.breadcrumb-item a:hover {
    color: #495057;
    text-decoration: underline;
}

.breadcrumb-item.active {
    color: #495057;
}

/* Our Story Section */
.our-story {
    background-color: #fff;
}

.our-story .lead {
    font-size: 1.25rem;
    font-weight: 400;
    color: #495057;
    margin-bottom: 2rem;
}

.our-story p {
    font-size: 1.1rem;
    color: #6c757d;
    line-height: 1.8;
    margin-bottom: 1.5rem;
}

/* Values Section */
.our-values {
    background-color: #f8f9fa;
}

.value-card {
    background: #fff;
    padding: 2rem 1.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
}

.value-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.value-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.value-icon i {
    font-size: 2rem;
    color: white;
}

.value-card h4 {
    font-family: 'Playfair Display', serif;
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.value-card p {
    color: #6c757d;
    line-height: 1.6;
    margin: 0;
}

/* Team Section */
.our-team {
    background-color: #fff;
}

.team-card {
    padding: 2rem 1rem;
    transition: transform 0.3s ease;
}

.team-card:hover {
    transform: translateY(-5px);
}

.team-image {
    margin-bottom: 1.5rem;
}

.team-image img {
    width: 150px;
    height: 150px;
    object-fit: cover;
    border: 4px solid #e9ecef;
    transition: border-color 0.3s ease;
}

.team-card:hover .team-image img {
    border-color: #007bff;
}

.team-card h4 {
    font-family: 'Playfair Display', serif;
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.team-title {
    font-weight: 600;
    color: #007bff;
    margin-bottom: 1rem;
    text-transform: uppercase;
    font-size: 0.9rem;
    letter-spacing: 1px;
}

.team-card p:last-child {
    color: #6c757d;
    line-height: 1.6;
    margin: 0;
}

/* Section Titles */
.section-title {
    font-family: 'Playfair Display', serif;
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.25rem;
    color: #6c757d;
    margin-bottom: 2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-title {
        font-size: 2.5rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .value-card {
        padding: 1.5rem 1rem;
        margin-bottom: 2rem;
    }
    
    .team-card {
        padding: 1.5rem 0.5rem;
        margin-bottom: 2rem;
    }
    
    .team-image img {
        width: 120px;
        height: 120px;
    }
}

@media (max-width: 576px) {
    .page-header {
        padding: 60px 0;
    }
    
    .page-title {
        font-size: 2rem;
    }
    
    .our-story .lead {
        font-size: 1.1rem;
    }
    
    .our-story p {
        font-size: 1rem;
    }
    
    .value-icon {
        width: 60px;
        height: 60px;
    }
    
    .value-icon i {
        font-size: 1.5rem;
    }
}
