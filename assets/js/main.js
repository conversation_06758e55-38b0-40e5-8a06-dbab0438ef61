/**
 * 14K Jewelry & Coin Exchange - Main JavaScript
 * Modern ES6+ JavaScript with enhanced functionality
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all components
    initializeNavigation();
    initializeFancybox();
    initializeAnimations();
    initializeForms();
    initializeScrollEffects();
    initializeTooltips();
    initializeLazyLoading();
    initializePriceCalculatorWidget();
});

/**
 * Navigation functionality
 */
function initializeNavigation() {
    const navbar = document.querySelector('.navbar');
    const navbarToggler = document.querySelector('.navbar-toggler');
    const navbarCollapse = document.querySelector('.navbar-collapse');

    // Navbar scroll effect
    window.addEventListener('scroll', function() {
        if (window.scrollY > 100) {
            navbar.classList.add('navbar-scrolled');
        } else {
            navbar.classList.remove('navbar-scrolled');
        }
    });

    // Close mobile menu when clicking on links
    document.querySelectorAll('.navbar-nav .nav-link').forEach(link => {
        link.addEventListener('click', function() {
            if (navbarCollapse.classList.contains('show')) {
                navbarToggler.click();
            }
        });
    });

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

/**
 * Initialize Fancybox for image galleries
 */
function initializeFancybox() {
    if (typeof Fancybox !== 'undefined') {
        Fancybox.bind('[data-fancybox]', {
            Toolbar: {
                display: {
                    left: ['infobar'],
                    middle: [],
                    right: ['slideshow', 'thumbs', 'close']
                }
            },
            Thumbs: {
                autoStart: false
            },
            Images: {
                zoom: true
            },
            Carousel: {
                infinite: false
            }
        });
    }
}

/**
 * Scroll animations
 */
function initializeAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('visible');
            }
        });
    }, observerOptions);

    // Observe elements with animation classes
    document.querySelectorAll('.fade-in, .slide-in-left, .slide-in-right').forEach(el => {
        observer.observe(el);
    });
}

/**
 * Form handling and validation
 */
function initializeForms() {
    // Contact forms
    document.querySelectorAll('form[data-ajax]').forEach(form => {
        form.addEventListener('submit', handleAjaxForm);
    });

    // Newsletter signup
    const newsletterForm = document.querySelector('.newsletter-signup form');
    if (newsletterForm) {
        newsletterForm.addEventListener('submit', handleNewsletterSignup);
    }

    // Form validation
    document.querySelectorAll('input[required], textarea[required]').forEach(field => {
        field.addEventListener('blur', validateField);
        field.addEventListener('input', clearFieldError);
    });
}

/**
 * Handle AJAX form submissions
 */
async function handleAjaxForm(e) {
    e.preventDefault();

    const form = e.target;
    const formData = new FormData(form);
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;

    // Show loading state
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<span class="loading"></span> Sending...';

    try {
        const response = await fetch(form.action, {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (result.success) {
            showNotification('Message sent successfully!', 'success');
            form.reset();
        } else {
            showNotification(result.message || 'An error occurred. Please try again.', 'error');
        }
    } catch (error) {
        showNotification('Network error. Please try again.', 'error');
    } finally {
        submitBtn.disabled = false;
        submitBtn.textContent = originalText;
    }
}

/**
 * Handle newsletter signup
 */
async function handleNewsletterSignup(e) {
    e.preventDefault();

    const form = e.target;
    const email = form.querySelector('input[name="email"]').value;
    const submitBtn = form.querySelector('button[type="submit"]');

    if (!isValidEmail(email)) {
        showNotification('Please enter a valid email address.', 'error');
        return;
    }

    submitBtn.disabled = true;
    submitBtn.innerHTML = '<span class="loading"></span>';

    try {
        const response = await fetch('/api/newsletter-signup.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ email })
        });

        const result = await response.json();

        if (result.success) {
            showNotification('Thank you for subscribing!', 'success');
            form.reset();
        } else {
            showNotification(result.message || 'Subscription failed. Please try again.', 'error');
        }
    } catch (error) {
        showNotification('Network error. Please try again.', 'error');
    } finally {
        submitBtn.disabled = false;
        submitBtn.textContent = 'Subscribe';
    }
}

/**
 * Field validation
 */
function validateField(e) {
    const field = e.target;
    const value = field.value.trim();

    clearFieldError(field);

    if (field.hasAttribute('required') && !value) {
        showFieldError(field, 'This field is required.');
        return false;
    }

    if (field.type === 'email' && value && !isValidEmail(value)) {
        showFieldError(field, 'Please enter a valid email address.');
        return false;
    }

    if (field.type === 'tel' && value && !isValidPhone(value)) {
        showFieldError(field, 'Please enter a valid phone number.');
        return false;
    }

    return true;
}

/**
 * Show field error
 */
function showFieldError(field, message) {
    field.classList.add('is-invalid');

    let errorDiv = field.parentNode.querySelector('.invalid-feedback');
    if (!errorDiv) {
        errorDiv = document.createElement('div');
        errorDiv.className = 'invalid-feedback';
        field.parentNode.appendChild(errorDiv);
    }

    errorDiv.textContent = message;
}

/**
 * Clear field error
 */
function clearFieldError(field) {
    field.classList.remove('is-invalid');
    const errorDiv = field.parentNode.querySelector('.invalid-feedback');
    if (errorDiv) {
        errorDiv.remove();
    }
}

/**
 * Scroll effects
 */
function initializeScrollEffects() {
    // Back to top button
    const backToTopBtn = createBackToTopButton();

    window.addEventListener('scroll', function() {
        if (window.scrollY > 300) {
            backToTopBtn.classList.add('visible');
        } else {
            backToTopBtn.classList.remove('visible');
        }
    });

    // Parallax effect for hero section
    const heroSection = document.querySelector('.hero-section');
    if (heroSection) {
        window.addEventListener('scroll', function() {
            const scrolled = window.pageYOffset;
            const rate = scrolled * -0.5;
            heroSection.style.transform = `translateY(${rate}px)`;
        });
    }
}

/**
 * Create back to top button
 */
function createBackToTopButton() {
    const btn = document.createElement('button');
    btn.className = 'back-to-top';
    btn.innerHTML = '<i class="fas fa-chevron-up"></i>';
    btn.setAttribute('aria-label', 'Back to top');

    btn.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });

    document.body.appendChild(btn);
    return btn;
}

/**
 * Initialize tooltips
 */
function initializeTooltips() {
    if (typeof bootstrap !== 'undefined') {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function(tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
}

/**
 * Lazy loading for images
 */
function initializeLazyLoading() {
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver(function(entries, observer) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });

        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
    }
}

/**
 * Show notification
 */
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <span class="notification-message">${message}</span>
            <button class="notification-close" aria-label="Close">&times;</button>
        </div>
    `;

    document.body.appendChild(notification);

    // Show notification
    setTimeout(() => notification.classList.add('show'), 100);

    // Auto hide after 5 seconds
    setTimeout(() => hideNotification(notification), 5000);

    // Close button functionality
    notification.querySelector('.notification-close').addEventListener('click', () => {
        hideNotification(notification);
    });
}

/**
 * Hide notification
 */
function hideNotification(notification) {
    notification.classList.remove('show');
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 300);
}

/**
 * Utility functions
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function isValidPhone(phone) {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    const cleanPhone = phone.replace(/[^\d+]/g, '');
    return phoneRegex.test(cleanPhone) && cleanPhone.length >= 10;
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

/**
 * Price calculator for precious metals
 */
class PriceCalculator {
    constructor() {
        this.metalPrices = {
            gold: 2000, // per ounce
            silver: 25,  // per ounce
            platinum: 1000 // per ounce
        };

        // Load current prices from API
        this.loadCurrentPrices();
    }

    async loadCurrentPrices() {
        try {
            const response = await fetch('/api/prices.php');
            const data = await response.json();

            if (data.success && data.data) {
                this.metalPrices.gold = data.data.gold.price;
                this.metalPrices.silver = data.data.silver.price;
                this.metalPrices.platinum = data.data.platinum.price;

                // Update any visible price displays
                this.updatePriceDisplays();
            }
        } catch (error) {
            console.warn('Could not load current prices, using defaults:', error);
        }
    }

    updatePriceDisplays() {
        // Update any elements that show current prices
        const priceElements = document.querySelectorAll('[data-metal-price]');
        priceElements.forEach(element => {
            const metal = element.dataset.metalPrice;
            if (this.metalPrices[metal]) {
                element.textContent = this.formatPrice(this.metalPrices[metal]);
            }
        });
    }

    calculateValue(metal, weight, purity) {
        const pricePerOunce = this.metalPrices[metal.toLowerCase()];
        if (!pricePerOunce) return 0;

        const purityDecimal = purity / 100;
        const weightInOunces = weight / 31.1035; // grams to ounces

        return pricePerOunce * weightInOunces * purityDecimal;
    }

    formatPrice(price) {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(price);
    }
}

// Initialize price calculator
window.priceCalculator = new PriceCalculator();

/**
 * Interactive Price Calculator Widget
 */
function initializePriceCalculatorWidget() {
    const calculatorContainer = document.getElementById('price-calculator-widget');
    if (!calculatorContainer) return;

    const calculatorHTML = `
        <div class="price-calculator-widget">
            <h4 class="mb-3">Precious Metals Calculator</h4>
            <div class="row g-3">
                <div class="col-md-6">
                    <label for="metal-type" class="form-label">Metal Type</label>
                    <select id="metal-type" class="form-select">
                        <option value="gold">Gold</option>
                        <option value="silver">Silver</option>
                        <option value="platinum">Platinum</option>
                    </select>
                </div>
                <div class="col-md-6">
                    <label for="metal-purity" class="form-label">Purity (%)</label>
                    <select id="metal-purity" class="form-select">
                        <option value="41.7">10K Gold (41.7%)</option>
                        <option value="58.3" selected>14K Gold (58.3%)</option>
                        <option value="75">18K Gold (75%)</option>
                        <option value="91.7">22K Gold (91.7%)</option>
                        <option value="99.9">24K Gold (99.9%)</option>
                        <option value="92.5">Sterling Silver (92.5%)</option>
                        <option value="99.9">Fine Silver (99.9%)</option>
                        <option value="95">Platinum (95%)</option>
                    </select>
                </div>
                <div class="col-md-6">
                    <label for="metal-weight" class="form-label">Weight (grams)</label>
                    <input type="number" id="metal-weight" class="form-control" placeholder="Enter weight" min="0" step="0.1">
                </div>
                <div class="col-md-6">
                    <label for="estimated-value" class="form-label">Estimated Value</label>
                    <input type="text" id="estimated-value" class="form-control" readonly placeholder="$0.00">
                </div>
            </div>
            <div class="mt-3">
                <small class="text-muted">
                    <i class="fas fa-info-circle"></i>
                    This is an estimate only. Actual prices may vary based on current market conditions and item condition.
                </small>
            </div>
        </div>
    `;

    calculatorContainer.innerHTML = calculatorHTML;

    // Add event listeners
    const metalType = document.getElementById('metal-type');
    const metalPurity = document.getElementById('metal-purity');
    const metalWeight = document.getElementById('metal-weight');
    const estimatedValue = document.getElementById('estimated-value');

    function updatePurityOptions() {
        const metal = metalType.value;
        const puritySelect = metalPurity;

        // Clear existing options
        puritySelect.innerHTML = '';

        if (metal === 'gold') {
            puritySelect.innerHTML = `
                <option value="41.7">10K Gold (41.7%)</option>
                <option value="58.3" selected>14K Gold (58.3%)</option>
                <option value="75">18K Gold (75%)</option>
                <option value="91.7">22K Gold (91.7%)</option>
                <option value="99.9">24K Gold (99.9%)</option>
            `;
        } else if (metal === 'silver') {
            puritySelect.innerHTML = `
                <option value="92.5" selected>Sterling Silver (92.5%)</option>
                <option value="99.9">Fine Silver (99.9%)</option>
                <option value="90">90% Silver Coins</option>
            `;
        } else if (metal === 'platinum') {
            puritySelect.innerHTML = `
                <option value="95" selected>Platinum (95%)</option>
                <option value="90">Platinum (90%)</option>
                <option value="85">Platinum (85%)</option>
            `;
        }

        calculateValue();
    }

    function calculateValue() {
        const metal = metalType.value;
        const purity = parseFloat(metalPurity.value) || 0;
        const weight = parseFloat(metalWeight.value) || 0;

        if (weight > 0 && purity > 0) {
            const value = window.priceCalculator.calculateValue(metal, weight, purity);
            estimatedValue.value = window.priceCalculator.formatPrice(value);
        } else {
            estimatedValue.value = '$0.00';
        }
    }

    // Event listeners
    metalType.addEventListener('change', updatePurityOptions);
    metalPurity.addEventListener('change', calculateValue);
    metalWeight.addEventListener('input', calculateValue);

    // Initialize
    updatePurityOptions();
}

/**
 * Google Maps integration
 */
function initializeMap() {
    if (typeof google !== 'undefined' && google.maps) {
        const mapElement = document.getElementById('google-map');
        if (mapElement) {
            const map = new google.maps.Map(mapElement, {
                center: { lat: 42.2139, lng: -83.1977 },
                zoom: 15,
                styles: [
                    {
                        featureType: 'all',
                        elementType: 'geometry.fill',
                        stylers: [{ color: '#f5f5f5' }]
                    },
                    {
                        featureType: 'water',
                        elementType: 'geometry',
                        stylers: [{ color: '#e9e9e9' }]
                    }
                ]
            });

            const marker = new google.maps.Marker({
                position: { lat: 42.2139, lng: -83.1977 },
                map: map,
                title: '14K Jewelry & Coin Exchange',
                icon: {
                    url: '/assets/images/map-marker.png',
                    scaledSize: new google.maps.Size(40, 40)
                }
            });

            const infoWindow = new google.maps.InfoWindow({
                content: `
                    <div class="map-info-window">
                        <h6>14K Jewelry & Coin Exchange</h6>
                        <p>123 Main Street<br>Southgate, MI 48195</p>
                        <p><strong>Phone:</strong> ${BUSINESS_PHONE}</p>
                        <p><strong>Hours:</strong> ${BUSINESS_HOURS}</p>
                    </div>
                `
            });

            marker.addListener('click', () => {
                infoWindow.open(map, marker);
            });
        }
    }
}

// Load Google Maps if needed
if (document.getElementById('google-map')) {
    window.initializeMap = initializeMap;
}